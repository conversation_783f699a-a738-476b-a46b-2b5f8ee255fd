# Website Clone

This ZIP file contains a complete offline copy of: https://fadb3a50-e6c0-4943-8b28-875a13d3eacd.onlook.live/

## How to View:

### ⚠️ IMPORTANT: Use Local Server for Best Results

**Modern browsers block local file access for security reasons.** For the website to display correctly with all CSS, JavaScript, and images, you MUST use a local HTTP server.

### Option 1: Local HTTP Server (Required for Full Functionality)

**Using Python (Recommended):**
```bash
# Navigate to the extracted folder, then run:
python3 -m http.server 8000
# Or on Windows:
python -m http.server 8000
```

**Using Node.js:**
```bash
npx serve .
# or
npx http-server
```

**Using PHP:**
```bash
php -S localhost:8000
```

Then open: **http://localhost:8000**

### Option 2: Direct File Opening (Limited Functionality)
Opening index.html directly in your browser will show:
- ✅ Basic HTML structure
- ❌ No CSS styling (appears unstyled)
- ❌ No JavaScript functionality
- ❌ CORS errors in console

**This is normal browser security behavior and not a bug.**

## Contents:
- HTML pages from the original website
- CSS stylesheets in assets/css/
- JavaScript files in assets/js/
- Images in assets/images/
- Fonts and other assets in assets/fonts/
- website-content.md - All text content in markdown format for AI analysis

## Note:
Some dynamic functionality may not work in this offline copy.
External links will still point to the original websites.

The website-content.md file contains all extracted text content from the website
in a clean markdown format, perfect for AI tools and content analysis.

Generated on: 2025-09-22T19:20:39.430Z
