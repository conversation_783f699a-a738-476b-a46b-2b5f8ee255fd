var f4=Object.create;var{getPrototypeOf:z4,defineProperty:pe,getOwnPropertyNames:D4}=Object;var _4=Object.prototype.hasOwnProperty;var ah=(r,t,i)=>{i=r!=null?f4(z4(r)):{};let o=t||!r||!r.__esModule?pe(i,"default",{value:r,enumerable:!0}):i;for(let n of D4(r))if(!_4.call(o,n))pe(o,n,{get:()=>r[n],enumerable:!0});return o};var vr=(r,t)=>()=>(t||r((t={exports:{}}).exports,t),t.exports);var a=(r,t)=>{for(var i in t)pe(r,i,{get:t[i],enumerable:!0,configurable:!0,set:(o)=>t[i]=()=>o})};var Ie=vr((N3,Uh)=>{function j4(r){var t=typeof r;return r!=null&&(t=="object"||t=="function")}Uh.exports=j4});var Jh=vr((B3,kh)=>{var O4=typeof global=="object"&&global&&global.Object===Object&&global;kh.exports=O4});var ae=vr((y3,Xh)=>{var p4=Jh(),I4=typeof self=="object"&&self&&self.Object===Object&&self,a4=p4||I4||Function("return this")();Xh.exports=a4});var qh=vr((A3,Ph)=>{var U4=ae(),k4=function(){return U4.Date.now()};Ph.exports=k4});var Kh=vr((H3,Wh)=>{var J4=/\s/;function X4(r){var t=r.length;while(t--&&J4.test(r.charAt(t)));return t}Wh.exports=X4});var Lh=vr((R3,Vh)=>{var P4=Kh(),q4=/^\s+/;function W4(r){return r?r.slice(0,P4(r)+1).replace(q4,""):r}Vh.exports=W4});var Ue=vr((M3,Yh)=>{var K4=ae(),V4=K4.Symbol;Yh.exports=V4});var Gh=vr((Z3,Fh)=>{var Eh=Ue(),Qh=Object.prototype,L4=Qh.hasOwnProperty,Y4=Qh.toString,fn=Eh?Eh.toStringTag:void 0;function E4(r){var t=L4.call(r,fn),i=r[fn];try{r[fn]=void 0;var o=!0}catch(e){}var n=Y4.call(r);if(o)if(t)r[fn]=i;else delete r[fn];return n}Fh.exports=E4});var Nh=vr((C3,Sh)=>{var Q4=Object.prototype,F4=Q4.toString;function G4(r){return F4.call(r)}Sh.exports=G4});var Hh=vr((T3,Ah)=>{var Bh=Ue(),S4=Gh(),N4=Nh(),B4="[object Null]",y4="[object Undefined]",yh=Bh?Bh.toStringTag:void 0;function A4(r){if(r==null)return r===void 0?y4:B4;return yh&&yh in Object(r)?S4(r):N4(r)}Ah.exports=A4});var Mh=vr((d3,Rh)=>{function H4(r){return r!=null&&typeof r=="object"}Rh.exports=H4});var Ch=vr((s3,Zh)=>{var R4=Hh(),M4=Mh(),Z4="[object Symbol]";function C4(r){return typeof r=="symbol"||M4(r)&&R4(r)==Z4}Zh.exports=C4});var r$=vr((rU,sh)=>{var T4=Lh(),Th=Ie(),d4=Ch(),dh=NaN,s4=/^[-+]0x[0-9a-f]+$/i,r6=/^0b[01]+$/i,t6=/^0o[0-7]+$/i,n6=parseInt;function i6(r){if(typeof r=="number")return r;if(d4(r))return dh;if(Th(r)){var t=typeof r.valueOf=="function"?r.valueOf():r;r=Th(t)?t+"":t}if(typeof r!="string")return r===0?r:+r;r=T4(r);var i=r6.test(r);return i||t6.test(r)?n6(r.slice(2),i?2:8):s4.test(r)?dh:+r}sh.exports=i6});var Je=vr((tU,n$)=>{var o6=Ie(),ke=qh(),t$=r$(),e6="Expected a function",l6=Math.max,c6=Math.min;function u6(r,t,i){var o,n,e,l,u,g,c=0,b=!1,v=!1,h=!0;if(typeof r!="function")throw new TypeError(e6);if(t=t$(t)||0,o6(i))b=!!i.leading,v="maxWait"in i,e=v?l6(t$(i.maxWait)||0,t):e,h="trailing"in i?!!i.trailing:h;function m(Q){var B=o,tr=n;return o=n=void 0,c=Q,l=r.apply(tr,B),l}function w(Q){return c=Q,u=setTimeout(_,t),b?m(Q):l}function O(Q){var B=Q-g,tr=Q-c,Oe=t-B;return v?c6(Oe,e-tr):Oe}function I(Q){var B=Q-g,tr=Q-c;return g===void 0||B>=t||B<0||v&&tr>=e}function _(){var Q=ke();if(I(Q))return J(Q);u=setTimeout(_,O(Q))}function J(Q){if(u=void 0,h&&o)return m(Q);return o=n=void 0,l}function L(){if(u!==void 0)clearTimeout(u);c=0,o=g=n=u=void 0}function P(){return u===void 0?l:J(ke())}function W(){var Q=ke(),B=I(Q);if(o=arguments,n=this,g=Q,B){if(u===void 0)return w(g);if(v)return clearTimeout(u),u=setTimeout(_,t),m(g)}if(u===void 0)u=setTimeout(_,t);return l}return W.cancel=L,W.flush=P,W}n$.exports=u6});var i0=vr((ez)=>{var n0="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/".split("");ez.encode=function(r){if(0<=r&&r<n0.length)return n0[r];throw new TypeError("Must be between 0 and 63: "+r)};ez.decode=function(r){var t=65,i=90,o=97,n=122,e=48,l=57,u=43,g=47,c=26,b=52;if(t<=r&&r<=i)return r-t;if(o<=r&&r<=n)return r-o+c;if(e<=r&&r<=l)return r-e+b;if(r==u)return 62;if(r==g)return 63;return-1}});var u0=vr((bz)=>{var o0=i0(),Be=5,e0=1<<Be,l0=e0-1,c0=e0;function uz(r){return r<0?(-r<<1)+1:(r<<1)+0}function gz(r){var t=(r&1)===1,i=r>>1;return t?-i:i}bz.encode=function r(t){var i="",o,n=uz(t);do{if(o=n&l0,n>>>=Be,n>0)o|=c0;i+=o0.encode(o)}while(n>0);return i};bz.decode=function r(t,i,o){var n=t.length,e=0,l=0,u,g;do{if(i>=n)throw new Error("Expected more digits in base 64 VLQ value.");if(g=o0.decode(t.charCodeAt(i++)),g===-1)throw new Error("Invalid base64 digit: "+t.charAt(i-1));u=!!(g&c0),g&=l0,e=e+(g<<l),l+=Be}while(u);o.value=gz(e),o.rest=i}});var yi=vr((kz)=>{function hz(r,t,i){if(t in r)return r[t];else if(arguments.length===3)return i;else throw new Error('"'+t+'" is a required argument.')}kz.getArg=hz;var g0=/^(?:([\w+\-.]+):)?\/\/(?:(\w+:\w+)@)?([\w.-]*)(?::(\d+))?(.*)$/,$z=/^data:.+\,.+$/;function an(r){var t=r.match(g0);if(!t)return null;return{scheme:t[1],auth:t[2],host:t[3],port:t[4],path:t[5]}}kz.urlParse=an;function At(r){var t="";if(r.scheme)t+=r.scheme+":";if(t+="//",r.auth)t+=r.auth+"@";if(r.host)t+=r.host;if(r.port)t+=":"+r.port;if(r.path)t+=r.path;return t}kz.urlGenerate=At;var xz=32;function wz(r){var t=[];return function(i){for(var o=0;o<t.length;o++)if(t[o].input===i){var n=t[0];return t[0]=t[o],t[o]=n,t[0].result}var e=r(i);if(t.unshift({input:i,result:e}),t.length>xz)t.pop();return e}}var ye=wz(function r(t){var i=t,o=an(t);if(o){if(!o.path)return t;i=o.path}var n=kz.isAbsolute(i),e=[],l=0,u=0;while(!0)if(l=u,u=i.indexOf("/",l),u===-1){e.push(i.slice(l));break}else{e.push(i.slice(l,u));while(u<i.length&&i[u]==="/")u++}for(var g,c=0,u=e.length-1;u>=0;u--)if(g=e[u],g===".")e.splice(u,1);else if(g==="..")c++;else if(c>0)if(g==="")e.splice(u+1,c),c=0;else e.splice(u,2),c--;if(i=e.join("/"),i==="")i=n?"/":".";if(o)return o.path=i,At(o);return i});kz.normalize=ye;function b0(r,t){if(r==="")r=".";if(t==="")t=".";var i=an(t),o=an(r);if(o)r=o.path||"/";if(i&&!i.scheme){if(o)i.scheme=o.scheme;return At(i)}if(i||t.match($z))return t;if(o&&!o.host&&!o.path)return o.host=t,At(o);var n=t.charAt(0)==="/"?t:ye(r.replace(/\/+$/,"")+"/"+t);if(o)return o.path=n,At(o);return n}kz.join=b0;kz.isAbsolute=function(r){return r.charAt(0)==="/"||g0.test(r)};function fz(r,t){if(r==="")r=".";r=r.replace(/\/$/,"");var i=0;while(t.indexOf(r+"/")!==0){var o=r.lastIndexOf("/");if(o<0)return t;if(r=r.slice(0,o),r.match(/^([^\/]+:\/)?\/*$/))return t;++i}return Array(i+1).join("../")+t.substr(r.length+1)}kz.relative=fz;var m0=function(){var r=Object.create(null);return!("__proto__"in r)}();function v0(r){return r}function zz(r){if(h0(r))return"$"+r;return r}kz.toSetString=m0?v0:zz;function Dz(r){if(h0(r))return r.slice(1);return r}kz.fromSetString=m0?v0:Dz;function h0(r){if(!r)return!1;var t=r.length;if(t<9)return!1;if(r.charCodeAt(t-1)!==95||r.charCodeAt(t-2)!==95||r.charCodeAt(t-3)!==111||r.charCodeAt(t-4)!==116||r.charCodeAt(t-5)!==111||r.charCodeAt(t-6)!==114||r.charCodeAt(t-7)!==112||r.charCodeAt(t-8)!==95||r.charCodeAt(t-9)!==95)return!1;for(var i=t-10;i>=0;i--)if(r.charCodeAt(i)!==36)return!1;return!0}function _z(r,t,i){var o=tt(r.source,t.source);if(o!==0)return o;if(o=r.originalLine-t.originalLine,o!==0)return o;if(o=r.originalColumn-t.originalColumn,o!==0||i)return o;if(o=r.generatedColumn-t.generatedColumn,o!==0)return o;if(o=r.generatedLine-t.generatedLine,o!==0)return o;return tt(r.name,t.name)}kz.compareByOriginalPositions=_z;function jz(r,t,i){var o=r.originalLine-t.originalLine;if(o!==0)return o;if(o=r.originalColumn-t.originalColumn,o!==0||i)return o;if(o=r.generatedColumn-t.generatedColumn,o!==0)return o;if(o=r.generatedLine-t.generatedLine,o!==0)return o;return tt(r.name,t.name)}kz.compareByOriginalPositionsNoSource=jz;function Oz(r,t,i){var o=r.generatedLine-t.generatedLine;if(o!==0)return o;if(o=r.generatedColumn-t.generatedColumn,o!==0||i)return o;if(o=tt(r.source,t.source),o!==0)return o;if(o=r.originalLine-t.originalLine,o!==0)return o;if(o=r.originalColumn-t.originalColumn,o!==0)return o;return tt(r.name,t.name)}kz.compareByGeneratedPositionsDeflated=Oz;function pz(r,t,i){var o=r.generatedColumn-t.generatedColumn;if(o!==0||i)return o;if(o=tt(r.source,t.source),o!==0)return o;if(o=r.originalLine-t.originalLine,o!==0)return o;if(o=r.originalColumn-t.originalColumn,o!==0)return o;return tt(r.name,t.name)}kz.compareByGeneratedPositionsDeflatedNoLine=pz;function tt(r,t){if(r===t)return 0;if(r===null)return 1;if(t===null)return-1;if(r>t)return 1;return-1}function Iz(r,t){var i=r.generatedLine-t.generatedLine;if(i!==0)return i;if(i=r.generatedColumn-t.generatedColumn,i!==0)return i;if(i=tt(r.source,t.source),i!==0)return i;if(i=r.originalLine-t.originalLine,i!==0)return i;if(i=r.originalColumn-t.originalColumn,i!==0)return i;return tt(r.name,t.name)}kz.compareByGeneratedPositionsInflated=Iz;function az(r){return JSON.parse(r.replace(/^\)]}'[^\n]*\n/,""))}kz.parseSourceMapInput=az;function Uz(r,t,i){if(t=t||"",r){if(r[r.length-1]!=="/"&&t[0]!=="/")r+="/";t=r+t}if(i){var o=an(i);if(!o)throw new Error("sourceMapURL could not be parsed");if(o.path){var n=o.path.lastIndexOf("/");if(n>=0)o.path=o.path.substring(0,n+1)}t=b0(At(o),t)}return ye(t)}kz.computeSourceURL=Uz});var $0=vr((yz)=>{var Ae=yi(),He=Object.prototype.hasOwnProperty,Ut=typeof Map!=="undefined";function nt(){this._array=[],this._set=Ut?new Map:Object.create(null)}nt.fromArray=function r(t,i){var o=new nt;for(var n=0,e=t.length;n<e;n++)o.add(t[n],i);return o};nt.prototype.size=function r(){return Ut?this._set.size:Object.getOwnPropertyNames(this._set).length};nt.prototype.add=function r(t,i){var o=Ut?t:Ae.toSetString(t),n=Ut?this.has(t):He.call(this._set,o),e=this._array.length;if(!n||i)this._array.push(t);if(!n)if(Ut)this._set.set(t,e);else this._set[o]=e};nt.prototype.has=function r(t){if(Ut)return this._set.has(t);else{var i=Ae.toSetString(t);return He.call(this._set,i)}};nt.prototype.indexOf=function r(t){if(Ut){var i=this._set.get(t);if(i>=0)return i}else{var o=Ae.toSetString(t);if(He.call(this._set,o))return this._set[o]}throw new Error('"'+t+'" is not in the set.')};nt.prototype.at=function r(t){if(t>=0&&t<this._array.length)return this._array[t];throw new Error("No element indexed by "+t)};nt.prototype.toArray=function r(){return this._array.slice()};yz.ArraySet=nt});var w0=vr((Rz)=>{var x0=yi();function Hz(r,t){var i=r.generatedLine,o=t.generatedLine,n=r.generatedColumn,e=t.generatedColumn;return o>i||o==i&&e>=n||x0.compareByGeneratedPositionsInflated(r,t)<=0}function Ai(){this._array=[],this._sorted=!0,this._last={generatedLine:-1,generatedColumn:0}}Ai.prototype.unsortedForEach=function r(t,i){this._array.forEach(t,i)};Ai.prototype.add=function r(t){if(Hz(this._last,t))this._last=t,this._array.push(t);else this._sorted=!1,this._array.push(t)};Ai.prototype.toArray=function r(){if(!this._sorted)this._array.sort(x0.compareByGeneratedPositionsInflated),this._sorted=!0;return this._array};Rz.MappingList=Ai});var Lt="PENPAL_CHILD";var x4=ah(Je(),1);var g6=class extends Error{code;constructor(r,t){super(t);this.name="PenpalError",this.code=r}},_r=g6,b6=(r)=>({name:r.name,message:r.message,stack:r.stack,penpalCode:r instanceof _r?r.code:void 0}),m6=({name:r,message:t,stack:i,penpalCode:o})=>{let n=o?new _r(o,t):new Error(t);return n.name=r,n.stack=i,n},v6=Symbol("Reply"),h6=class{value;transferables;#r=v6;constructor(r,t){this.value=r,this.transferables=t?.transferables}},$6=h6,Jr="penpal",ki=(r)=>{return typeof r==="object"&&r!==null},c$=(r)=>{return typeof r==="function"},x6=(r)=>{return ki(r)&&r.namespace===Jr},Yt=(r)=>{return r.type==="SYN"},Ji=(r)=>{return r.type==="ACK1"},zn=(r)=>{return r.type==="ACK2"},u$=(r)=>{return r.type==="CALL"},g$=(r)=>{return r.type==="REPLY"},w6=(r)=>{return r.type==="DESTROY"},b$=(r,t=[])=>{let i=[];for(let o of Object.keys(r)){let n=r[o];if(c$(n))i.push([...t,o]);else if(ki(n))i.push(...b$(n,[...t,o]))}return i},f6=(r,t)=>{let i=r.reduce((o,n)=>{return ki(o)?o[n]:void 0},t);return c$(i)?i:void 0},bt=(r)=>{return r.join(".")},i$=(r,t,i)=>({namespace:Jr,channel:r,type:"REPLY",callId:t,isError:!0,...i instanceof Error?{value:b6(i),isSerializedErrorInstance:!0}:{value:i}}),z6=(r,t,i,o)=>{let n=!1,e=async(l)=>{if(n)return;if(!u$(l))return;o?.(`Received ${bt(l.methodPath)}() call`,l);let{methodPath:u,args:g,id:c}=l,b,v;try{let h=f6(u,t);if(!h)throw new _r("METHOD_NOT_FOUND",`Method \`${bt(u)}\` is not found.`);let m=await h(...g);if(m instanceof $6)v=m.transferables,m=await m.value;b={namespace:Jr,channel:i,type:"REPLY",callId:c,value:m}}catch(h){b=i$(i,c,h)}if(n)return;try{o?.(`Sending ${bt(u)}() reply`,b),r.sendMessage(b,v)}catch(h){if(h.name==="DataCloneError")b=i$(i,c,h),o?.(`Sending ${bt(u)}() reply`,b),r.sendMessage(b);throw h}};return r.addMessageHandler(e),()=>{n=!0,r.removeMessageHandler(e)}},D6=z6,m$=crypto.randomUUID?.bind(crypto)??(()=>new Array(4).fill(0).map(()=>Math.floor(Math.random()*Number.MAX_SAFE_INTEGER).toString(16)).join("-")),_6=Symbol("CallOptions"),j6=class{transferables;timeout;#r=_6;constructor(r){this.transferables=r?.transferables,this.timeout=r?.timeout}},O6=j6,p6=new Set(["apply","call","bind"]),v$=(r,t,i=[])=>{return new Proxy(i.length?()=>{}:Object.create(null),{get(o,n){if(n==="then")return;if(i.length&&p6.has(n))return Reflect.get(o,n);return v$(r,t,[...i,n])},apply(o,n,e){return r(i,e)}})},o$=(r)=>{return new _r("CONNECTION_DESTROYED",`Method call ${bt(r)}() failed due to destroyed connection`)},I6=(r,t,i)=>{let o=!1,n=new Map,e=(g)=>{if(!g$(g))return;let{callId:c,value:b,isError:v,isSerializedErrorInstance:h}=g,m=n.get(c);if(!m)return;if(n.delete(c),i?.(`Received ${bt(m.methodPath)}() call`,g),v)m.reject(h?m6(b):b);else m.resolve(b)};return r.addMessageHandler(e),{remoteProxy:v$((g,c)=>{if(o)throw o$(g);let b=m$(),v=c[c.length-1],h=v instanceof O6,{timeout:m,transferables:w}=h?v:{},O=h?c.slice(0,-1):c;return new Promise((I,_)=>{let J=m!==void 0?window.setTimeout(()=>{n.delete(b),_(new _r("METHOD_CALL_TIMEOUT",`Method call ${bt(g)}() timed out after ${m}ms`))},m):void 0;n.set(b,{methodPath:g,resolve:I,reject:_,timeoutId:J});try{let L={namespace:Jr,channel:t,type:"CALL",id:b,methodPath:g,args:O};i?.(`Sending ${bt(g)}() call`,L),r.sendMessage(L,w)}catch(L){_(new _r("TRANSMISSION_FAILED",L.message))}})},i),destroy:()=>{o=!0,r.removeMessageHandler(e);for(let{methodPath:g,reject:c,timeoutId:b}of n.values())clearTimeout(b),c(o$(g));n.clear()}}},a6=I6,U6=()=>{let r,t;return{promise:new Promise((o,n)=>{r=o,t=n}),resolve:r,reject:t}},k6=U6,J6=class extends Error{constructor(r){super(`You've hit a bug in Penpal. Please file an issue with the following information: ${r}`)}},Et=J6,Xe="deprecated-penpal",X6=(r)=>{return ki(r)&&"penpal"in r},P6=(r)=>r.split("."),e$=(r)=>r.join("."),h$=(r)=>{return new Et(`Unexpected message to translate: ${JSON.stringify(r)}`)},q6=(r)=>{if(r.penpal==="syn")return{namespace:Jr,channel:void 0,type:"SYN",participantId:Xe};if(r.penpal==="ack")return{namespace:Jr,channel:void 0,type:"ACK2"};if(r.penpal==="call")return{namespace:Jr,channel:void 0,type:"CALL",id:r.id,methodPath:P6(r.methodName),args:r.args};if(r.penpal==="reply")if(r.resolution==="fulfilled")return{namespace:Jr,channel:void 0,type:"REPLY",callId:r.id,value:r.returnValue};else return{namespace:Jr,channel:void 0,type:"REPLY",callId:r.id,isError:!0,...r.returnValueIsError?{value:r.returnValue,isSerializedErrorInstance:!0}:{value:r.returnValue}};throw h$(r)},W6=(r)=>{if(Ji(r))return{penpal:"synAck",methodNames:r.methodPaths.map(e$)};if(u$(r))return{penpal:"call",id:r.id,methodName:e$(r.methodPath),args:r.args};if(g$(r))if(r.isError)return{penpal:"reply",id:r.callId,resolution:"rejected",...r.isSerializedErrorInstance?{returnValue:r.value,returnValueIsError:!0}:{returnValue:r.value}};else return{penpal:"reply",id:r.callId,resolution:"fulfilled",returnValue:r.value};throw h$(r)},K6=({messenger:r,methods:t,timeout:i,channel:o,log:n})=>{let e=m$(),l,u=[],g=!1,c=b$(t),{promise:b,resolve:v,reject:h}=k6(),m=i!==void 0?setTimeout(()=>{h(new _r("CONNECTION_TIMEOUT",`Connection timed out after ${i}ms`))},i):void 0,w=()=>{for(let W of u)W()},O=()=>{if(g)return;u.push(D6(r,t,o,n));let{remoteProxy:W,destroy:Q}=a6(r,o,n);u.push(Q),clearTimeout(m),g=!0,v({remoteProxy:W,destroy:w})},I=()=>{let W={namespace:Jr,type:"SYN",channel:o,participantId:e};n?.("Sending handshake SYN",W);try{r.sendMessage(W)}catch(Q){h(new _r("TRANSMISSION_FAILED",Q.message))}},_=(W)=>{if(n?.("Received handshake SYN",W),W.participantId===l&&l!==Xe)return;if(l=W.participantId,I(),!(e>l||l===Xe))return;let B={namespace:Jr,channel:o,type:"ACK1",methodPaths:c};n?.("Sending handshake ACK1",B);try{r.sendMessage(B)}catch(tr){h(new _r("TRANSMISSION_FAILED",tr.message));return}},J=(W)=>{n?.("Received handshake ACK1",W);let Q={namespace:Jr,channel:o,type:"ACK2"};n?.("Sending handshake ACK2",Q);try{r.sendMessage(Q)}catch(B){h(new _r("TRANSMISSION_FAILED",B.message));return}O()},L=(W)=>{n?.("Received handshake ACK2",W),O()},P=(W)=>{if(Yt(W))_(W);if(Ji(W))J(W);if(zn(W))L(W)};return r.addMessageHandler(P),u.push(()=>r.removeMessageHandler(P)),I(),b},V6=K6,L6=(r)=>{let t=!1,i;return(...o)=>{if(!t)t=!0,i=r(...o);return i}},Y6=L6,l$=new WeakSet,E6=({messenger:r,methods:t={},timeout:i,channel:o,log:n})=>{if(!r)throw new _r("INVALID_ARGUMENT","messenger must be defined");if(l$.has(r))throw new _r("INVALID_ARGUMENT","A messenger can only be used for a single connection");l$.add(r);let e=[r.destroy],l=Y6((c)=>{if(c){let b={namespace:Jr,channel:o,type:"DESTROY"};try{r.sendMessage(b)}catch(v){}}for(let b of e)b();n?.("Connection destroyed")}),u=(c)=>{return x6(c)&&c.channel===o};return{promise:(async()=>{try{r.initialize({log:n,validateReceivedMessage:u}),r.addMessageHandler((v)=>{if(w6(v))l(!1)});let{remoteProxy:c,destroy:b}=await V6({messenger:r,methods:t,timeout:i,channel:o,log:n});return e.push(b),c}catch(c){throw l(!0),c}})(),destroy:()=>{l(!0)}}},$$=E6,Q6=class{#r;#o;#n;#t;#l;#i=new Set;#e;#c=!1;constructor({remoteWindow:r,allowedOrigins:t}){if(!r)throw new _r("INVALID_ARGUMENT","remoteWindow must be defined");this.#r=r,this.#o=t?.length?t:[window.origin]}initialize=({log:r,validateReceivedMessage:t})=>{this.#n=r,this.#t=t,window.addEventListener("message",this.#m)};sendMessage=(r,t)=>{if(Yt(r)){let i=this.#u(r);this.#r.postMessage(r,{targetOrigin:i,transfer:t});return}if(Ji(r)||this.#c){let i=this.#c?W6(r):r,o=this.#u(r);this.#r.postMessage(i,{targetOrigin:o,transfer:t});return}if(zn(r)){let{port1:i,port2:o}=new MessageChannel;this.#e=i,i.addEventListener("message",this.#g),i.start();let n=[o,...t||[]],e=this.#u(r);this.#r.postMessage(r,{targetOrigin:e,transfer:n});return}if(this.#e){this.#e.postMessage(r,{transfer:t});return}throw new Et("Port is undefined")};addMessageHandler=(r)=>{this.#i.add(r)};removeMessageHandler=(r)=>{this.#i.delete(r)};destroy=()=>{window.removeEventListener("message",this.#m),this.#b(),this.#i.clear()};#v=(r)=>{return this.#o.some((t)=>t instanceof RegExp?t.test(r):t===r||t==="*")};#u=(r)=>{if(Yt(r))return"*";if(!this.#l)throw new Et("Concrete remote origin not set");return this.#l==="null"&&this.#o.includes("*")?"*":this.#l};#b=()=>{this.#e?.removeEventListener("message",this.#g),this.#e?.close(),this.#e=void 0};#m=({source:r,origin:t,ports:i,data:o})=>{if(r!==this.#r)return;if(X6(o))this.#n?.("Please upgrade the child window to the latest version of Penpal."),this.#c=!0,o=q6(o);if(!this.#t?.(o))return;if(!this.#v(t)){this.#n?.(`Received a message from origin \`${t}\` which did not match allowed origins \`[${this.#o.join(", ")}]\``);return}if(Yt(o))this.#b(),this.#l=t;if(zn(o)&&!this.#c){if(this.#e=i[0],!this.#e)throw new Et("No port received on ACK2");this.#e.addEventListener("message",this.#g),this.#e.start()}for(let n of this.#i)n(o)};#g=({data:r})=>{if(!this.#t?.(r))return;for(let t of this.#i)t(r)}},x$=Q6,nU=class{#r;#o;#n=new Set;#t;constructor({worker:r}){if(!r)throw new _r("INVALID_ARGUMENT","worker must be defined");this.#r=r}initialize=({validateReceivedMessage:r})=>{this.#o=r,this.#r.addEventListener("message",this.#i)};sendMessage=(r,t)=>{if(Yt(r)||Ji(r)){this.#r.postMessage(r,{transfer:t});return}if(zn(r)){let{port1:i,port2:o}=new MessageChannel;this.#t=i,i.addEventListener("message",this.#i),i.start(),this.#r.postMessage(r,{transfer:[o,...t||[]]});return}if(this.#t){this.#t.postMessage(r,{transfer:t});return}throw new Et("Port is undefined")};addMessageHandler=(r)=>{this.#n.add(r)};removeMessageHandler=(r)=>{this.#n.delete(r)};destroy=()=>{this.#r.removeEventListener("message",this.#i),this.#l(),this.#n.clear()};#l=()=>{this.#t?.removeEventListener("message",this.#i),this.#t?.close(),this.#t=void 0};#i=({ports:r,data:t})=>{if(!this.#o?.(t))return;if(Yt(t))this.#l();if(zn(t)){if(this.#t=r[0],!this.#t)throw new Et("No port received on ACK2");this.#t.addEventListener("message",this.#i),this.#t.start()}for(let i of this.#n)i(t)}};var iU=class{#r;#o;#n=new Set;constructor({port:r}){if(!r)throw new _r("INVALID_ARGUMENT","port must be defined");this.#r=r}initialize=({validateReceivedMessage:r})=>{this.#o=r,this.#r.addEventListener("message",this.#t),this.#r.start()};sendMessage=(r,t)=>{this.#r?.postMessage(r,{transfer:t})};addMessageHandler=(r)=>{this.#n.add(r)};removeMessageHandler=(r)=>{this.#n.delete(r)};destroy=()=>{this.#r.removeEventListener("message",this.#t),this.#r.close(),this.#n.clear()};#t=({data:r})=>{if(!this.#o?.(r))return;for(let t of this.#n)t(r)}};var w$=["SCRIPT","STYLE","LINK","META","NOSCRIPT"],f$=new Set(["a","abbr","area","audio","b","bdi","bdo","br","button","canvas","cite","code","data","datalist","del","dfn","em","embed","h1","h2","h3","h4","h5","h6","i","iframe","img","input","ins","kbd","label","li","map","mark","meter","noscript","object","output","p","picture","progress","q","ruby","s","samp","script","select","slot","small","span","strong","sub","sup","svg","template","textarea","time","u","var","video","wbr"]);var Pe=".next-prod";var _U={SCALE:0.7,PAN_POSITION:{x:175,y:100},URL:"http://localhost:3000/",ASPECT_RATIO_LOCKED:!1,DEVICE:"Custom:Custom",THEME:"system",ORIENTATION:"Portrait",MIN_DIMENSIONS:{width:"280px",height:"360px"},COMMANDS:{run:"bun run dev",build:"bun run build",install:"bun install"},IMAGE_FOLDER:"public",IMAGE_DIMENSION:{width:"100px",height:"100px"},FONT_FOLDER:"fonts",FONT_CONFIG:"app/fonts.ts",TAILWIND_CONFIG:"tailwind.config.ts",CHAT_SETTINGS:{showSuggestions:!0,autoApplyCode:!0,expandCodeBlocks:!1,showMiniChat:!0,maxImages:5},EDITOR_SETTINGS:{shouldWarnDelete:!1,enableBunReplace:!0,buildFlags:"--no-lint"}};var qe=["node_modules","dist","build",".git",".next"],pU=[...qe,"static","out",Pe],IU=[...qe,Pe],aU=[...qe,"coverage"],F6=[".jsx",".tsx"],G6=[".js",".ts",".mjs",".cjs"],UU=[...F6,...G6];var XU={["en"]:"English",["ja"]:"日本語",["zh"]:"中文",["ko"]:"한국어"};var j$=ah(Je(),1);function S(r){return document.querySelector(`[${"data-odid"}="${r}"]`)}function We(r,t=!1){let i=`[${"data-odid"}="${r}"]`;if(!t)return i;return S6(i)}function S6(r){return CSS.escape(r)}function Ot(r){return r&&r instanceof Node&&r.nodeType===Node.ELEMENT_NODE&&!w$.includes(r.tagName)&&!r.hasAttribute("data-onlook-ignore")&&r.style.display!=="none"}var N6="useandom-26T198340PX75pxJACKVERYMINDBUSHWOLF_GQZbfghjklqvwyzrict";var z$=(r=21)=>{let t="",i=r|0;while(i--)t+=N6[Math.random()*64|0];return t};function Xr(r){let t=r.getAttribute("data-odid");if(!t)t=`odid-${z$()}`,r.setAttribute("data-odid",t);return t}function Sr(r){return r.getAttribute("data-oid")}function Nr(r){return r.getAttribute("data-oiid")}function D$(r,t){if(!jr)return;jr.onDomProcessed({layerMap:Object.fromEntries(r),rootNode:t}).catch((i)=>{console.error("Failed to send DOM processed event:",i)})}function Ke(r){window._onlookFrameId=r}function Qt(){let r=window._onlookFrameId;if(!r)return console.warn("Frame id not found"),jr?.getFrameId().then((t)=>{Ke(t)}),"";return r}function B6(r=document.body){if(!Qt())return console.warn("frameView id not found, skipping dom processing"),null;let i=zr(r);if(!i)return console.warn("Error building layer tree, root element is null"),null;let o=r.getAttribute("data-odid");if(!o)return console.warn("Root dom id not found"),null;let n=i.get(o);if(!n)return console.warn("Root node not found"),null;return D$(i,n),{rootDomId:o,layerMap:Array.from(i.entries())}}var Xi=j$.default(B6,500),y6=[(r)=>{let t=r.parentElement;return t&&t.tagName.toLowerCase()==="svg"},(r)=>{return r.tagName.toLowerCase()==="next-route-announcer"},(r)=>{return r.tagName.toLowerCase()==="nextjs-portal"}];function zr(r){if(!Ot(r))return null;let t=new Map,i=document.createTreeWalker(r,NodeFilter.SHOW_ELEMENT,{acceptNode:(e)=>{let l=e;if(y6.some((u)=>u(l)))return NodeFilter.FILTER_REJECT;return Ot(l)?NodeFilter.FILTER_ACCEPT:NodeFilter.FILTER_SKIP}}),o=_$(r);o.children=[],t.set(o.domId,o);let n=i.nextNode();while(n){let e=_$(n);e.children=[];let l=n.parentElement;if(l){let u=l.getAttribute("data-odid");if(u){e.parent=u;let g=t.get(u);if(g&&g.children)g.children.push(e.domId)}}t.set(e.domId,e),n=i.nextNode()}return t}function _$(r){let t=Xr(r),i=Sr(r),o=Nr(r),n=Array.from(r.childNodes).map((g)=>g.nodeType===Node.TEXT_NODE?g.textContent:"").join(" ").trim().slice(0,500),e=window.getComputedStyle(r),l=r.getAttribute("data-ocname");return{domId:t,oid:i||null,instanceId:o||null,textContent:n||"",tagName:r.tagName.toLowerCase(),isVisible:e.visibility!=="hidden",component:l||null,frameId:Qt(),children:null,parent:null,dynamicType:null,coreElementType:null}}function Ve(r){throw new Error(`Expected \`never\`, found: ${JSON.stringify(r)}`)}var O$=(r)=>JSON.parse(JSON.stringify(r));function p$(r){let t=a$(r),i=A6(r),o=H6(r);return{defined:{width:"auto",height:"auto",...i,...o},computed:t}}function I$(r){let t=S(r);if(!t)return{};return a$(t)}function a$(r){return O$(window.getComputedStyle(r))}function A6(r){let t={},i=U$(r.style.cssText);return Object.entries(i).forEach(([o,n])=>{t[o]=n}),t}function H6(r){let t={},i=document.styleSheets;for(let o=0;o<i.length;o++){let n,e=i[o];try{if(!e){console.warn("Sheet is undefined");continue}n=Array.from(e.cssRules)||e.rules}catch(l){console.warn("Can't read the css rules of: "+e?.href,l);continue}for(let l=0;l<n.length;l++)try{let u=n[l];if(u&&r.matches(u.selectorText)){let g=U$(u.style.cssText);Object.entries(g).forEach(([c,b])=>t[c]=b)}}catch(u){console.warn("Error",u)}}return t}function U$(r){let t={};return r.split(";").forEach((i)=>{if(i=i.trim(),!i)return;let[o,...n]=i.split(":");t[o?.trim()??""]=n.join(":").trim()}),t}var k$=(r,t)=>{let i=document.elementFromPoint(r,t);if(!i)return;let o=(e)=>{if(e?.shadowRoot){let l=e.shadowRoot.elementFromPoint(r,t);if(l==e)return e;else if(l?.shadowRoot)return o(l);else return l||e}else return e};return o(i)||i},lr=(r,t)=>{let i=r.parentElement,o=i?{domId:i.getAttribute("data-odid"),frameId:Qt(),oid:i.getAttribute("data-oid"),instanceId:i.getAttribute("data-oiid"),rect:i.getBoundingClientRect()}:null,n=r.getBoundingClientRect(),e=t?p$(r):null;return{domId:r.getAttribute("data-odid"),oid:r.getAttribute("data-oid"),frameId:Qt(),instanceId:r.getAttribute("data-oiid"),rect:n,tagName:r.tagName,parent:o,styles:e}};function Pi(r){try{let t=r.getAttribute("data-onlook-drag-saved-style");if(t){let i=JSON.parse(t);for(let o in i)r.style[o]=i[o]}}catch(t){console.warn("Error restoring style",t)}}function J$(r){let t=r.parentElement;if(!t)return;return{type:"index",targetDomId:t.getAttribute("data-odid"),targetOid:Nr(t)||Sr(t)||null,index:Array.from(r.parentElement?.children||[]).indexOf(r),originalIndex:Array.from(r.parentElement?.children||[]).indexOf(r)}}var X$=(r)=>{let t=Array.from(r.childNodes).filter((i)=>i.nodeType===Node.TEXT_NODE).map((i)=>i.textContent);if(t.length===0)return;return t.join("")};var qi=(r,t)=>{let i=S(r)||document.body;return lr(i,t)},P$=(r,t,i)=>{let o=R6(r,t)||document.body;return lr(o,i)},R6=(r,t)=>{let i=document.elementFromPoint(r,t);if(!i)return;let o=(e)=>{if(e?.shadowRoot){let l=e.shadowRoot.elementFromPoint(r,t);if(l==e)return e;else if(l?.shadowRoot)return o(l);else return l||e}else return e};return o(i)||i},q$=(r,t,i)=>{let o=S(r);if(!o){console.warn("Failed to updateElementInstanceId: Element not found");return}o.setAttribute("data-oiid",t),o.setAttribute("data-ocname",i)},W$=(r)=>{let t=S(r);if(!t?.parentElement)return null;return lr(t.parentElement,!1)},K$=(r)=>{let t=S(r);if(!t)return 0;return t.children.length},V$=(r)=>{let t=S(r);if(!t)return null;return lr(t.offsetParent,!1)};function L$(r,t,i){let o=S(r.domId);if(!o)return console.warn("Failed to find parent element",r.domId),null;let n=M6(t),e=new Set(i.map((c)=>c.domId)),l=Array.from(o.children).map((c,b)=>({element:c,index:b,domId:Xr(c)})).filter(({domId:c})=>e.has(c));if(l.length===0)return console.warn("No valid children found to group"),null;let u=Math.min(...l.map((c)=>c.index));return o.insertBefore(n,o.children[u]??null),l.forEach(({element:c})=>{let b=c.cloneNode(!0);b.setAttribute("data-onlook-inserted","true"),n.appendChild(b),c.style.display="none",E$(c)}),{domEl:lr(n,!0),newMap:zr(n)}}function Y$(r,t){let i=S(r.domId);if(!i)return console.warn(`Parent element not found: ${r.domId}`),null;let o;if(t.domId)o=S(t.domId);else return console.warn("Container domId is required for ungrouping"),null;if(!o)return console.warn("Container element not found for ungrouping"),null;return Array.from(o.children).forEach((l)=>{i.appendChild(l)}),o.remove(),{domEl:lr(i,!0),newMap:zr(i)}}function M6(r){let t=document.createElement(r.tagName);return Object.entries(r.attributes).forEach(([i,o])=>{t.setAttribute(i,o)}),t.setAttribute("data-onlook-inserted","true"),t.setAttribute("data-odid",r.domId),t.setAttribute("data-oid",r.oid),t}function E$(r){r.removeAttribute("data-odid"),r.removeAttribute("data-oid"),r.removeAttribute("data-onlook-inserted");let t=Array.from(r.children);if(t.length===0)return;t.forEach((i)=>{E$(i)})}function Wi(r){let t=S(r);if(!t)return console.warn("Element not found for domId:",r),null;return Q$(t)}function Q$(r){let t=Array.from(r.attributes).reduce((o,n)=>{return o[n.name]=n.value,o},{}),i=Nr(r)||Sr(r)||null;if(!i)return console.warn("Element has no oid"),null;return{oid:i,domId:Xr(r),tagName:r.tagName.toLowerCase(),children:Array.from(r.children).map((o)=>Q$(o)).filter(Boolean),attributes:t,textContent:X$(r)||null,styles:{}}}function F$(r){let t=S(r);if(!t)throw new Error("Element not found for domId: "+r);let i=t.parentElement;if(!i)throw new Error("Inserted element has no parent");let o=Nr(i)||Sr(i);if(!o)return console.warn("Parent element has no oid"),null;let n=Xr(i),e=Array.from(i.children).indexOf(t);if(e===-1)return{type:"append",targetDomId:n,targetOid:o};return{type:"index",targetDomId:n,targetOid:o,index:e,originalIndex:e}}function G$(r){let t=document.querySelector(`[${"data-odid"}="${r}"]`);if(!t)return console.warn("No element found",{domId:r}),{dynamicType:null,coreType:null};let i=t.getAttribute("data-onlook-dynamic-type")||null,o=t.getAttribute("data-onlook-core-element-type")||null;return{dynamicType:i,coreType:o}}function S$(r,t,i){let o=document.querySelector(`[${"data-odid"}="${r}"]`);if(o){if(t)o.setAttribute("data-onlook-dynamic-type",t);if(i)o.setAttribute("data-onlook-core-element-type",i)}}function N$(){let t=document.body.querySelector(`[${"data-oid"}]`);if(t)return lr(t,!0);return null}var Qr=0,f=1,U=2,R=3,G=4,gr=5,Ft=6,er=7,wr=8,X=9,k=10,y=11,q=12,Y=13,dr=14,hr=15,d=16,nr=17,ir=18,br=19,fr=20,K=21,p=22,Z=23,xr=24,A=25;function cr(r){return r>=48&&r<=57}function Ir(r){return cr(r)||r>=65&&r<=70||r>=97&&r<=102}function Li(r){return r>=65&&r<=90}function Z6(r){return r>=97&&r<=122}function C6(r){return Li(r)||Z6(r)}function T6(r){return r>=128}function Vi(r){return C6(r)||T6(r)||r===95}function Dn(r){return Vi(r)||cr(r)||r===45}function d6(r){return r>=0&&r<=8||r===11||r>=14&&r<=31||r===127}function _n(r){return r===10||r===13||r===12}function Br(r){return _n(r)||r===32||r===9}function Or(r,t){if(r!==92)return!1;if(_n(t)||t===0)return!1;return!0}function Gt(r,t,i){if(r===45)return Vi(t)||t===45||Or(t,i);if(Vi(r))return!0;if(r===92)return Or(r,t);return!1}function Yi(r,t,i){if(r===43||r===45){if(cr(t))return 2;return t===46&&cr(i)?3:0}if(r===46)return cr(t)?2:0;if(cr(r))return 1;return 0}function Ei(r){if(r===65279)return 1;if(r===65534)return 1;return 0}var Le=new Array(128),s6=128,jn=130,Ye=131,Qi=132,Ee=133;for(let r=0;r<Le.length;r++)Le[r]=Br(r)&&jn||cr(r)&&Ye||Vi(r)&&Qi||d6(r)&&Ee||r||s6;function Fi(r){return r<128?Le[r]:Qi}function St(r,t){return t<r.length?r.charCodeAt(t):0}function Gi(r,t,i){if(i===13&&St(r,t+1)===10)return 2;return 1}function sr(r,t,i){let o=r.charCodeAt(t);if(Li(o))o=o|32;return o===i}function rt(r,t,i,o){if(i-t!==o.length)return!1;if(t<0||i>r.length)return!1;for(let n=t;n<i;n++){let e=o.charCodeAt(n-t),l=r.charCodeAt(n);if(Li(l))l=l|32;if(l!==e)return!1}return!0}function B$(r,t){for(;t>=0;t--)if(!Br(r.charCodeAt(t)))break;return t+1}function On(r,t){for(;t<r.length;t++)if(!Br(r.charCodeAt(t)))break;return t}function Qe(r,t){for(;t<r.length;t++)if(!cr(r.charCodeAt(t)))break;return t}function yr(r,t){if(t+=2,Ir(St(r,t-1))){for(let o=Math.min(r.length,t+5);t<o;t++)if(!Ir(St(r,t)))break;let i=St(r,t);if(Br(i))t+=Gi(r,t,i)}return t}function pn(r,t){for(;t<r.length;t++){let i=r.charCodeAt(t);if(Dn(i))continue;if(Or(i,St(r,t+1))){t=yr(r,t)-1;continue}break}return t}function pt(r,t){let i=r.charCodeAt(t);if(i===43||i===45)i=r.charCodeAt(t+=1);if(cr(i))t=Qe(r,t+1),i=r.charCodeAt(t);if(i===46&&cr(r.charCodeAt(t+1)))t+=2,t=Qe(r,t);if(sr(r,t,101)){let o=0;if(i=r.charCodeAt(t+1),i===45||i===43)o=1,i=r.charCodeAt(t+2);if(cr(i))t=Qe(r,t+1+o+1)}return t}function Si(r,t){for(;t<r.length;t++){let i=r.charCodeAt(t);if(i===41){t++;break}if(Or(i,St(r,t+1)))t=yr(r,t)}return t}function In(r){if(r.length===1&&!Ir(r.charCodeAt(0)))return r[0];let t=parseInt(r,16);if(t===0||t>=55296&&t<=57343||t>1114111)t=65533;return String.fromCodePoint(t)}var Nt=["EOF-token","ident-token","function-token","at-keyword-token","hash-token","string-token","bad-string-token","url-token","bad-url-token","delim-token","number-token","percentage-token","dimension-token","whitespace-token","CDO-token","CDC-token","colon-token","semicolon-token","comma-token","[-token","]-token","(-token",")-token","{-token","}-token","comment-token"];function Bt(r=null,t){if(r===null||r.length<t)return new Uint32Array(Math.max(t+1024,16384));return r}var y$=10,rz=12,A$=13;function H$(r){let t=r.source,i=t.length,o=t.length>0?Ei(t.charCodeAt(0)):0,n=Bt(r.lines,i),e=Bt(r.columns,i),l=r.startLine,u=r.startColumn;for(let g=o;g<i;g++){let c=t.charCodeAt(g);if(n[g]=l,e[g]=u++,c===y$||c===A$||c===rz){if(c===A$&&g+1<i&&t.charCodeAt(g+1)===y$)g++,n[g]=l,e[g]=u;l++,u=1}}n[i]=l,e[i]=u,r.lines=n,r.columns=e,r.computed=!0}class Ni{constructor(r,t,i,o){this.setSource(r,t,i,o),this.lines=null,this.columns=null}setSource(r="",t=0,i=1,o=1){this.source=r,this.startOffset=t,this.startLine=i,this.startColumn=o,this.computed=!1}getLocation(r,t){if(!this.computed)H$(this);return{source:t,offset:this.startOffset+r,line:this.lines[r],column:this.columns[r]}}getLocationRange(r,t,i){if(!this.computed)H$(this);return{source:i,start:{offset:this.startOffset+r,line:this.lines[r],column:this.columns[r]},end:{offset:this.startOffset+t,line:this.lines[t],column:this.columns[t]}}}}var Ar=16777215,Hr=24,It=new Uint8Array(32);It[U]=p;It[K]=p;It[br]=fr;It[Z]=xr;function R$(r){return It[r]!==0}class Bi{constructor(r,t){this.setSource(r,t)}reset(){this.eof=!1,this.tokenIndex=-1,this.tokenType=0,this.tokenStart=this.firstCharOffset,this.tokenEnd=this.firstCharOffset}setSource(r="",t=()=>{}){r=String(r||"");let i=r.length,o=Bt(this.offsetAndType,r.length+1),n=Bt(this.balance,r.length+1),e=0,l=-1,u=0,g=r.length;this.offsetAndType=null,this.balance=null,n.fill(0),t(r,(c,b,v)=>{let h=e++;if(o[h]=c<<Hr|v,l===-1)l=b;if(n[h]=g,c===u){let m=n[g];n[g]=h,g=m,u=It[o[m]>>Hr]}else if(R$(c))g=h,u=It[c]}),o[e]=Qr<<Hr|i,n[e]=e;for(let c=0;c<e;c++){let b=n[c];if(b<=c){let v=n[b];if(v!==c)n[c]=v}else if(b>e)n[c]=e}this.source=r,this.firstCharOffset=l===-1?0:l,this.tokenCount=e,this.offsetAndType=o,this.balance=n,this.reset(),this.next()}lookupType(r){if(r+=this.tokenIndex,r<this.tokenCount)return this.offsetAndType[r]>>Hr;return Qr}lookupTypeNonSC(r){for(let t=this.tokenIndex;t<this.tokenCount;t++){let i=this.offsetAndType[t]>>Hr;if(i!==Y&&i!==A){if(r--===0)return i}}return Qr}lookupOffset(r){if(r+=this.tokenIndex,r<this.tokenCount)return this.offsetAndType[r-1]&Ar;return this.source.length}lookupOffsetNonSC(r){for(let t=this.tokenIndex;t<this.tokenCount;t++){let i=this.offsetAndType[t]>>Hr;if(i!==Y&&i!==A){if(r--===0)return t-this.tokenIndex}}return Qr}lookupValue(r,t){if(r+=this.tokenIndex,r<this.tokenCount)return rt(this.source,this.offsetAndType[r-1]&Ar,this.offsetAndType[r]&Ar,t);return!1}getTokenStart(r){if(r===this.tokenIndex)return this.tokenStart;if(r>0)return r<this.tokenCount?this.offsetAndType[r-1]&Ar:this.offsetAndType[this.tokenCount]&Ar;return this.firstCharOffset}substrToCursor(r){return this.source.substring(r,this.tokenStart)}isBalanceEdge(r){return this.balance[this.tokenIndex]<r}isDelim(r,t){if(t)return this.lookupType(t)===X&&this.source.charCodeAt(this.lookupOffset(t))===r;return this.tokenType===X&&this.source.charCodeAt(this.tokenStart)===r}skip(r){let t=this.tokenIndex+r;if(t<this.tokenCount)this.tokenIndex=t,this.tokenStart=this.offsetAndType[t-1]&Ar,t=this.offsetAndType[t],this.tokenType=t>>Hr,this.tokenEnd=t&Ar;else this.tokenIndex=this.tokenCount,this.next()}next(){let r=this.tokenIndex+1;if(r<this.tokenCount)this.tokenIndex=r,this.tokenStart=this.tokenEnd,r=this.offsetAndType[r],this.tokenType=r>>Hr,this.tokenEnd=r&Ar;else this.eof=!0,this.tokenIndex=this.tokenCount,this.tokenType=Qr,this.tokenStart=this.tokenEnd=this.source.length}skipSC(){while(this.tokenType===Y||this.tokenType===A)this.next()}skipUntilBalanced(r,t){let i=r,o=0,n=0;r:for(;i<this.tokenCount;i++){if(o=this.balance[i],o<r)break r;switch(n=i>0?this.offsetAndType[i-1]&Ar:this.firstCharOffset,t(this.source.charCodeAt(n))){case 1:break r;case 2:i++;break r;default:if(R$(this.offsetAndType[i]>>Hr))i=o}}this.skip(i-this.tokenIndex)}forEachToken(r){for(let t=0,i=this.firstCharOffset;t<this.tokenCount;t++){let o=i,n=this.offsetAndType[t],e=n&Ar,l=n>>Hr;i=e,r(l,o,e,t)}}dump(){let r=new Array(this.tokenCount);return this.forEachToken((t,i,o,n)=>{r[n]={idx:n,type:Nt[t],chunk:this.source.substring(i,o),balance:this.balance[n]}}),r}}function mt(r,t){function i(v){return v<u?r.charCodeAt(v):0}function o(){if(c=pt(r,c),Gt(i(c),i(c+1),i(c+2))){b=q,c=pn(r,c);return}if(i(c)===37){b=y,c++;return}b=k}function n(){let v=c;if(c=pn(r,c),rt(r,v,c,"url")&&i(c)===40){if(c=On(r,c+1),i(c)===34||i(c)===39){b=U,c=v+4;return}l();return}if(i(c)===40){b=U,c++;return}b=f}function e(v){if(!v)v=i(c++);b=gr;for(;c<r.length;c++){let h=r.charCodeAt(c);switch(Fi(h)){case v:c++;return;case jn:if(_n(h)){c+=Gi(r,c,h),b=Ft;return}break;case 92:if(c===r.length-1)break;let m=i(c+1);if(_n(m))c+=Gi(r,c+1,m);else if(Or(h,m))c=yr(r,c)-1;break}}}function l(){b=er,c=On(r,c);for(;c<r.length;c++){let v=r.charCodeAt(c);switch(Fi(v)){case 41:c++;return;case jn:if(c=On(r,c),i(c)===41||c>=r.length){if(c<r.length)c++;return}c=Si(r,c),b=wr;return;case 34:case 39:case 40:case Ee:c=Si(r,c),b=wr;return;case 92:if(Or(v,i(c+1))){c=yr(r,c)-1;break}c=Si(r,c),b=wr;return}}}r=String(r||"");let u=r.length,g=Ei(i(0)),c=g,b;while(c<u){let v=r.charCodeAt(c);switch(Fi(v)){case jn:b=Y,c=On(r,c+1);break;case 34:e();break;case 35:if(Dn(i(c+1))||Or(i(c+1),i(c+2)))b=G,c=pn(r,c+1);else b=X,c++;break;case 39:e();break;case 40:b=K,c++;break;case 41:b=p,c++;break;case 43:if(Yi(v,i(c+1),i(c+2)))o();else b=X,c++;break;case 44:b=ir,c++;break;case 45:if(Yi(v,i(c+1),i(c+2)))o();else if(i(c+1)===45&&i(c+2)===62)b=hr,c=c+3;else if(Gt(v,i(c+1),i(c+2)))n();else b=X,c++;break;case 46:if(Yi(v,i(c+1),i(c+2)))o();else b=X,c++;break;case 47:if(i(c+1)===42)b=A,c=r.indexOf("*/",c+2),c=c===-1?r.length:c+2;else b=X,c++;break;case 58:b=d,c++;break;case 59:b=nr,c++;break;case 60:if(i(c+1)===33&&i(c+2)===45&&i(c+3)===45)b=dr,c=c+4;else b=X,c++;break;case 64:if(Gt(i(c+1),i(c+2),i(c+3)))b=R,c=pn(r,c+1);else b=X,c++;break;case 91:b=br,c++;break;case 92:if(Or(v,i(c+1)))n();else b=X,c++;break;case 93:b=fr,c++;break;case 123:b=Z,c++;break;case 125:b=xr,c++;break;case Ye:o();break;case Qi:n();break;default:b=X,c++}t(b,g,g=c)}}var yt=null;class or{static createItem(r){return{prev:null,next:null,data:r}}constructor(){this.head=null,this.tail=null,this.cursor=null}createItem(r){return or.createItem(r)}allocateCursor(r,t){let i;if(yt!==null)i=yt,yt=yt.cursor,i.prev=r,i.next=t,i.cursor=this.cursor;else i={prev:r,next:t,cursor:this.cursor};return this.cursor=i,i}releaseCursor(){let{cursor:r}=this;this.cursor=r.cursor,r.prev=null,r.next=null,r.cursor=yt,yt=r}updateCursors(r,t,i,o){let{cursor:n}=this;while(n!==null){if(n.prev===r)n.prev=t;if(n.next===i)n.next=o;n=n.cursor}}*[Symbol.iterator](){for(let r=this.head;r!==null;r=r.next)yield r.data}get size(){let r=0;for(let t=this.head;t!==null;t=t.next)r++;return r}get isEmpty(){return this.head===null}get first(){return this.head&&this.head.data}get last(){return this.tail&&this.tail.data}fromArray(r){let t=null;this.head=null;for(let i of r){let o=or.createItem(i);if(t!==null)t.next=o;else this.head=o;o.prev=t,t=o}return this.tail=t,this}toArray(){return[...this]}toJSON(){return[...this]}forEach(r,t=this){let i=this.allocateCursor(null,this.head);while(i.next!==null){let o=i.next;i.next=o.next,r.call(t,o.data,o,this)}this.releaseCursor()}forEachRight(r,t=this){let i=this.allocateCursor(this.tail,null);while(i.prev!==null){let o=i.prev;i.prev=o.prev,r.call(t,o.data,o,this)}this.releaseCursor()}reduce(r,t,i=this){let o=this.allocateCursor(null,this.head),n=t,e;while(o.next!==null)e=o.next,o.next=e.next,n=r.call(i,n,e.data,e,this);return this.releaseCursor(),n}reduceRight(r,t,i=this){let o=this.allocateCursor(this.tail,null),n=t,e;while(o.prev!==null)e=o.prev,o.prev=e.prev,n=r.call(i,n,e.data,e,this);return this.releaseCursor(),n}some(r,t=this){for(let i=this.head;i!==null;i=i.next)if(r.call(t,i.data,i,this))return!0;return!1}map(r,t=this){let i=new or;for(let o=this.head;o!==null;o=o.next)i.appendData(r.call(t,o.data,o,this));return i}filter(r,t=this){let i=new or;for(let o=this.head;o!==null;o=o.next)if(r.call(t,o.data,o,this))i.appendData(o.data);return i}nextUntil(r,t,i=this){if(r===null)return;let o=this.allocateCursor(null,r);while(o.next!==null){let n=o.next;if(o.next=n.next,t.call(i,n.data,n,this))break}this.releaseCursor()}prevUntil(r,t,i=this){if(r===null)return;let o=this.allocateCursor(r,null);while(o.prev!==null){let n=o.prev;if(o.prev=n.prev,t.call(i,n.data,n,this))break}this.releaseCursor()}clear(){this.head=null,this.tail=null}copy(){let r=new or;for(let t of this)r.appendData(t);return r}prepend(r){if(this.updateCursors(null,r,this.head,r),this.head!==null)this.head.prev=r,r.next=this.head;else this.tail=r;return this.head=r,this}prependData(r){return this.prepend(or.createItem(r))}append(r){return this.insert(r)}appendData(r){return this.insert(or.createItem(r))}insert(r,t=null){if(t!==null)if(this.updateCursors(t.prev,r,t,r),t.prev===null){if(this.head!==t)throw new Error("before doesn't belong to list");this.head=r,t.prev=r,r.next=t,this.updateCursors(null,r)}else t.prev.next=r,r.prev=t.prev,t.prev=r,r.next=t;else{if(this.updateCursors(this.tail,r,null,r),this.tail!==null)this.tail.next=r,r.prev=this.tail;else this.head=r;this.tail=r}return this}insertData(r,t){return this.insert(or.createItem(r),t)}remove(r){if(this.updateCursors(r,r.prev,r,r.next),r.prev!==null)r.prev.next=r.next;else{if(this.head!==r)throw new Error("item doesn't belong to list");this.head=r.next}if(r.next!==null)r.next.prev=r.prev;else{if(this.tail!==r)throw new Error("item doesn't belong to list");this.tail=r.prev}return r.prev=null,r.next=null,r}push(r){this.insert(or.createItem(r))}pop(){return this.tail!==null?this.remove(this.tail):null}unshift(r){this.prepend(or.createItem(r))}shift(){return this.head!==null?this.remove(this.head):null}prependList(r){return this.insertList(r,this.head)}appendList(r){return this.insertList(r)}insertList(r,t){if(r.head===null)return this;if(t!==void 0&&t!==null){if(this.updateCursors(t.prev,r.tail,t,r.head),t.prev!==null)t.prev.next=r.head,r.head.prev=t.prev;else this.head=r.head;t.prev=r.tail,r.tail.next=t}else{if(this.updateCursors(this.tail,r.tail,null,r.head),this.tail!==null)this.tail.next=r.head,r.head.prev=this.tail;else this.head=r.head;this.tail=r.tail}return r.head=null,r.tail=null,this}replace(r,t){if("head"in t)this.insertList(t,r);else this.insert(t,r);this.remove(r)}}function at(r,t){let i=Object.create(SyntaxError.prototype),o=new Error;return Object.assign(i,{name:r,message:t,get stack(){return(o.stack||"").replace(/^(.+\n){1,3}/,`${r}: ${t}
`)}})}var Fe=100,M$=60,Z$="    ";function C$({source:r,line:t,column:i,baseLine:o,baseColumn:n},e){function l(w,O){return c.slice(w,O).map((I,_)=>String(w+_+1).padStart(h)+" |"+I).join(`
`)}let u=`
`.repeat(Math.max(o-1,0)),g=" ".repeat(Math.max(n-1,0)),c=(u+g+r).split(/\r\n?|\n|\f/),b=Math.max(1,t-e)-1,v=Math.min(t+e,c.length+1),h=Math.max(4,String(v).length)+1,m=0;if(i+=(Z$.length-1)*(c[t-1].substr(0,i-1).match(/\t/g)||[]).length,i>Fe)m=i-M$+3,i=M$-2;for(let w=b;w<=v;w++)if(w>=0&&w<c.length)c[w]=c[w].replace(/\t/g,Z$),c[w]=(m>0&&c[w].length>m?"…":"")+c[w].substr(m,Fe-2)+(c[w].length>m+Fe-1?"…":"");return[l(b,t),new Array(i+h+2).join("-")+"^",l(t,v)].filter(Boolean).join(`
`).replace(/^(\s+\d+\s+\|\n)+/,"").replace(/\n(\s+\d+\s+\|)+$/,"")}function Ge(r,t,i,o,n,e=1,l=1){return Object.assign(at("SyntaxError",r),{source:t,offset:i,line:o,column:n,sourceFragment(g){return C$({source:t,line:o,column:n,baseLine:e,baseColumn:l},isNaN(g)?0:g)},get formattedMessage(){return`Parse error: ${r}
`+C$({source:t,line:o,column:n,baseLine:e,baseColumn:l},2)}})}function T$(r){let t=this.createList(),i=!1,o={recognizer:r};while(!this.eof){switch(this.tokenType){case A:this.next();continue;case Y:i=!0,this.next();continue}let n=r.getNode.call(this,o);if(n===void 0)break;if(i){if(r.onWhiteSpace)r.onWhiteSpace.call(this,n,t,o);i=!1}t.push(n)}if(i&&r.onWhiteSpace)r.onWhiteSpace.call(this,null,t,o);return t}var d$=()=>{},tz=33,nz=35,Se=59,s$=123,r0=0;function iz(r){return function(){return this[r]()}}function Ne(r){let t=Object.create(null);for(let i of Object.keys(r)){let o=r[i],n=o.parse||o;if(n)t[i]=n}return t}function oz(r){let t={context:Object.create(null),features:Object.assign(Object.create(null),r.features),scope:Object.assign(Object.create(null),r.scope),atrule:Ne(r.atrule),pseudo:Ne(r.pseudo),node:Ne(r.node)};for(let[i,o]of Object.entries(r.parseContext))switch(typeof o){case"function":t.context[i]=o;break;case"string":t.context[i]=iz(o);break}return{config:t,...t,...t.node}}function t0(r){let t="",i="<unknown>",o=!1,n=d$,e=!1,l=new Ni,u=Object.assign(new Bi,oz(r||{}),{parseAtrulePrelude:!0,parseRulePrelude:!0,parseValue:!0,parseCustomProperty:!1,readSequence:T$,consumeUntilBalanceEnd:()=>0,consumeUntilLeftCurlyBracket(c){return c===s$?1:0},consumeUntilLeftCurlyBracketOrSemicolon(c){return c===s$||c===Se?1:0},consumeUntilExclamationMarkOrSemicolon(c){return c===tz||c===Se?1:0},consumeUntilSemicolonIncluded(c){return c===Se?2:0},createList(){return new or},createSingleNodeList(c){return new or().appendData(c)},getFirstListNode(c){return c&&c.first},getLastListNode(c){return c&&c.last},parseWithFallback(c,b){let v=this.tokenIndex;try{return c.call(this)}catch(h){if(e)throw h;this.skip(v-this.tokenIndex);let m=b.call(this);return e=!0,n(h,m),e=!1,m}},lookupNonWSType(c){let b;do if(b=this.lookupType(c++),b!==Y&&b!==A)return b;while(b!==r0);return r0},charCodeAt(c){return c>=0&&c<t.length?t.charCodeAt(c):0},substring(c,b){return t.substring(c,b)},substrToCursor(c){return this.source.substring(c,this.tokenStart)},cmpChar(c,b){return sr(t,c,b)},cmpStr(c,b,v){return rt(t,c,b,v)},consume(c){let b=this.tokenStart;return this.eat(c),this.substrToCursor(b)},consumeFunctionName(){let c=t.substring(this.tokenStart,this.tokenEnd-1);return this.eat(U),c},consumeNumber(c){let b=t.substring(this.tokenStart,pt(t,this.tokenStart));return this.eat(c),b},eat(c){if(this.tokenType!==c){let b=Nt[c].slice(0,-6).replace(/-/g," ").replace(/^./,(m)=>m.toUpperCase()),v=`${/[[\](){}]/.test(b)?`"${b}"`:b} is expected`,h=this.tokenStart;switch(c){case f:if(this.tokenType===U||this.tokenType===er)h=this.tokenEnd-1,v="Identifier is expected but function found";else v="Identifier is expected";break;case G:if(this.isDelim(nz))this.next(),h++,v="Name is expected";break;case y:if(this.tokenType===k)h=this.tokenEnd,v="Percent sign is expected";break}this.error(v,h)}this.next()},eatIdent(c){if(this.tokenType!==f||this.lookupValue(0,c)===!1)this.error(`Identifier "${c}" is expected`);this.next()},eatDelim(c){if(!this.isDelim(c))this.error(`Delim "${String.fromCharCode(c)}" is expected`);this.next()},getLocation(c,b){if(o)return l.getLocationRange(c,b,i);return null},getLocationFromList(c){if(o){let b=this.getFirstListNode(c),v=this.getLastListNode(c);return l.getLocationRange(b!==null?b.loc.start.offset-l.startOffset:this.tokenStart,v!==null?v.loc.end.offset-l.startOffset:this.tokenStart,i)}return null},error(c,b){let v=typeof b!=="undefined"&&b<t.length?l.getLocation(b):this.eof?l.getLocation(B$(t,t.length-1)):l.getLocation(this.tokenStart);throw new Ge(c||"Unexpected input",t,v.offset,v.line,v.column,l.startLine,l.startColumn)}});return Object.assign(function(c,b){t=c,b=b||{},u.setSource(t,mt),l.setSource(t,b.offset,b.line,b.column),i=b.filename||"<unknown>",o=Boolean(b.positions),n=typeof b.onParseError==="function"?b.onParseError:d$,e=!1,u.parseAtrulePrelude="parseAtrulePrelude"in b?Boolean(b.parseAtrulePrelude):!0,u.parseRulePrelude="parseRulePrelude"in b?Boolean(b.parseRulePrelude):!0,u.parseValue="parseValue"in b?Boolean(b.parseValue):!0,u.parseCustomProperty="parseCustomProperty"in b?Boolean(b.parseCustomProperty):!1;let{context:v="default",onComment:h}=b;if(v in u.context===!1)throw new Error("Unknown context `"+v+"`");if(typeof h==="function")u.forEachToken((w,O,I)=>{if(w===A){let _=u.getLocation(O,I),J=rt(t,I-2,I,"*/")?t.slice(O+2,I-2):t.slice(O+2,I);h(J,_)}});let m=u.context[v].call(u,b);if(!u.eof)u.error();return m},{SyntaxError:Ge,config:u.config})}var Un=u0(),ur=yi(),Hi=$0().ArraySet,Zz=w0().MappingList;function Vr(r){if(!r)r={};this._file=ur.getArg(r,"file",null),this._sourceRoot=ur.getArg(r,"sourceRoot",null),this._skipValidation=ur.getArg(r,"skipValidation",!1),this._ignoreInvalidMapping=ur.getArg(r,"ignoreInvalidMapping",!1),this._sources=new Hi,this._names=new Hi,this._mappings=new Zz,this._sourcesContents=null}Vr.prototype._version=3;Vr.fromSourceMap=function r(t,i){var o=t.sourceRoot,n=new Vr(Object.assign(i||{},{file:t.file,sourceRoot:o}));return t.eachMapping(function(e){var l={generated:{line:e.generatedLine,column:e.generatedColumn}};if(e.source!=null){if(l.source=e.source,o!=null)l.source=ur.relative(o,l.source);if(l.original={line:e.originalLine,column:e.originalColumn},e.name!=null)l.name=e.name}n.addMapping(l)}),t.sources.forEach(function(e){var l=e;if(o!==null)l=ur.relative(o,e);if(!n._sources.has(l))n._sources.add(l);var u=t.sourceContentFor(e);if(u!=null)n.setSourceContent(e,u)}),n};Vr.prototype.addMapping=function r(t){var i=ur.getArg(t,"generated"),o=ur.getArg(t,"original",null),n=ur.getArg(t,"source",null),e=ur.getArg(t,"name",null);if(!this._skipValidation){if(this._validateMapping(i,o,n,e)===!1)return}if(n!=null){if(n=String(n),!this._sources.has(n))this._sources.add(n)}if(e!=null){if(e=String(e),!this._names.has(e))this._names.add(e)}this._mappings.add({generatedLine:i.line,generatedColumn:i.column,originalLine:o!=null&&o.line,originalColumn:o!=null&&o.column,source:n,name:e})};Vr.prototype.setSourceContent=function r(t,i){var o=t;if(this._sourceRoot!=null)o=ur.relative(this._sourceRoot,o);if(i!=null){if(!this._sourcesContents)this._sourcesContents=Object.create(null);this._sourcesContents[ur.toSetString(o)]=i}else if(this._sourcesContents){if(delete this._sourcesContents[ur.toSetString(o)],Object.keys(this._sourcesContents).length===0)this._sourcesContents=null}};Vr.prototype.applySourceMap=function r(t,i,o){var n=i;if(i==null){if(t.file==null)throw new Error(`SourceMapGenerator.prototype.applySourceMap requires either an explicit source file, or the source map's "file" property. Both were omitted.`);n=t.file}var e=this._sourceRoot;if(e!=null)n=ur.relative(e,n);var l=new Hi,u=new Hi;this._mappings.unsortedForEach(function(g){if(g.source===n&&g.originalLine!=null){var c=t.originalPositionFor({line:g.originalLine,column:g.originalColumn});if(c.source!=null){if(g.source=c.source,o!=null)g.source=ur.join(o,g.source);if(e!=null)g.source=ur.relative(e,g.source);if(g.originalLine=c.line,g.originalColumn=c.column,c.name!=null)g.name=c.name}}var b=g.source;if(b!=null&&!l.has(b))l.add(b);var v=g.name;if(v!=null&&!u.has(v))u.add(v)},this),this._sources=l,this._names=u,t.sources.forEach(function(g){var c=t.sourceContentFor(g);if(c!=null){if(o!=null)g=ur.join(o,g);if(e!=null)g=ur.relative(e,g);this.setSourceContent(g,c)}},this)};Vr.prototype._validateMapping=function r(t,i,o,n){if(i&&typeof i.line!=="number"&&typeof i.column!=="number"){var e="original.line and original.column are not numbers -- you probably meant to omit the original mapping entirely and only map the generated position. If so, pass null for the original mapping instead of an object with empty or null values.";if(this._ignoreInvalidMapping){if(typeof console!=="undefined"&&console.warn)console.warn(e);return!1}else throw new Error(e)}if(t&&"line"in t&&"column"in t&&t.line>0&&t.column>=0&&!i&&!o&&!n)return;else if(t&&"line"in t&&"column"in t&&i&&"line"in i&&"column"in i&&t.line>0&&t.column>=0&&i.line>0&&i.column>=0&&o)return;else{var e="Invalid mapping: "+JSON.stringify({generated:t,source:o,original:i,name:n});if(this._ignoreInvalidMapping){if(typeof console!=="undefined"&&console.warn)console.warn(e);return!1}else throw new Error(e)}};Vr.prototype._serializeMappings=function r(){var t=0,i=1,o=0,n=0,e=0,l=0,u="",g,c,b,v,h=this._mappings.toArray();for(var m=0,w=h.length;m<w;m++){if(c=h[m],g="",c.generatedLine!==i){t=0;while(c.generatedLine!==i)g+=";",i++}else if(m>0){if(!ur.compareByGeneratedPositionsInflated(c,h[m-1]))continue;g+=","}if(g+=Un.encode(c.generatedColumn-t),t=c.generatedColumn,c.source!=null){if(v=this._sources.indexOf(c.source),g+=Un.encode(v-l),l=v,g+=Un.encode(c.originalLine-1-n),n=c.originalLine-1,g+=Un.encode(c.originalColumn-o),o=c.originalColumn,c.name!=null)b=this._names.indexOf(c.name),g+=Un.encode(b-e),e=b}u+=g}return u};Vr.prototype._generateSourcesContent=function r(t,i){return t.map(function(o){if(!this._sourcesContents)return null;if(i!=null)o=ur.relative(i,o);var n=ur.toSetString(o);return Object.prototype.hasOwnProperty.call(this._sourcesContents,n)?this._sourcesContents[n]:null},this)};Vr.prototype.toJSON=function r(){var t={version:this._version,sources:this._sources.toArray(),names:this._names.toArray(),mappings:this._serializeMappings()};if(this._file!=null)t.file=this._file;if(this._sourceRoot!=null)t.sourceRoot=this._sourceRoot;if(this._sourcesContents)t.sourcesContent=this._generateSourcesContent(t.sources,t.sourceRoot);return t};Vr.prototype.toString=function r(){return JSON.stringify(this.toJSON())};var Re=Vr;var f0=new Set(["Atrule","Selector","Declaration"]);function z0(r){let t=new Re,i={line:1,column:0},o={line:0,column:0},n={line:1,column:0},e={generated:n},l=1,u=0,g=!1,c=r.node;r.node=function(h){if(h.loc&&h.loc.start&&f0.has(h.type)){let m=h.loc.start.line,w=h.loc.start.column-1;if(o.line!==m||o.column!==w){if(o.line=m,o.column=w,i.line=l,i.column=u,g){if(g=!1,i.line!==n.line||i.column!==n.column)t.addMapping(e)}g=!0,t.addMapping({source:h.loc.source,original:o,generated:i})}}if(c.call(this,h),g&&f0.has(h.type))n.line=l,n.column=u};let b=r.emit;r.emit=function(h,m,w){for(let O=0;O<h.length;O++)if(h.charCodeAt(O)===10)l++,u=0;else u++;b(h,m,w)};let v=r.result;return r.result=function(){if(g)t.addMapping(e);return{css:v(),map:t}},r}var Ri={};a(Ri,{spec:()=>sz,safe:()=>Ze});var Cz=43,Tz=45,Me=(r,t)=>{if(r===X)r=t;if(typeof r==="string"){let i=r.charCodeAt(0);return i>127?32768:i<<8}return r},D0=[[f,f],[f,U],[f,er],[f,wr],[f,"-"],[f,k],[f,y],[f,q],[f,hr],[f,K],[R,f],[R,U],[R,er],[R,wr],[R,"-"],[R,k],[R,y],[R,q],[R,hr],[G,f],[G,U],[G,er],[G,wr],[G,"-"],[G,k],[G,y],[G,q],[G,hr],[q,f],[q,U],[q,er],[q,wr],[q,"-"],[q,k],[q,y],[q,q],[q,hr],["#",f],["#",U],["#",er],["#",wr],["#","-"],["#",k],["#",y],["#",q],["#",hr],["-",f],["-",U],["-",er],["-",wr],["-","-"],["-",k],["-",y],["-",q],["-",hr],[k,f],[k,U],[k,er],[k,wr],[k,k],[k,y],[k,q],[k,"%"],[k,hr],["@",f],["@",U],["@",er],["@",wr],["@","-"],["@",hr],[".",k],[".",y],[".",q],["+",k],["+",y],["+",q],["/","*"]],dz=D0.concat([[f,G],[q,G],[G,G],[R,K],[R,gr],[R,d],[y,y],[y,q],[y,U],[y,"-"],[p,f],[p,U],[p,y],[p,q],[p,G],[p,"-"]]);function _0(r){let t=new Set(r.map(([i,o])=>Me(i)<<16|Me(o)));return function(i,o,n){let e=Me(o,n),l=n.charCodeAt(0);if(l===Tz&&o!==f&&o!==U&&o!==hr||l===Cz?t.has(i<<16|l<<8):t.has(i<<16|e))this.emit(" ",Y,!0);return e}}var sz=_0(D0),Ze=_0(dz);var r1=92;function t1(r,t){if(typeof t==="function"){let i=null;r.children.forEach((o)=>{if(i!==null)t.call(this,i);this.node(o),i=o});return}r.children.forEach(this.node,this)}function n1(r){mt(r,(t,i,o)=>{this.token(t,r.slice(i,o))})}function j0(r){let t=new Map;for(let[i,o]of Object.entries(r.node))if(typeof(o.generate||o)==="function")t.set(i,o.generate||o);return function(i,o){let n="",e=0,l={node(g){if(t.has(g.type))t.get(g.type).call(u,g);else throw new Error("Unknown node type: "+g.type)},tokenBefore:Ze,token(g,c){if(e=this.tokenBefore(e,g,c),this.emit(c,g,!1),g===X&&c.charCodeAt(0)===r1)this.emit(`
`,Y,!0)},emit(g){n+=g},result(){return n}};if(o){if(typeof o.decorator==="function")l=o.decorator(l);if(o.sourceMap)l=z0(l);if(o.mode in Ri)l.tokenBefore=Ri[o.mode]}let u={node:(g)=>l.node(g),children:t1,token:(g,c)=>l.token(g,c),tokenize:n1};return l.node(i),l.result()}}function O0(r){return{fromPlainObject(t){return r(t,{enter(i){if(i.children&&i.children instanceof or===!1)i.children=new or().fromArray(i.children)}}),t},toPlainObject(t){return r(t,{leave(i){if(i.children&&i.children instanceof or)i.children=i.children.toArray()}}),t}}}var{hasOwnProperty:Ce}=Object.prototype,kn=function(){};function p0(r){return typeof r==="function"?r:kn}function I0(r,t){return function(i,o,n){if(i.type===t)r.call(this,i,o,n)}}function i1(r,t){let i=t.structure,o=[];for(let n in i){if(Ce.call(i,n)===!1)continue;let e=i[n],l={name:n,type:!1,nullable:!1};if(!Array.isArray(e))e=[e];for(let u of e)if(u===null)l.nullable=!0;else if(typeof u==="string")l.type="node";else if(Array.isArray(u))l.type="list";if(l.type)o.push(l)}if(o.length)return{context:t.walkContext,fields:o};return null}function o1(r){let t={};for(let i in r.node)if(Ce.call(r.node,i)){let o=r.node[i];if(!o.structure)throw new Error("Missed `structure` field in `"+i+"` node type definition");t[i]=i1(i,o)}return t}function a0(r,t){let i=r.fields.slice(),o=r.context,n=typeof o==="string";if(t)i.reverse();return function(e,l,u,g){let c;if(n)c=l[o],l[o]=e;for(let b of i){let v=e[b.name];if(!b.nullable||v){if(b.type==="list"){if(t?v.reduceRight(g,!1):v.reduce(g,!1))return!0}else if(u(v))return!0}}if(n)l[o]=c}}function U0({StyleSheet:r,Atrule:t,Rule:i,Block:o,DeclarationList:n}){return{Atrule:{StyleSheet:r,Atrule:t,Rule:i,Block:o},Rule:{StyleSheet:r,Atrule:t,Rule:i,Block:o},Declaration:{StyleSheet:r,Atrule:t,Rule:i,Block:o,DeclarationList:n}}}function k0(r){let t=o1(r),i={},o={},n=Symbol("break-walk"),e=Symbol("skip-node");for(let c in t)if(Ce.call(t,c)&&t[c]!==null)i[c]=a0(t[c],!1),o[c]=a0(t[c],!0);let l=U0(i),u=U0(o),g=function(c,b){function v(_,J,L){let P=h.call(I,_,J,L);if(P===n)return!0;if(P===e)return!1;if(w.hasOwnProperty(_.type)){if(w[_.type](_,I,v,O))return!0}if(m.call(I,_,J,L)===n)return!0;return!1}let h=kn,m=kn,w=i,O=(_,J,L,P)=>_||v(J,L,P),I={break:n,skip:e,root:c,stylesheet:null,atrule:null,atrulePrelude:null,rule:null,selector:null,block:null,declaration:null,function:null};if(typeof b==="function")h=b;else if(b){if(h=p0(b.enter),m=p0(b.leave),b.reverse)w=o;if(b.visit){if(l.hasOwnProperty(b.visit))w=b.reverse?u[b.visit]:l[b.visit];else if(!t.hasOwnProperty(b.visit))throw new Error("Bad value `"+b.visit+"` for `visit` option (should be: "+Object.keys(t).sort().join(", ")+")");h=I0(h,b.visit),m=I0(m,b.visit)}}if(h===kn&&m===kn)throw new Error("Neither `enter` nor `leave` walker handler is set or both aren't a function");v(c)};return g.break=n,g.skip=e,g.find=function(c,b){let v=null;return g(c,function(h,m,w){if(b.call(this,h,m,w))return v=h,n}),v},g.findLast=function(c,b){let v=null;return g(c,{reverse:!0,enter(h,m,w){if(b.call(this,h,m,w))return v=h,n}}),v},g.findAll=function(c,b){let v=[];return g(c,function(h,m,w){if(b.call(this,h,m,w))v.push(h)}),v},g}function e1(r){return r}function l1(r){let{min:t,max:i,comma:o}=r;if(t===0&&i===0)return o?"#?":"*";if(t===0&&i===1)return"?";if(t===1&&i===0)return o?"#":"+";if(t===1&&i===1)return"";return(o?"#":"")+(t===i?"{"+t+"}":"{"+t+","+(i!==0?i:"")+"}")}function c1(r){switch(r.type){case"Range":return" ["+(r.min===null?"-∞":r.min)+","+(r.max===null?"∞":r.max)+"]";default:throw new Error("Unknown node type `"+r.type+"`")}}function u1(r,t,i,o){let n=r.combinator===" "||o?r.combinator:" "+r.combinator+" ",e=r.terms.map((l)=>Mi(l,t,i,o)).join(n);if(r.explicit||i)return(o||e[0]===","?"[":"[ ")+e+(o?"]":" ]");return e}function Mi(r,t,i,o){let n;switch(r.type){case"Group":n=u1(r,t,i,o)+(r.disallowEmpty?"!":"");break;case"Multiplier":return Mi(r.term,t,i,o)+t(l1(r),r);case"Boolean":n="<boolean-expr["+Mi(r.term,t,i,o)+"]>";break;case"Type":n="<"+r.name+(r.opts?t(c1(r.opts),r.opts):"")+">";break;case"Property":n="<'"+r.name+"'>";break;case"Keyword":n=r.name;break;case"AtKeyword":n="@"+r.name;break;case"Function":n=r.name+"(";break;case"String":case"Token":n=r.value;break;case"Comma":n=",";break;default:throw new Error("Unknown node type `"+r.type+"`")}return t(n,r)}function Ht(r,t){let i=e1,o=!1,n=!1;if(typeof t==="function")i=t;else if(t){if(o=Boolean(t.forceBraces),n=Boolean(t.compact),typeof t.decorate==="function")i=t.decorate}return Mi(r,i,o,n)}var J0={offset:0,line:1,column:1};function g1(r,t){let{tokens:i,longestMatch:o}=r,n=o<i.length?i[o].node||null:null,e=n!==t?n:null,l=0,u=0,g=0,c="",b,v;for(let h=0;h<i.length;h++){let m=i[h].value;if(h===o)u=m.length,l=c.length;if(e!==null&&i[h].node===e)if(h<=o)g++;else g=0;c+=m}if(o===i.length||g>1)b=Zi(e||t,"end")||Jn(J0,c),v=Jn(b);else b=Zi(e,"start")||Jn(Zi(t,"start")||J0,c.slice(0,l)),v=Zi(e,"end")||Jn(b,c.substr(l,u));return{css:c,mismatchOffset:l,mismatchLength:u,start:b,end:v}}function Zi(r,t){let i=r&&r.loc&&r.loc[t];if(i)return"line"in i?Jn(i):i;return null}function Jn({offset:r,line:t,column:i},o){let n={offset:r,line:t,column:i};if(o){let e=o.split(/\n|\r\n?|\f/);n.offset+=o.length,n.line+=e.length-1,n.column=e.length===1?n.column+o.length:e.pop().length+1}return n}var Rt=function(r,t){let i=at("SyntaxReferenceError",r+(t?" `"+t+"`":""));return i.reference=t,i},X0=function(r,t,i,o){let n=at("SyntaxMatchError",r),{css:e,mismatchOffset:l,mismatchLength:u,start:g,end:c}=g1(o,i);return n.rawMessage=r,n.syntax=t?Ht(t):"<generic>",n.css=e,n.mismatchOffset=l,n.mismatchLength=u,n.message=r+`
  syntax: `+n.syntax+`
   value: `+(e||"<empty string>")+`
  --------`+new Array(n.mismatchOffset+1).join("-")+"^",Object.assign(n,g),n.loc={source:i&&i.loc&&i.loc.source||"<unknown>",start:g,end:c},n};var Ci=new Map,Mt=new Map;var Ti=b1,Te=m1;function di(r,t){return t=t||0,r.length-t>=2&&r.charCodeAt(t)===45&&r.charCodeAt(t+1)===45}function P0(r,t){if(t=t||0,r.length-t>=3){if(r.charCodeAt(t)===45&&r.charCodeAt(t+1)!==45){let i=r.indexOf("-",t+2);if(i!==-1)return r.substring(t,i+1)}}return""}function b1(r){if(Ci.has(r))return Ci.get(r);let t=r.toLowerCase(),i=Ci.get(t);if(i===void 0){let o=di(t,0),n=!o?P0(t,0):"";i=Object.freeze({basename:t.substr(n.length),name:t,prefix:n,vendor:n,custom:o})}return Ci.set(r,i),i}function m1(r){if(Mt.has(r))return Mt.get(r);let t=r,i=r[0];if(i==="/")i=r[1]==="/"?"//":"/";else if(i!=="_"&&i!=="*"&&i!=="$"&&i!=="#"&&i!=="+"&&i!=="&")i="";let o=di(t,i.length);if(!o){if(t=t.toLowerCase(),Mt.has(t)){let u=Mt.get(t);return Mt.set(r,u),u}}let n=!o?P0(t,i.length):"",e=t.substr(0,i.length+n.length),l=Object.freeze({basename:t.substr(e.length),name:t.substr(i.length),hack:i,vendor:n,prefix:e,custom:o});return Mt.set(r,l),l}var Zt=["initial","inherit","unset","revert","revert-layer"];var Pn=43,Rr=45,de=110,Ct=!0,h1=!1;function rl(r,t){return r!==null&&r.type===X&&r.value.charCodeAt(0)===t}function Xn(r,t,i){while(r!==null&&(r.type===Y||r.type===A))r=i(++t);return t}function vt(r,t,i,o){if(!r)return 0;let n=r.value.charCodeAt(t);if(n===Pn||n===Rr){if(i)return 0;t++}for(;t<r.value.length;t++)if(!cr(r.value.charCodeAt(t)))return 0;return o+1}function se(r,t,i){let o=!1,n=Xn(r,t,i);if(r=i(n),r===null)return t;if(r.type!==k)if(rl(r,Pn)||rl(r,Rr)){if(o=!0,n=Xn(i(++n),n,i),r=i(n),r===null||r.type!==k)return 0}else return t;if(!o){let e=r.value.charCodeAt(0);if(e!==Pn&&e!==Rr)return 0}return vt(r,o?0:1,o,n)}function tl(r,t){let i=0;if(!r)return 0;if(r.type===k)return vt(r,0,h1,i);else if(r.type===f&&r.value.charCodeAt(0)===Rr){if(!sr(r.value,1,de))return 0;switch(r.value.length){case 2:return se(t(++i),i,t);case 3:if(r.value.charCodeAt(2)!==Rr)return 0;return i=Xn(t(++i),i,t),r=t(i),vt(r,0,Ct,i);default:if(r.value.charCodeAt(2)!==Rr)return 0;return vt(r,3,Ct,i)}}else if(r.type===f||rl(r,Pn)&&t(i+1).type===f){if(r.type!==f)r=t(++i);if(r===null||!sr(r.value,0,de))return 0;switch(r.value.length){case 1:return se(t(++i),i,t);case 2:if(r.value.charCodeAt(1)!==Rr)return 0;return i=Xn(t(++i),i,t),r=t(i),vt(r,0,Ct,i);default:if(r.value.charCodeAt(1)!==Rr)return 0;return vt(r,2,Ct,i)}}else if(r.type===q){let o=r.value.charCodeAt(0),n=o===Pn||o===Rr?1:0,e=n;for(;e<r.value.length;e++)if(!cr(r.value.charCodeAt(e)))break;if(e===n)return 0;if(!sr(r.value,e,de))return 0;if(e+1===r.value.length)return se(t(++i),i,t);else{if(r.value.charCodeAt(e+1)!==Rr)return 0;if(e+2===r.value.length)return i=Xn(t(++i),i,t),r=t(i),vt(r,0,Ct,i);else return vt(r,e+2,Ct,i)}}return 0}var $1=43,q0=45,W0=63,x1=117;function nl(r,t){return r!==null&&r.type===X&&r.value.charCodeAt(0)===t}function w1(r,t){return r.value.charCodeAt(0)===t}function qn(r,t,i){let o=0;for(let n=t;n<r.value.length;n++){let e=r.value.charCodeAt(n);if(e===q0&&i&&o!==0)return qn(r,t+o+1,!1),6;if(!Ir(e))return 0;if(++o>6)return 0}return o}function si(r,t,i){if(!r)return 0;while(nl(i(t),W0)){if(++r>6)return 0;t++}return t}function il(r,t){let i=0;if(r===null||r.type!==f||!sr(r.value,0,x1))return 0;if(r=t(++i),r===null)return 0;if(nl(r,$1)){if(r=t(++i),r===null)return 0;if(r.type===f)return si(qn(r,0,!0),++i,t);if(nl(r,W0))return si(1,++i,t);return 0}if(r.type===k){let o=qn(r,1,!0);if(o===0)return 0;if(r=t(++i),r===null)return i;if(r.type===q||r.type===k){if(!w1(r,q0)||!qn(r,1,!1))return 0;return i+1}return si(o,i,t)}if(r.type===q)return si(qn(r,1,!0),++i,t);return 0}var f1=["calc(","-moz-calc(","-webkit-calc("],ol=new Map([[U,p],[K,p],[br,fr],[Z,xr]]);function Fr(r,t){return t<r.length?r.charCodeAt(t):0}function K0(r,t){return rt(r,0,r.length,t)}function V0(r,t){for(let i=0;i<t.length;i++)if(K0(r,t[i]))return!0;return!1}function L0(r,t){if(t!==r.length-2)return!1;return Fr(r,t)===92&&cr(Fr(r,t+1))}function ro(r,t,i){if(r&&r.type==="Range"){let o=Number(i!==void 0&&i!==t.length?t.substr(0,i):t);if(isNaN(o))return!0;if(r.min!==null&&o<r.min&&typeof r.min!=="string")return!0;if(r.max!==null&&o>r.max&&typeof r.max!=="string")return!0}return!1}function z1(r,t){let i=0,o=[],n=0;r:do{switch(r.type){case xr:case p:case fr:if(r.type!==i)break r;if(i=o.pop(),o.length===0){n++;break r}break;case U:case K:case br:case Z:o.push(i),i=ol.get(r.type);break}n++}while(r=t(n));return n}function Lr(r){return function(t,i,o){if(t===null)return 0;if(t.type===U&&V0(t.value,f1))return z1(t,i);return r(t,i,o)}}function C(r){return function(t){if(t===null||t.type!==r)return 0;return 1}}function D1(r){if(r===null||r.type!==f)return 0;let t=r.value.toLowerCase();if(V0(t,Zt))return 0;if(K0(t,"default"))return 0;return 1}function Y0(r){if(r===null||r.type!==f)return 0;if(Fr(r.value,0)!==45||Fr(r.value,1)!==45)return 0;return 1}function _1(r){if(!Y0(r))return 0;if(r.value==="--")return 0;return 1}function j1(r){if(r===null||r.type!==G)return 0;let t=r.value.length;if(t!==4&&t!==5&&t!==7&&t!==9)return 0;for(let i=1;i<t;i++)if(!Ir(Fr(r.value,i)))return 0;return 1}function O1(r){if(r===null||r.type!==G)return 0;if(!Gt(Fr(r.value,1),Fr(r.value,2),Fr(r.value,3)))return 0;return 1}function p1(r,t){if(!r)return 0;let i=0,o=[],n=0;r:do{switch(r.type){case Ft:case wr:break r;case xr:case p:case fr:if(r.type!==i)break r;i=o.pop();break;case nr:if(i===0)break r;break;case X:if(i===0&&r.value==="!")break r;break;case U:case K:case br:case Z:o.push(i),i=ol.get(r.type);break}n++}while(r=t(n));return n}function I1(r,t){if(!r)return 0;let i=0,o=[],n=0;r:do{switch(r.type){case Ft:case wr:break r;case xr:case p:case fr:if(r.type!==i)break r;i=o.pop();break;case U:case K:case br:case Z:o.push(i),i=ol.get(r.type);break}n++}while(r=t(n));return n}function it(r){if(r)r=new Set(r);return function(t,i,o){if(t===null||t.type!==q)return 0;let n=pt(t.value,0);if(r!==null){let e=t.value.indexOf("\\",n),l=e===-1||!L0(t.value,e)?t.value.substr(n):t.value.substring(n,e);if(r.has(l.toLowerCase())===!1)return 0}if(ro(o,t.value,n))return 0;return 1}}function a1(r,t,i){if(r===null||r.type!==y)return 0;if(ro(i,r.value,r.value.length-1))return 0;return 1}function E0(r){if(typeof r!=="function")r=function(){return 0};return function(t,i,o){if(t!==null&&t.type===k){if(Number(t.value)===0)return 1}return r(t,i,o)}}function U1(r,t,i){if(r===null)return 0;let o=pt(r.value,0);if(o!==r.value.length&&!L0(r.value,o))return 0;if(ro(i,r.value,o))return 0;return 1}function k1(r,t,i){if(r===null||r.type!==k)return 0;let o=Fr(r.value,0)===43||Fr(r.value,0)===45?1:0;for(;o<r.value.length;o++)if(!cr(Fr(r.value,o)))return 0;if(ro(i,r.value,o))return 0;return 1}var J1={"ident-token":C(f),"function-token":C(U),"at-keyword-token":C(R),"hash-token":C(G),"string-token":C(gr),"bad-string-token":C(Ft),"url-token":C(er),"bad-url-token":C(wr),"delim-token":C(X),"number-token":C(k),"percentage-token":C(y),"dimension-token":C(q),"whitespace-token":C(Y),"CDO-token":C(dr),"CDC-token":C(hr),"colon-token":C(d),"semicolon-token":C(nr),"comma-token":C(ir),"[-token":C(br),"]-token":C(fr),"(-token":C(K),")-token":C(p),"{-token":C(Z),"}-token":C(xr)},X1={string:C(gr),ident:C(f),percentage:Lr(a1),zero:E0(),number:Lr(U1),integer:Lr(k1),"custom-ident":D1,"dashed-ident":Y0,"custom-property-name":_1,"hex-color":j1,"id-selector":O1,"an-plus-b":tl,urange:il,"declaration-value":p1,"any-value":I1};function P1(r){let{angle:t,decibel:i,frequency:o,flex:n,length:e,resolution:l,semitones:u,time:g}=r||{};return{dimension:Lr(it(null)),angle:Lr(it(t)),decibel:Lr(it(i)),frequency:Lr(it(o)),flex:Lr(it(n)),length:Lr(E0(it(e))),resolution:Lr(it(l)),semitones:Lr(it(u)),time:Lr(it(g))}}function Q0(r){return{...J1,...X1,...P1(r)}}var to={};a(to,{time:()=>K1,semitones:()=>Q1,resolution:()=>L1,length:()=>q1,frequency:()=>V1,flex:()=>Y1,decibel:()=>E1,angle:()=>W1});var q1=["cm","mm","q","in","pt","pc","px","em","rem","ex","rex","cap","rcap","ch","rch","ic","ric","lh","rlh","vw","svw","lvw","dvw","vh","svh","lvh","dvh","vi","svi","lvi","dvi","vb","svb","lvb","dvb","vmin","svmin","lvmin","dvmin","vmax","svmax","lvmax","dvmax","cqw","cqh","cqi","cqb","cqmin","cqmax"],W1=["deg","grad","rad","turn"],K1=["s","ms"],V1=["hz","khz"],L1=["dpi","dpcm","dppx","x"],Y1=["fr"],E1=["db"],Q1=["st"];function el(r,t,i){return Object.assign(at("SyntaxError",r),{input:t,offset:i,rawMessage:r,message:r+`
  `+t+`
--`+new Array((i||t.length)+1).join("-")+"^"})}var F1=9,G1=10,S1=12,N1=13,B1=32,F0=new Uint8Array(128).map((r,t)=>/[a-zA-Z0-9\-]/.test(String.fromCharCode(t))?1:0);class ll{constructor(r){this.str=r,this.pos=0}charCodeAt(r){return r<this.str.length?this.str.charCodeAt(r):0}charCode(){return this.charCodeAt(this.pos)}isNameCharCode(r=this.charCode()){return r<128&&F0[r]===1}nextCharCode(){return this.charCodeAt(this.pos+1)}nextNonWsCode(r){return this.charCodeAt(this.findWsEnd(r))}skipWs(){this.pos=this.findWsEnd(this.pos)}findWsEnd(r){for(;r<this.str.length;r++){let t=this.str.charCodeAt(r);if(t!==N1&&t!==G1&&t!==S1&&t!==B1&&t!==F1)break}return r}substringToPos(r){return this.str.substring(this.pos,this.pos=r)}eat(r){if(this.charCode()!==r)this.error("Expect `"+String.fromCharCode(r)+"`");this.pos++}peek(){return this.pos<this.str.length?this.str.charAt(this.pos++):""}error(r){throw new el(r,this.str,this.pos)}scanSpaces(){return this.substringToPos(this.findWsEnd(this.pos))}scanWord(){let r=this.pos;for(;r<this.str.length;r++){let t=this.str.charCodeAt(r);if(t>=128||F0[t]===0)break}if(this.pos===r)this.error("Expect a keyword");return this.substringToPos(r)}scanNumber(){let r=this.pos;for(;r<this.str.length;r++){let t=this.str.charCodeAt(r);if(t<48||t>57)break}if(this.pos===r)this.error("Expect a number");return this.substringToPos(r)}scanString(){let r=this.str.indexOf("'",this.pos+1);if(r===-1)this.pos=this.str.length,this.error("Expect an apostrophe");return this.substringToPos(r+1)}}var y1=9,A1=10,H1=12,R1=13,M1=32,R0=33,bl=35,G0=38,no=39,M0=40,Z1=41,Z0=42,ml=43,vl=44,S0=45,hl=60,ul=62,gl=63,C1=64,Wn=91,Kn=93,io=123,N0=124,B0=125,y0=8734,A0={" ":1,"&&":2,"||":3,"|":4};function H0(r){let t=null,i=null;if(r.eat(io),r.skipWs(),t=r.scanNumber(r),r.skipWs(),r.charCode()===vl){if(r.pos++,r.skipWs(),r.charCode()!==B0)i=r.scanNumber(r),r.skipWs()}else i=t;return r.eat(B0),{min:Number(t),max:i?Number(i):0}}function T1(r){let t=null,i=!1;switch(r.charCode()){case Z0:r.pos++,t={min:0,max:0};break;case ml:r.pos++,t={min:1,max:0};break;case gl:r.pos++,t={min:0,max:1};break;case bl:if(r.pos++,i=!0,r.charCode()===io)t=H0(r);else if(r.charCode()===gl)r.pos++,t={min:0,max:0};else t={min:1,max:0};break;case io:t=H0(r);break;default:return null}return{type:"Multiplier",comma:i,min:t.min,max:t.max,term:null}}function ht(r,t){let i=T1(r);if(i!==null){if(i.term=t,r.charCode()===bl&&r.charCodeAt(r.pos-1)===ml)return ht(r,i);return i}return t}function cl(r){let t=r.peek();if(t==="")return null;return ht(r,{type:"Token",value:t})}function d1(r){let t;return r.eat(hl),r.eat(no),t=r.scanWord(),r.eat(no),r.eat(ul),ht(r,{type:"Property",name:t})}function s1(r){let t=null,i=null,o=1;if(r.eat(Wn),r.charCode()===S0)r.peek(),o=-1;if(o==-1&&r.charCode()===y0)r.peek();else if(t=o*Number(r.scanNumber(r)),r.isNameCharCode())t+=r.scanWord();if(r.skipWs(),r.eat(vl),r.skipWs(),r.charCode()===y0)r.peek();else{if(o=1,r.charCode()===S0)r.peek(),o=-1;if(i=o*Number(r.scanNumber(r)),r.isNameCharCode())i+=r.scanWord()}return r.eat(Kn),{type:"Range",min:t,max:i}}function r2(r){let t,i=null;if(r.eat(hl),t=r.scanWord(),t==="boolean-expr"){r.eat(Wn);let o=$l(r,Kn);return r.eat(Kn),r.eat(ul),ht(r,{type:"Boolean",term:o.terms.length===1?o.terms[0]:o})}if(r.charCode()===M0&&r.nextCharCode()===Z1)r.pos+=2,t+="()";if(r.charCodeAt(r.findWsEnd(r.pos))===Wn)r.skipWs(),i=s1(r);return r.eat(ul),ht(r,{type:"Type",name:t,opts:i})}function t2(r){let t=r.scanWord();if(r.charCode()===M0)return r.pos++,{type:"Function",name:t};return ht(r,{type:"Keyword",name:t})}function n2(r,t){function i(n,e){return{type:"Group",terms:n,combinator:e,disallowEmpty:!1,explicit:!1}}let o;t=Object.keys(t).sort((n,e)=>A0[n]-A0[e]);while(t.length>0){o=t.shift();let n=0,e=0;for(;n<r.length;n++){let l=r[n];if(l.type==="Combinator")if(l.value===o){if(e===-1)e=n-1;r.splice(n,1),n--}else{if(e!==-1&&n-e>1)r.splice(e,n-e,i(r.slice(e,n),o)),n=e+1;e=-1}}if(e!==-1&&t.length)r.splice(e,n-e,i(r.slice(e,n),o))}return o}function $l(r,t){let i=Object.create(null),o=[],n,e=null,l=r.pos;while(r.charCode()!==t&&(n=o2(r,t)))if(n.type!=="Spaces"){if(n.type==="Combinator"){if(e===null||e.type==="Combinator")r.pos=l,r.error("Unexpected combinator");i[n.value]=!0}else if(e!==null&&e.type!=="Combinator")i[" "]=!0,o.push({type:"Combinator",value:" "});o.push(n),e=n,l=r.pos}if(e!==null&&e.type==="Combinator")r.pos-=l,r.error("Unexpected combinator");return{type:"Group",terms:o,combinator:n2(o,i)||" ",disallowEmpty:!1,explicit:!1}}function i2(r,t){let i;if(r.eat(Wn),i=$l(r,t),r.eat(Kn),i.explicit=!0,r.charCode()===R0)r.pos++,i.disallowEmpty=!0;return i}function o2(r,t){let i=r.charCode();switch(i){case Kn:break;case Wn:return ht(r,i2(r,t));case hl:return r.nextCharCode()===no?d1(r):r2(r);case N0:return{type:"Combinator",value:r.substringToPos(r.pos+(r.nextCharCode()===N0?2:1))};case G0:return r.pos++,r.eat(G0),{type:"Combinator",value:"&&"};case vl:return r.pos++,{type:"Comma"};case no:return ht(r,{type:"String",value:r.scanString()});case M1:case y1:case A1:case R1:case H1:return{type:"Spaces",value:r.scanSpaces()};case C1:if(i=r.nextCharCode(),r.isNameCharCode(i))return r.pos++,{type:"AtKeyword",name:r.scanWord()};return cl(r);case Z0:case ml:case gl:case bl:case R0:break;case io:if(i=r.nextCharCode(),i<48||i>57)return cl(r);break;default:if(r.isNameCharCode(i))return t2(r);return cl(r)}}function Vn(r){let t=new ll(r),i=$l(t);if(t.pos!==r.length)t.error("Unexpected input");if(i.terms.length===1&&i.terms[0].type==="Group")return i.terms[0];return i}var Ln=function(){};function C0(r){return typeof r==="function"?r:Ln}function xl(r,t,i){function o(l){switch(n.call(i,l),l.type){case"Group":l.terms.forEach(o);break;case"Multiplier":case"Boolean":o(l.term);break;case"Type":case"Property":case"Keyword":case"AtKeyword":case"Function":case"String":case"Token":case"Comma":break;default:throw new Error("Unknown type: "+l.type)}e.call(i,l)}let n=Ln,e=Ln;if(typeof t==="function")n=t;else if(t)n=C0(t.enter),e=C0(t.leave);if(n===Ln&&e===Ln)throw new Error("Neither `enter` nor `leave` walker handler is set or both aren't a function");o(r,i)}var l2={decorator(r){let t=[],i=null;return{...r,node(o){let n=i;i=o,r.node.call(this,o),i=n},emit(o,n,e){t.push({type:n,value:o,node:e?null:i})},result(){return t}}}};function c2(r){let t=[];return mt(r,(i,o,n)=>t.push({type:i,value:r.slice(o,n),node:null})),t}function wl(r,t){if(typeof r==="string")return c2(r);return t.generate(r,l2)}var N={type:"Match"},H={type:"Mismatch"},oo={type:"DisallowEmpty"},u2=40,g2=41;function Dr(r,t,i){if(t===N&&i===H)return r;if(r===N&&t===N&&i===N)return r;if(r.type==="If"&&r.else===H&&t===N)t=r.then,r=r.match;return{type:"If",match:r,then:t,else:i}}function d0(r){return r.length>2&&r.charCodeAt(r.length-2)===u2&&r.charCodeAt(r.length-1)===g2}function T0(r){return r.type==="Keyword"||r.type==="AtKeyword"||r.type==="Function"||r.type==="Type"&&d0(r.name)}function $t(r,t=" ",i=!1){return{type:"Group",terms:r,combinator:t,disallowEmpty:!1,explicit:i}}function Yn(r,t,i=new Set){if(!i.has(r))switch(i.add(r),r.type){case"If":r.match=Yn(r.match,t,i),r.then=Yn(r.then,t,i),r.else=Yn(r.else,t,i);break;case"Type":return t[r.name]||r}return r}function fl(r,t,i){switch(r){case" ":{let o=N;for(let n=t.length-1;n>=0;n--){let e=t[n];o=Dr(e,o,H)}return o}case"|":{let o=H,n=null;for(let e=t.length-1;e>=0;e--){let l=t[e];if(T0(l)){if(n===null&&e>0&&T0(t[e-1]))n=Object.create(null),o=Dr({type:"Enum",map:n},N,o);if(n!==null){let u=(d0(l.name)?l.name.slice(0,-1):l.name).toLowerCase();if(u in n===!1){n[u]=l;continue}}}n=null,o=Dr(l,N,o)}return o}case"&&":{if(t.length>5)return{type:"MatchOnce",terms:t,all:!0};let o=H;for(let n=t.length-1;n>=0;n--){let e=t[n],l;if(t.length>1)l=fl(r,t.filter(function(u){return u!==e}),!1);else l=N;o=Dr(e,l,o)}return o}case"||":{if(t.length>5)return{type:"MatchOnce",terms:t,all:!1};let o=i?N:H;for(let n=t.length-1;n>=0;n--){let e=t[n],l;if(t.length>1)l=fl(r,t.filter(function(u){return u!==e}),!0);else l=N;o=Dr(e,l,o)}return o}}}function b2(r){let t=N,i=Tt(r.term);if(r.max===0){if(i=Dr(i,oo,H),t=Dr(i,null,H),t.then=Dr(N,N,t),r.comma)t.then.else=Dr({type:"Comma",syntax:r},t,H)}else for(let o=r.min||1;o<=r.max;o++){if(r.comma&&t!==N)t=Dr({type:"Comma",syntax:r},t,H);t=Dr(i,Dr(N,N,t),H)}if(r.min===0)t=Dr(N,N,t);else for(let o=0;o<r.min-1;o++){if(r.comma&&t!==N)t=Dr({type:"Comma",syntax:r},t,H);t=Dr(i,t,H)}return t}function Tt(r){if(typeof r==="function")return{type:"Generic",fn:r};switch(r.type){case"Group":{let t=fl(r.combinator,r.terms.map(Tt),!1);if(r.disallowEmpty)t=Dr(t,oo,H);return t}case"Multiplier":return b2(r);case"Boolean":{let t=Tt(r.term),i=Tt($t([$t([{type:"Keyword",name:"not"},{type:"Type",name:"!boolean-group"}]),$t([{type:"Type",name:"!boolean-group"},$t([{type:"Multiplier",comma:!1,min:0,max:0,term:$t([{type:"Keyword",name:"and"},{type:"Type",name:"!boolean-group"}])},{type:"Multiplier",comma:!1,min:0,max:0,term:$t([{type:"Keyword",name:"or"},{type:"Type",name:"!boolean-group"}])}],"|")])],"|")),o=Tt($t([{type:"Type",name:"!term"},$t([{type:"Token",value:"("},{type:"Type",name:"!self"},{type:"Token",value:")"}]),{type:"Type",name:"general-enclosed"}],"|"));return Yn(o,{"!term":t,"!self":i}),Yn(i,{"!boolean-group":o}),i}case"Type":case"Property":return{type:r.type,name:r.name,syntax:r};case"Keyword":return{type:r.type,name:r.name.toLowerCase(),syntax:r};case"AtKeyword":return{type:r.type,name:"@"+r.name.toLowerCase(),syntax:r};case"Function":return{type:r.type,name:r.name.toLowerCase()+"(",syntax:r};case"String":if(r.value.length===3)return{type:"Token",value:r.value.charAt(1),syntax:r};return{type:r.type,value:r.value.substr(1,r.value.length-2).replace(/\\'/g,"'"),syntax:r};case"Token":return{type:r.type,value:r.value,syntax:r};case"Comma":return{type:r.type,syntax:r};default:throw new Error("Unknown node type:",r.type)}}function En(r,t){if(typeof r==="string")r=Vn(r);return{type:"MatchGraph",match:Tt(r),syntax:t||null,source:r}}var{hasOwnProperty:s0}=Object.prototype,m2=0,v2=1,Dl=2,ox=3,rx="Match",h2="Mismatch",$2="Maximum iteration number exceeded (please fill an issue on https://github.com/csstree/csstree/issues)",tx=15000,x2=0;function w2(r){let t=null,i=null,o=r;while(o!==null)i=o.prev,o.prev=t,t=o,o=i;return t}function zl(r,t){if(r.length!==t.length)return!1;for(let i=0;i<r.length;i++){let o=t.charCodeAt(i),n=r.charCodeAt(i);if(n>=65&&n<=90)n=n|32;if(n!==o)return!1}return!0}function f2(r){if(r.type!==X)return!1;return r.value!=="?"}function nx(r){if(r===null)return!0;return r.type===ir||r.type===U||r.type===K||r.type===br||r.type===Z||f2(r)}function ix(r){if(r===null)return!0;return r.type===p||r.type===fr||r.type===xr||r.type===X&&r.value==="/"}function z2(r,t,i){function o(){do J++,_=J<r.length?r[J]:null;while(_!==null&&(_.type===Y||_.type===A))}function n(W){let Q=J+W;return Q<r.length?r[Q]:null}function e(W,Q){return{nextState:W,matchStack:P,syntaxStack:v,thenStack:h,tokenIndex:J,prev:Q}}function l(W){h={nextState:W,matchStack:P,syntaxStack:v,prev:h}}function u(W){m=e(W,m)}function g(){if(P={type:v2,syntax:t.syntax,token:_,prev:P},o(),w=null,J>L)L=J}function c(){v={syntax:t.syntax,opts:t.syntax.opts||v!==null&&v.opts||null,prev:v},P={type:Dl,syntax:t.syntax,token:P.token,prev:P}}function b(){if(P.type===Dl)P=P.prev;else P={type:ox,syntax:v.syntax,token:P.token,prev:P};v=v.prev}let v=null,h=null,m=null,w=null,O=0,I=null,_=null,J=-1,L=0,P={type:m2,syntax:null,token:null,prev:null};o();while(I===null&&++O<tx)switch(t.type){case"Match":if(h===null){if(_!==null){if(J!==r.length-1||_.value!=="\\0"&&_.value!=="\\9"){t=H;break}}I=rx;break}if(t=h.nextState,t===oo)if(h.matchStack===P){t=H;break}else t=N;while(h.syntaxStack!==v)b();h=h.prev;break;case"Mismatch":if(w!==null&&w!==!1){if(m===null||J>m.tokenIndex)m=w,w=!1}else if(m===null){I=h2;break}t=m.nextState,h=m.thenStack,v=m.syntaxStack,P=m.matchStack,J=m.tokenIndex,_=J<r.length?r[J]:null,m=m.prev;break;case"MatchGraph":t=t.match;break;case"If":if(t.else!==H)u(t.else);if(t.then!==N)l(t.then);t=t.match;break;case"MatchOnce":t={type:"MatchOnceBuffer",syntax:t,index:0,mask:0};break;case"MatchOnceBuffer":{let B=t.syntax.terms;if(t.index===B.length){if(t.mask===0||t.syntax.all){t=H;break}t=N;break}if(t.mask===(1<<B.length)-1){t=N;break}for(;t.index<B.length;t.index++){let tr=1<<t.index;if((t.mask&tr)===0){u(t),l({type:"AddMatchOnce",syntax:t.syntax,mask:t.mask|tr}),t=B[t.index++];break}}break}case"AddMatchOnce":t={type:"MatchOnceBuffer",syntax:t.syntax,index:0,mask:t.mask};break;case"Enum":if(_!==null){let B=_.value.toLowerCase();if(B.indexOf("\\")!==-1)B=B.replace(/\\[09].*$/,"");if(s0.call(t.map,B)){t=t.map[B];break}}t=H;break;case"Generic":{let B=v!==null?v.opts:null,tr=J+Math.floor(t.fn(_,n,B));if(!isNaN(tr)&&tr>J){while(J<tr)g();t=N}else t=H;break}case"Type":case"Property":{let B=t.type==="Type"?"types":"properties",tr=s0.call(i,B)?i[B][t.name]:null;if(!tr||!tr.match)throw new Error("Bad syntax reference: "+(t.type==="Type"?"<"+t.name+">":"<'"+t.name+"'>"));if(w!==!1&&_!==null&&t.type==="Type"){if(t.name==="custom-ident"&&_.type===f||t.name==="length"&&_.value==="0"){if(w===null)w=e(t,m);t=H;break}}c(),t=tr.matchRef||tr.match;break}case"Keyword":{let B=t.name;if(_!==null){let tr=_.value;if(tr.indexOf("\\")!==-1)tr=tr.replace(/\\[09].*$/,"");if(zl(tr,B)){g(),t=N;break}}t=H;break}case"AtKeyword":case"Function":if(_!==null&&zl(_.value,t.name)){g(),t=N;break}t=H;break;case"Token":if(_!==null&&_.value===t.value){g(),t=N;break}t=H;break;case"Comma":if(_!==null&&_.type===ir)if(nx(P.token))t=H;else g(),t=ix(_)?H:N;else t=nx(P.token)||ix(_)?N:H;break;case"String":let W="",Q=J;for(;Q<r.length&&W.length<t.value.length;Q++)W+=r[Q].value;if(zl(W,t.value)){while(J<Q)g();t=N}else t=H;break;default:throw new Error("Unknown node type: "+t.type)}switch(x2+=O,I){case null:console.warn("[csstree-match] BREAK after "+tx+" iterations"),I=$2,P=null;break;case rx:while(v!==null)b();break;default:P=null}return{tokens:r,reason:I,iterations:O,match:P,longestMatch:L}}function _l(r,t,i){let o=z2(r,t,i||{});if(o.match===null)return o;let n=o.match,e=o.match={syntax:t.syntax||null,match:[]},l=[e];n=w2(n).prev;while(n!==null){switch(n.type){case Dl:e.match.push(e={syntax:n.syntax,match:[]}),l.push(e);break;case ox:l.pop(),e=l[l.length-1];break;default:e.match.push({syntax:n.syntax||null,token:n.token.value,node:n.token.node})}n=n.prev}return o}var Ol={};a(Ol,{isType:()=>D2,isProperty:()=>_2,isKeyword:()=>j2,getTrace:()=>ex});function ex(r){function t(n){if(n===null)return!1;return n.type==="Type"||n.type==="Property"||n.type==="Keyword"}function i(n){if(Array.isArray(n.match)){for(let e=0;e<n.match.length;e++)if(i(n.match[e])){if(t(n.syntax))o.unshift(n.syntax);return!0}}else if(n.node===r)return o=t(n.syntax)?[n.syntax]:[],!0;return!1}let o=null;if(this.matched!==null)i(this.matched);return o}function D2(r,t){return jl(this,r,(i)=>i.type==="Type"&&i.name===t)}function _2(r,t){return jl(this,r,(i)=>i.type==="Property"&&i.name===t)}function j2(r){return jl(this,r,(t)=>t.type==="Keyword")}function jl(r,t,i){let o=ex.call(r,t);if(o===null)return!1;return o.some(i)}function lx(r){if("node"in r)return r.node;return lx(r.match[0])}function cx(r){if("node"in r)return r.node;return cx(r.match[r.match.length-1])}function pl(r,t,i,o,n){function e(u){if(u.syntax!==null&&u.syntax.type===o&&u.syntax.name===n){let g=lx(u),c=cx(u);r.syntax.walk(t,function(b,v,h){if(b===g){let m=new or;do{if(m.appendData(v.data),v.data===c)break;v=v.next}while(v!==null);l.push({parent:h,nodes:m})}})}if(Array.isArray(u.match))u.match.forEach(e)}let l=[];if(i.matched!==null)e(i.matched);return l}var{hasOwnProperty:Qn}=Object.prototype;function Il(r){return typeof r==="number"&&isFinite(r)&&Math.floor(r)===r&&r>=0}function ux(r){return Boolean(r)&&Il(r.offset)&&Il(r.line)&&Il(r.column)}function O2(r,t){return function i(o,n){if(!o||o.constructor!==Object)return n(o,"Type of node should be an Object");for(let e in o){let l=!0;if(Qn.call(o,e)===!1)continue;if(e==="type"){if(o.type!==r)n(o,"Wrong node type `"+o.type+"`, expected `"+r+"`")}else if(e==="loc"){if(o.loc===null)continue;else if(o.loc&&o.loc.constructor===Object)if(typeof o.loc.source!=="string")e+=".source";else if(!ux(o.loc.start))e+=".start";else if(!ux(o.loc.end))e+=".end";else continue;l=!1}else if(t.hasOwnProperty(e)){l=!1;for(let u=0;!l&&u<t[e].length;u++){let g=t[e][u];switch(g){case String:l=typeof o[e]==="string";break;case Boolean:l=typeof o[e]==="boolean";break;case null:l=o[e]===null;break;default:if(typeof g==="string")l=o[e]&&o[e].type===g;else if(Array.isArray(g))l=o[e]instanceof or}}}else n(o,"Unknown field `"+e+"` for "+r+" node type");if(!l)n(o,"Bad value for `"+r+"."+e+"`")}for(let e in t)if(Qn.call(t,e)&&Qn.call(o,e)===!1)n(o,"Field `"+r+"."+e+"` is missed")}}function gx(r,t){let i=[];for(let o=0;o<r.length;o++){let n=r[o];if(n===String||n===Boolean)i.push(n.name.toLowerCase());else if(n===null)i.push("null");else if(typeof n==="string")i.push(n);else if(Array.isArray(n))i.push("List<"+(gx(n,t)||"any")+">");else throw new Error("Wrong value `"+n+"` in `"+t+"` structure definition")}return i.join(" | ")}function p2(r,t){let i=t.structure,o={type:String,loc:!0},n={type:'"'+r+'"'};for(let e in i){if(Qn.call(i,e)===!1)continue;let l=o[e]=Array.isArray(i[e])?i[e].slice():[i[e]];n[e]=gx(l,r+"."+e)}return{docs:n,check:O2(r,o)}}function bx(r){let t={};if(r.node){for(let i in r.node)if(Qn.call(r.node,i)){let o=r.node[i];if(o.structure)t[i]=p2(i,o);else throw new Error("Missed `structure` field in `"+i+"` node type definition")}}return t}function al(r,t,i){let o={};for(let n in r)if(r[n].syntax)o[n]=i?r[n].syntax:Ht(r[n].syntax,{compact:t});return o}function I2(r,t,i){let o={};for(let[n,e]of Object.entries(r))o[n]={prelude:e.prelude&&(i?e.prelude.syntax:Ht(e.prelude.syntax,{compact:t})),descriptors:e.descriptors&&al(e.descriptors,t,i)};return o}function a2(r){for(let t=0;t<r.length;t++)if(r[t].value.toLowerCase()==="var(")return!0;return!1}function U2(r){let t=r.terms[0];return r.explicit===!1&&r.terms.length===1&&t.type==="Multiplier"&&t.comma===!0}function Gr(r,t,i){return{matched:r,iterations:i,error:t,...Ol}}function dt(r,t,i,o){let n=wl(i,r.syntax),e;if(a2(n))return Gr(null,new Error("Matching for a tree with var() is not supported"));if(o)e=_l(n,r.cssWideKeywordsSyntax,r);if(!o||!e.match){if(e=_l(n,t.match,r),!e.match)return Gr(null,new X0(e.reason,t.syntax,i,e),e.iterations)}return Gr(e.match,null,e.iterations)}class Fn{constructor(r,t,i){if(this.cssWideKeywords=Zt,this.syntax=t,this.generic=!1,this.units={...to},this.atrules=Object.create(null),this.properties=Object.create(null),this.types=Object.create(null),this.structure=i||bx(r),r){if(r.cssWideKeywords)this.cssWideKeywords=r.cssWideKeywords;if(r.units){for(let o of Object.keys(to))if(Array.isArray(r.units[o]))this.units[o]=r.units[o]}if(r.types)for(let[o,n]of Object.entries(r.types))this.addType_(o,n);if(r.generic){this.generic=!0;for(let[o,n]of Object.entries(Q0(this.units)))this.addType_(o,n)}if(r.atrules)for(let[o,n]of Object.entries(r.atrules))this.addAtrule_(o,n);if(r.properties)for(let[o,n]of Object.entries(r.properties))this.addProperty_(o,n)}this.cssWideKeywordsSyntax=En(this.cssWideKeywords.join(" |  "))}checkStructure(r){function t(n,e){o.push({node:n,message:e})}let i=this.structure,o=[];return this.syntax.walk(r,function(n){if(i.hasOwnProperty(n.type))i[n.type].check(n,t);else t(n,"Unknown node type `"+n.type+"`")}),o.length?o:!1}createDescriptor(r,t,i,o=null){let n={type:t,name:i},e={type:t,name:i,parent:o,serializable:typeof r==="string"||r&&typeof r.type==="string",syntax:null,match:null,matchRef:null};if(typeof r==="function")e.match=En(r,n);else{if(typeof r==="string")Object.defineProperty(e,"syntax",{get(){return Object.defineProperty(e,"syntax",{value:Vn(r)}),e.syntax}});else e.syntax=r;if(Object.defineProperty(e,"match",{get(){return Object.defineProperty(e,"match",{value:En(e.syntax,n)}),e.match}}),t==="Property")Object.defineProperty(e,"matchRef",{get(){let l=e.syntax,u=U2(l)?En({...l,terms:[l.terms[0].term]},n):null;return Object.defineProperty(e,"matchRef",{value:u}),u}})}return e}addAtrule_(r,t){if(!t)return;this.atrules[r]={type:"Atrule",name:r,prelude:t.prelude?this.createDescriptor(t.prelude,"AtrulePrelude",r):null,descriptors:t.descriptors?Object.keys(t.descriptors).reduce((i,o)=>{return i[o]=this.createDescriptor(t.descriptors[o],"AtruleDescriptor",o,r),i},Object.create(null)):null}}addProperty_(r,t){if(!t)return;this.properties[r]=this.createDescriptor(t,"Property",r)}addType_(r,t){if(!t)return;this.types[r]=this.createDescriptor(t,"Type",r)}checkAtruleName(r){if(!this.getAtrule(r))return new Rt("Unknown at-rule","@"+r)}checkAtrulePrelude(r,t){let i=this.checkAtruleName(r);if(i)return i;let o=this.getAtrule(r);if(!o.prelude&&t)return new SyntaxError("At-rule `@"+r+"` should not contain a prelude");if(o.prelude&&!t){if(!dt(this,o.prelude,"",!1).matched)return new SyntaxError("At-rule `@"+r+"` should contain a prelude")}}checkAtruleDescriptorName(r,t){let i=this.checkAtruleName(r);if(i)return i;let o=this.getAtrule(r),n=Ti(t);if(!o.descriptors)return new SyntaxError("At-rule `@"+r+"` has no known descriptors");if(!o.descriptors[n.name]&&!o.descriptors[n.basename])return new Rt("Unknown at-rule descriptor",t)}checkPropertyName(r){if(!this.getProperty(r))return new Rt("Unknown property",r)}matchAtrulePrelude(r,t){let i=this.checkAtrulePrelude(r,t);if(i)return Gr(null,i);let o=this.getAtrule(r);if(!o.prelude)return Gr(null,null);return dt(this,o.prelude,t||"",!1)}matchAtruleDescriptor(r,t,i){let o=this.checkAtruleDescriptorName(r,t);if(o)return Gr(null,o);let n=this.getAtrule(r),e=Ti(t);return dt(this,n.descriptors[e.name]||n.descriptors[e.basename],i,!1)}matchDeclaration(r){if(r.type!=="Declaration")return Gr(null,new Error("Not a Declaration node"));return this.matchProperty(r.property,r.value)}matchProperty(r,t){if(Te(r).custom)return Gr(null,new Error("Lexer matching doesn't applicable for custom properties"));let i=this.checkPropertyName(r);if(i)return Gr(null,i);return dt(this,this.getProperty(r),t,!0)}matchType(r,t){let i=this.getType(r);if(!i)return Gr(null,new Rt("Unknown type",r));return dt(this,i,t,!1)}match(r,t){if(typeof r!=="string"&&(!r||!r.type))return Gr(null,new Rt("Bad syntax"));if(typeof r==="string"||!r.match)r=this.createDescriptor(r,"Type","anonymous");return dt(this,r,t,!1)}findValueFragments(r,t,i,o){return pl(this,t,this.matchProperty(r,t),i,o)}findDeclarationValueFragments(r,t,i){return pl(this,r.value,this.matchDeclaration(r),t,i)}findAllFragments(r,t,i){let o=[];return this.syntax.walk(r,{visit:"Declaration",enter:(n)=>{o.push.apply(o,this.findDeclarationValueFragments(n,t,i))}}),o}getAtrule(r,t=!0){let i=Ti(r);return(i.vendor&&t?this.atrules[i.name]||this.atrules[i.basename]:this.atrules[i.name])||null}getAtrulePrelude(r,t=!0){let i=this.getAtrule(r,t);return i&&i.prelude||null}getAtruleDescriptor(r,t){return this.atrules.hasOwnProperty(r)&&this.atrules.declarators?this.atrules[r].declarators[t]||null:null}getProperty(r,t=!0){let i=Te(r);return(i.vendor&&t?this.properties[i.name]||this.properties[i.basename]:this.properties[i.name])||null}getType(r){return hasOwnProperty.call(this.types,r)?this.types[r]:null}validate(){function r(u,g){return g?`<${u}>`:`<'${u}'>`}function t(u,g,c,b){if(c.has(g))return c.get(g);if(c.set(g,!1),b.syntax!==null)xl(b.syntax,function(v){if(v.type!=="Type"&&v.type!=="Property")return;let h=v.type==="Type"?u.types:u.properties,m=v.type==="Type"?o:n;if(!hasOwnProperty.call(h,v.name))i.push(`${r(g,c===o)} used missed syntax definition ${r(v.name,v.type==="Type")}`),c.set(g,!0);else if(t(u,v.name,m,h[v.name]))i.push(`${r(g,c===o)} used broken syntax definition ${r(v.name,v.type==="Type")}`),c.set(g,!0)},this)}let i=[],o=new Map,n=new Map;for(let u in this.types)t(this,u,o,this.types[u]);for(let u in this.properties)t(this,u,n,this.properties[u]);let e=[...o.keys()].filter((u)=>o.get(u)),l=[...n.keys()].filter((u)=>n.get(u));if(e.length||l.length)return{errors:i,types:e,properties:l};return null}dump(r,t){return{generic:this.generic,cssWideKeywords:this.cssWideKeywords,units:this.units,types:al(this.types,!t,r),properties:al(this.properties,!t,r),atrules:I2(this.atrules,!t,r)}}toString(){return JSON.stringify(this.dump())}}function Ul(r,t){if(typeof t==="string"&&/^\s*\|/.test(t))return typeof r==="string"?r+t:t.replace(/^\s*\|\s*/,"");return t||null}function mx(r,t){let i=Object.create(null);for(let[o,n]of Object.entries(r))if(n){i[o]={};for(let e of Object.keys(n))if(t.includes(e))i[o][e]=n[e]}return i}function Gn(r,t){let i={...r};for(let[o,n]of Object.entries(t))switch(o){case"generic":i[o]=Boolean(n);break;case"cssWideKeywords":i[o]=r[o]?[...r[o],...n]:n||[];break;case"units":i[o]={...r[o]};for(let[e,l]of Object.entries(n))i[o][e]=Array.isArray(l)?l:[];break;case"atrules":i[o]={...r[o]};for(let[e,l]of Object.entries(n)){let u=i[o][e]||{},g=i[o][e]={prelude:u.prelude||null,descriptors:{...u.descriptors}};if(!l)continue;g.prelude=l.prelude?Ul(g.prelude,l.prelude):g.prelude||null;for(let[c,b]of Object.entries(l.descriptors||{}))g.descriptors[c]=b?Ul(g.descriptors[c],b):null;if(!Object.keys(g.descriptors).length)g.descriptors=null}break;case"types":case"properties":i[o]={...r[o]};for(let[e,l]of Object.entries(n))i[o][e]=Ul(i[o][e],l);break;case"scope":case"features":i[o]={...r[o]};for(let[e,l]of Object.entries(n))i[o][e]={...i[o][e],...l};break;case"parseContext":i[o]={...r[o],...n};break;case"atrule":case"pseudo":i[o]={...r[o],...mx(n,["parse"])};break;case"node":i[o]={...r[o],...mx(n,["name","structure","parse","generate","walkContext"])};break}return i}function vx(r){let t=t0(r),i=k0(r),o=j0(r),{fromPlainObject:n,toPlainObject:e}=O0(i),l={lexer:null,createLexer:(u)=>new Fn(u,l,l.lexer.structure),tokenize:mt,parse:t,generate:o,walk:i,find:i.find,findLast:i.findLast,findAll:i.findAll,fromPlainObject:n,toPlainObject:e,fork(u){let g=Gn({},r);return vx(typeof u==="function"?u(g):Gn(g,u))}};return l.lexer=new Fn({generic:r.generic,cssWideKeywords:r.cssWideKeywords,units:r.units,types:r.types,atrules:r.atrules,properties:r.properties,node:r.node},l),l}var kl=(r)=>vx(Gn({},r));var hx={generic:!0,cssWideKeywords:["initial","inherit","unset","revert","revert-layer"],units:{angle:["deg","grad","rad","turn"],decibel:["db"],flex:["fr"],frequency:["hz","khz"],length:["cm","mm","q","in","pt","pc","px","em","rem","ex","rex","cap","rcap","ch","rch","ic","ric","lh","rlh","vw","svw","lvw","dvw","vh","svh","lvh","dvh","vi","svi","lvi","dvi","vb","svb","lvb","dvb","vmin","svmin","lvmin","dvmin","vmax","svmax","lvmax","dvmax","cqw","cqh","cqi","cqb","cqmin","cqmax"],resolution:["dpi","dpcm","dppx","x"],semitones:["st"],time:["s","ms"]},types:{"abs()":"abs( <calc-sum> )","absolute-size":"xx-small|x-small|small|medium|large|x-large|xx-large|xxx-large","acos()":"acos( <calc-sum> )","alpha-value":"<number>|<percentage>","angle-percentage":"<angle>|<percentage>","angular-color-hint":"<angle-percentage>","angular-color-stop":"<color>&&<color-stop-angle>?","angular-color-stop-list":"[<angular-color-stop> [, <angular-color-hint>]?]# , <angular-color-stop>","animateable-feature":"scroll-position|contents|<custom-ident>","asin()":"asin( <calc-sum> )","atan()":"atan( <calc-sum> )","atan2()":"atan2( <calc-sum> , <calc-sum> )",attachment:"scroll|fixed|local","attr()":"attr( <attr-name> <type-or-unit>? [, <attr-fallback>]? )","attr-matcher":"['~'|'|'|'^'|'$'|'*']? '='","attr-modifier":"i|s","attribute-selector":"'[' <wq-name> ']'|'[' <wq-name> <attr-matcher> [<string-token>|<ident-token>] <attr-modifier>? ']'","auto-repeat":"repeat( [auto-fill|auto-fit] , [<line-names>? <fixed-size>]+ <line-names>? )","auto-track-list":"[<line-names>? [<fixed-size>|<fixed-repeat>]]* <line-names>? <auto-repeat> [<line-names>? [<fixed-size>|<fixed-repeat>]]* <line-names>?",axis:"block|inline|x|y","baseline-position":"[first|last]? baseline","basic-shape":"<inset()>|<xywh()>|<rect()>|<circle()>|<ellipse()>|<polygon()>|<path()>","bg-image":"none|<image>","bg-layer":"<bg-image>||<bg-position> [/ <bg-size>]?||<repeat-style>||<attachment>||<box>||<box>","bg-position":"[[left|center|right|top|bottom|<length-percentage>]|[left|center|right|<length-percentage>] [top|center|bottom|<length-percentage>]|[center|[left|right] <length-percentage>?]&&[center|[top|bottom] <length-percentage>?]]","bg-size":"[<length-percentage>|auto]{1,2}|cover|contain","blur()":"blur( <length> )","blend-mode":"normal|multiply|screen|overlay|darken|lighten|color-dodge|color-burn|hard-light|soft-light|difference|exclusion|hue|saturation|color|luminosity",box:"border-box|padding-box|content-box","brightness()":"brightness( <number-percentage> )","calc()":"calc( <calc-sum> )","calc-sum":"<calc-product> [['+'|'-'] <calc-product>]*","calc-product":"<calc-value> ['*' <calc-value>|'/' <number>]*","calc-value":"<number>|<dimension>|<percentage>|<calc-constant>|( <calc-sum> )","calc-constant":"e|pi|infinity|-infinity|NaN","cf-final-image":"<image>|<color>","cf-mixing-image":"<percentage>?&&<image>","circle()":"circle( [<shape-radius>]? [at <position>]? )","clamp()":"clamp( <calc-sum>#{3} )","class-selector":"'.' <ident-token>","clip-source":"<url>",color:"<color-base>|currentColor|<system-color>|<device-cmyk()>|<light-dark()>|<-non-standard-color>","color-stop":"<color-stop-length>|<color-stop-angle>","color-stop-angle":"<angle-percentage>{1,2}","color-stop-length":"<length-percentage>{1,2}","color-stop-list":"[<linear-color-stop> [, <linear-color-hint>]?]# , <linear-color-stop>","color-interpolation-method":"in [<rectangular-color-space>|<polar-color-space> <hue-interpolation-method>?|<custom-color-space>]",combinator:"'>'|'+'|'~'|['|' '|']","common-lig-values":"[common-ligatures|no-common-ligatures]","compat-auto":"searchfield|textarea|push-button|slider-horizontal|checkbox|radio|square-button|menulist|listbox|meter|progress-bar|button","composite-style":"clear|copy|source-over|source-in|source-out|source-atop|destination-over|destination-in|destination-out|destination-atop|xor","compositing-operator":"add|subtract|intersect|exclude","compound-selector":"[<type-selector>? <subclass-selector>*]!","compound-selector-list":"<compound-selector>#","complex-selector":"<complex-selector-unit> [<combinator>? <complex-selector-unit>]*","complex-selector-list":"<complex-selector>#","conic-gradient()":"conic-gradient( [from <angle>]? [at <position>]? , <angular-color-stop-list> )","contextual-alt-values":"[contextual|no-contextual]","content-distribution":"space-between|space-around|space-evenly|stretch","content-list":"[<string>|contents|<image>|<counter>|<quote>|<target>|<leader()>|<attr()>]+","content-position":"center|start|end|flex-start|flex-end","content-replacement":"<image>","contrast()":"contrast( [<number-percentage>] )","cos()":"cos( <calc-sum> )",counter:"<counter()>|<counters()>","counter()":"counter( <counter-name> , <counter-style>? )","counter-name":"<custom-ident>","counter-style":"<counter-style-name>|symbols( )","counter-style-name":"<custom-ident>","counters()":"counters( <counter-name> , <string> , <counter-style>? )","cross-fade()":"cross-fade( <cf-mixing-image> , <cf-final-image>? )","cubic-bezier-timing-function":"ease|ease-in|ease-out|ease-in-out|cubic-bezier( <number [0,1]> , <number> , <number [0,1]> , <number> )","deprecated-system-color":"ActiveBorder|ActiveCaption|AppWorkspace|Background|ButtonFace|ButtonHighlight|ButtonShadow|ButtonText|CaptionText|GrayText|Highlight|HighlightText|InactiveBorder|InactiveCaption|InactiveCaptionText|InfoBackground|InfoText|Menu|MenuText|Scrollbar|ThreeDDarkShadow|ThreeDFace|ThreeDHighlight|ThreeDLightShadow|ThreeDShadow|Window|WindowFrame|WindowText","discretionary-lig-values":"[discretionary-ligatures|no-discretionary-ligatures]","display-box":"contents|none","display-inside":"flow|flow-root|table|flex|grid|ruby","display-internal":"table-row-group|table-header-group|table-footer-group|table-row|table-cell|table-column-group|table-column|table-caption|ruby-base|ruby-text|ruby-base-container|ruby-text-container","display-legacy":"inline-block|inline-list-item|inline-table|inline-flex|inline-grid","display-listitem":"<display-outside>?&&[flow|flow-root]?&&list-item","display-outside":"block|inline|run-in","drop-shadow()":"drop-shadow( <length>{2,3} <color>? )","east-asian-variant-values":"[jis78|jis83|jis90|jis04|simplified|traditional]","east-asian-width-values":"[full-width|proportional-width]","element()":"element( <custom-ident> , [first|start|last|first-except]? )|element( <id-selector> )","ellipse()":"ellipse( [<shape-radius>{2}]? [at <position>]? )","ending-shape":"circle|ellipse","env()":"env( <custom-ident> , <declaration-value>? )","exp()":"exp( <calc-sum> )","explicit-track-list":"[<line-names>? <track-size>]+ <line-names>?","family-name":"<string>|<custom-ident>+","feature-tag-value":"<string> [<integer>|on|off]?","feature-type":"@stylistic|@historical-forms|@styleset|@character-variant|@swash|@ornaments|@annotation","feature-value-block":"<feature-type> '{' <feature-value-declaration-list> '}'","feature-value-block-list":"<feature-value-block>+","feature-value-declaration":"<custom-ident> : <integer>+ ;","feature-value-declaration-list":"<feature-value-declaration>","feature-value-name":"<custom-ident>","fill-rule":"nonzero|evenodd","filter-function":"<blur()>|<brightness()>|<contrast()>|<drop-shadow()>|<grayscale()>|<hue-rotate()>|<invert()>|<opacity()>|<saturate()>|<sepia()>","filter-function-list":"[<filter-function>|<url>]+","final-bg-layer":"<'background-color'>||<bg-image>||<bg-position> [/ <bg-size>]?||<repeat-style>||<attachment>||<box>||<box>","fixed-breadth":"<length-percentage>","fixed-repeat":"repeat( [<integer [1,∞]>] , [<line-names>? <fixed-size>]+ <line-names>? )","fixed-size":"<fixed-breadth>|minmax( <fixed-breadth> , <track-breadth> )|minmax( <inflexible-breadth> , <fixed-breadth> )","font-stretch-absolute":"normal|ultra-condensed|extra-condensed|condensed|semi-condensed|semi-expanded|expanded|extra-expanded|ultra-expanded|<percentage>","font-variant-css21":"[normal|small-caps]","font-weight-absolute":"normal|bold|<number [1,1000]>","frequency-percentage":"<frequency>|<percentage>","general-enclosed":"[<function-token> <any-value>? )]|[( <any-value>? )]","generic-family":"<generic-script-specific>|<generic-complete>|<generic-incomplete>|<-non-standard-generic-family>","generic-name":"serif|sans-serif|cursive|fantasy|monospace","geometry-box":"<shape-box>|fill-box|stroke-box|view-box",gradient:"<linear-gradient()>|<repeating-linear-gradient()>|<radial-gradient()>|<repeating-radial-gradient()>|<conic-gradient()>|<repeating-conic-gradient()>|<-legacy-gradient>","grayscale()":"grayscale( <number-percentage> )","grid-line":"auto|<custom-ident>|[<integer>&&<custom-ident>?]|[span&&[<integer>||<custom-ident>]]","historical-lig-values":"[historical-ligatures|no-historical-ligatures]","hsl()":"hsl( <hue> <percentage> <percentage> [/ <alpha-value>]? )|hsl( <hue> , <percentage> , <percentage> , <alpha-value>? )","hsla()":"hsla( <hue> <percentage> <percentage> [/ <alpha-value>]? )|hsla( <hue> , <percentage> , <percentage> , <alpha-value>? )",hue:"<number>|<angle>","hue-rotate()":"hue-rotate( <angle> )","hue-interpolation-method":"[shorter|longer|increasing|decreasing] hue","hwb()":"hwb( [<hue>|none] [<percentage>|none] [<percentage>|none] [/ [<alpha-value>|none]]? )","hypot()":"hypot( <calc-sum># )",image:"<url>|<image()>|<image-set()>|<element()>|<paint()>|<cross-fade()>|<gradient>","image()":"image( <image-tags>? [<image-src>? , <color>?]! )","image-set()":"image-set( <image-set-option># )","image-set-option":"[<image>|<string>] [<resolution>||type( <string> )]","image-src":"<url>|<string>","image-tags":"ltr|rtl","inflexible-breadth":"<length-percentage>|min-content|max-content|auto","inset()":"inset( <length-percentage>{1,4} [round <'border-radius'>]? )","invert()":"invert( <number-percentage> )","keyframes-name":"<custom-ident>|<string>","keyframe-block":"<keyframe-selector># { <declaration-list> }","keyframe-block-list":"<keyframe-block>+","keyframe-selector":"from|to|<percentage>|<timeline-range-name> <percentage>","lab()":"lab( [<percentage>|<number>|none] [<percentage>|<number>|none] [<percentage>|<number>|none] [/ [<alpha-value>|none]]? )","layer()":"layer( <layer-name> )","layer-name":"<ident> ['.' <ident>]*","lch()":"lch( [<percentage>|<number>|none] [<percentage>|<number>|none] [<hue>|none] [/ [<alpha-value>|none]]? )","leader()":"leader( <leader-type> )","leader-type":"dotted|solid|space|<string>","length-percentage":"<length>|<percentage>","light-dark()":"light-dark( <color> , <color> )","line-names":"'[' <custom-ident>* ']'","line-name-list":"[<line-names>|<name-repeat>]+","line-style":"none|hidden|dotted|dashed|solid|double|groove|ridge|inset|outset","line-width":"<length>|thin|medium|thick","linear-color-hint":"<length-percentage>","linear-color-stop":"<color> <color-stop-length>?","linear-gradient()":"linear-gradient( [[<angle>|to <side-or-corner>]||<color-interpolation-method>]? , <color-stop-list> )","log()":"log( <calc-sum> , <calc-sum>? )","mask-layer":"<mask-reference>||<position> [/ <bg-size>]?||<repeat-style>||<geometry-box>||[<geometry-box>|no-clip]||<compositing-operator>||<masking-mode>","mask-position":"[<length-percentage>|left|center|right] [<length-percentage>|top|center|bottom]?","mask-reference":"none|<image>|<mask-source>","mask-source":"<url>","masking-mode":"alpha|luminance|match-source","matrix()":"matrix( <number>#{6} )","matrix3d()":"matrix3d( <number>#{16} )","max()":"max( <calc-sum># )","media-and":"<media-in-parens> [and <media-in-parens>]+","media-condition":"<media-not>|<media-and>|<media-or>|<media-in-parens>","media-condition-without-or":"<media-not>|<media-and>|<media-in-parens>","media-feature":"( [<mf-plain>|<mf-boolean>|<mf-range>] )","media-in-parens":"( <media-condition> )|<media-feature>|<general-enclosed>","media-not":"not <media-in-parens>","media-or":"<media-in-parens> [or <media-in-parens>]+","media-query":"<media-condition>|[not|only]? <media-type> [and <media-condition-without-or>]?","media-query-list":"<media-query>#","media-type":"<ident>","mf-boolean":"<mf-name>","mf-name":"<ident>","mf-plain":"<mf-name> : <mf-value>","mf-range":"<mf-name> ['<'|'>']? '='? <mf-value>|<mf-value> ['<'|'>']? '='? <mf-name>|<mf-value> '<' '='? <mf-name> '<' '='? <mf-value>|<mf-value> '>' '='? <mf-name> '>' '='? <mf-value>","mf-value":"<number>|<dimension>|<ident>|<ratio>","min()":"min( <calc-sum># )","minmax()":"minmax( [<length-percentage>|min-content|max-content|auto] , [<length-percentage>|<flex>|min-content|max-content|auto] )","mod()":"mod( <calc-sum> , <calc-sum> )","name-repeat":"repeat( [<integer [1,∞]>|auto-fill] , <line-names>+ )","named-color":"transparent|aliceblue|antiquewhite|aqua|aquamarine|azure|beige|bisque|black|blanchedalmond|blue|blueviolet|brown|burlywood|cadetblue|chartreuse|chocolate|coral|cornflowerblue|cornsilk|crimson|cyan|darkblue|darkcyan|darkgoldenrod|darkgray|darkgreen|darkgrey|darkkhaki|darkmagenta|darkolivegreen|darkorange|darkorchid|darkred|darksalmon|darkseagreen|darkslateblue|darkslategray|darkslategrey|darkturquoise|darkviolet|deeppink|deepskyblue|dimgray|dimgrey|dodgerblue|firebrick|floralwhite|forestgreen|fuchsia|gainsboro|ghostwhite|gold|goldenrod|gray|green|greenyellow|grey|honeydew|hotpink|indianred|indigo|ivory|khaki|lavender|lavenderblush|lawngreen|lemonchiffon|lightblue|lightcoral|lightcyan|lightgoldenrodyellow|lightgray|lightgreen|lightgrey|lightpink|lightsalmon|lightseagreen|lightskyblue|lightslategray|lightslategrey|lightsteelblue|lightyellow|lime|limegreen|linen|magenta|maroon|mediumaquamarine|mediumblue|mediumorchid|mediumpurple|mediumseagreen|mediumslateblue|mediumspringgreen|mediumturquoise|mediumvioletred|midnightblue|mintcream|mistyrose|moccasin|navajowhite|navy|oldlace|olive|olivedrab|orange|orangered|orchid|palegoldenrod|palegreen|paleturquoise|palevioletred|papayawhip|peachpuff|peru|pink|plum|powderblue|purple|rebeccapurple|red|rosybrown|royalblue|saddlebrown|salmon|sandybrown|seagreen|seashell|sienna|silver|skyblue|slateblue|slategray|slategrey|snow|springgreen|steelblue|tan|teal|thistle|tomato|turquoise|violet|wheat|white|whitesmoke|yellow|yellowgreen","namespace-prefix":"<ident>","ns-prefix":"[<ident-token>|'*']? '|'","number-percentage":"<number>|<percentage>","numeric-figure-values":"[lining-nums|oldstyle-nums]","numeric-fraction-values":"[diagonal-fractions|stacked-fractions]","numeric-spacing-values":"[proportional-nums|tabular-nums]",nth:"<an-plus-b>|even|odd","opacity()":"opacity( [<number-percentage>] )","overflow-position":"unsafe|safe","outline-radius":"<length>|<percentage>","page-body":"<declaration>? [; <page-body>]?|<page-margin-box> <page-body>","page-margin-box":"<page-margin-box-type> '{' <declaration-list> '}'","page-margin-box-type":"@top-left-corner|@top-left|@top-center|@top-right|@top-right-corner|@bottom-left-corner|@bottom-left|@bottom-center|@bottom-right|@bottom-right-corner|@left-top|@left-middle|@left-bottom|@right-top|@right-middle|@right-bottom","page-selector-list":"[<page-selector>#]?","page-selector":"<pseudo-page>+|<ident> <pseudo-page>*","page-size":"A5|A4|A3|B5|B4|JIS-B5|JIS-B4|letter|legal|ledger","path()":"path( [<fill-rule> ,]? <string> )","paint()":"paint( <ident> , <declaration-value>? )","perspective()":"perspective( [<length [0,∞]>|none] )","polygon()":"polygon( <fill-rule>? , [<length-percentage> <length-percentage>]# )","polar-color-space":"hsl|hwb|lch|oklch",position:"[[left|center|right]||[top|center|bottom]|[left|center|right|<length-percentage>] [top|center|bottom|<length-percentage>]?|[[left|right] <length-percentage>]&&[[top|bottom] <length-percentage>]]","pow()":"pow( <calc-sum> , <calc-sum> )","pseudo-class-selector":"':' <ident-token>|':' <function-token> <any-value> ')'","pseudo-element-selector":"':' <pseudo-class-selector>|<legacy-pseudo-element-selector>","pseudo-page":": [left|right|first|blank]",quote:"open-quote|close-quote|no-open-quote|no-close-quote","radial-gradient()":"radial-gradient( [<ending-shape>||<size>]? [at <position>]? , <color-stop-list> )",ratio:"<number [0,∞]> [/ <number [0,∞]>]?","ray()":"ray( <angle>&&<ray-size>?&&contain?&&[at <position>]? )","ray-size":"closest-side|closest-corner|farthest-side|farthest-corner|sides","rectangular-color-space":"srgb|srgb-linear|display-p3|a98-rgb|prophoto-rgb|rec2020|lab|oklab|xyz|xyz-d50|xyz-d65","relative-selector":"<combinator>? <complex-selector>","relative-selector-list":"<relative-selector>#","relative-size":"larger|smaller","rem()":"rem( <calc-sum> , <calc-sum> )","repeat-style":"repeat-x|repeat-y|[repeat|space|round|no-repeat]{1,2}","repeating-conic-gradient()":"repeating-conic-gradient( [from <angle>]? [at <position>]? , <angular-color-stop-list> )","repeating-linear-gradient()":"repeating-linear-gradient( [<angle>|to <side-or-corner>]? , <color-stop-list> )","repeating-radial-gradient()":"repeating-radial-gradient( [<ending-shape>||<size>]? [at <position>]? , <color-stop-list> )","reversed-counter-name":"reversed( <counter-name> )","rgb()":"rgb( <percentage>{3} [/ <alpha-value>]? )|rgb( <number>{3} [/ <alpha-value>]? )|rgb( <percentage>#{3} , <alpha-value>? )|rgb( <number>#{3} , <alpha-value>? )","rgba()":"rgba( <percentage>{3} [/ <alpha-value>]? )|rgba( <number>{3} [/ <alpha-value>]? )|rgba( <percentage>#{3} , <alpha-value>? )|rgba( <number>#{3} , <alpha-value>? )","rotate()":"rotate( [<angle>|<zero>] )","rotate3d()":"rotate3d( <number> , <number> , <number> , [<angle>|<zero>] )","rotateX()":"rotateX( [<angle>|<zero>] )","rotateY()":"rotateY( [<angle>|<zero>] )","rotateZ()":"rotateZ( [<angle>|<zero>] )","round()":"round( <rounding-strategy>? , <calc-sum> , <calc-sum> )","rounding-strategy":"nearest|up|down|to-zero","saturate()":"saturate( <number-percentage> )","scale()":"scale( [<number>|<percentage>]#{1,2} )","scale3d()":"scale3d( [<number>|<percentage>]#{3} )","scaleX()":"scaleX( [<number>|<percentage>] )","scaleY()":"scaleY( [<number>|<percentage>] )","scaleZ()":"scaleZ( [<number>|<percentage>] )","scroll()":"scroll( [<axis>||<scroller>]? )",scroller:"root|nearest|self","self-position":"center|start|end|self-start|self-end|flex-start|flex-end","shape-radius":"<length-percentage>|closest-side|farthest-side","sign()":"sign( <calc-sum> )","skew()":"skew( [<angle>|<zero>] , [<angle>|<zero>]? )","skewX()":"skewX( [<angle>|<zero>] )","skewY()":"skewY( [<angle>|<zero>] )","sepia()":"sepia( <number-percentage> )",shadow:"inset?&&<length>{2,4}&&<color>?","shadow-t":"[<length>{2,3}&&<color>?]",shape:"rect( <top> , <right> , <bottom> , <left> )|rect( <top> <right> <bottom> <left> )","shape-box":"<box>|margin-box","side-or-corner":"[left|right]||[top|bottom]","sin()":"sin( <calc-sum> )","single-animation":"<'animation-duration'>||<easing-function>||<'animation-delay'>||<single-animation-iteration-count>||<single-animation-direction>||<single-animation-fill-mode>||<single-animation-play-state>||[none|<keyframes-name>]||<single-animation-timeline>","single-animation-direction":"normal|reverse|alternate|alternate-reverse","single-animation-fill-mode":"none|forwards|backwards|both","single-animation-iteration-count":"infinite|<number>","single-animation-play-state":"running|paused","single-animation-timeline":"auto|none|<dashed-ident>|<scroll()>|<view()>","single-transition":"[none|<single-transition-property>]||<time>||<easing-function>||<time>||<transition-behavior-value>","single-transition-property":"all|<custom-ident>",size:"closest-side|farthest-side|closest-corner|farthest-corner|<length>|<length-percentage>{2}","sqrt()":"sqrt( <calc-sum> )","step-position":"jump-start|jump-end|jump-none|jump-both|start|end","step-timing-function":"step-start|step-end|steps( <integer> [, <step-position>]? )","subclass-selector":"<id-selector>|<class-selector>|<attribute-selector>|<pseudo-class-selector>","supports-condition":"not <supports-in-parens>|<supports-in-parens> [and <supports-in-parens>]*|<supports-in-parens> [or <supports-in-parens>]*","supports-in-parens":"( <supports-condition> )|<supports-feature>|<general-enclosed>","supports-feature":"<supports-decl>|<supports-selector-fn>","supports-decl":"( <declaration> )","supports-selector-fn":"selector( <complex-selector> )",symbol:"<string>|<image>|<custom-ident>","system-color":"AccentColor|AccentColorText|ActiveText|ButtonBorder|ButtonFace|ButtonText|Canvas|CanvasText|Field|FieldText|GrayText|Highlight|HighlightText|LinkText|Mark|MarkText|SelectedItem|SelectedItemText|VisitedText","tan()":"tan( <calc-sum> )",target:"<target-counter()>|<target-counters()>|<target-text()>","target-counter()":"target-counter( [<string>|<url>] , <custom-ident> , <counter-style>? )","target-counters()":"target-counters( [<string>|<url>] , <custom-ident> , <string> , <counter-style>? )","target-text()":"target-text( [<string>|<url>] , [content|before|after|first-letter]? )","time-percentage":"<time>|<percentage>","timeline-range-name":"cover|contain|entry|exit|entry-crossing|exit-crossing","easing-function":"linear|<cubic-bezier-timing-function>|<step-timing-function>","track-breadth":"<length-percentage>|<flex>|min-content|max-content|auto","track-list":"[<line-names>? [<track-size>|<track-repeat>]]+ <line-names>?","track-repeat":"repeat( [<integer [1,∞]>] , [<line-names>? <track-size>]+ <line-names>? )","track-size":"<track-breadth>|minmax( <inflexible-breadth> , <track-breadth> )|fit-content( <length-percentage> )","transform-function":"<matrix()>|<translate()>|<translateX()>|<translateY()>|<scale()>|<scaleX()>|<scaleY()>|<rotate()>|<skew()>|<skewX()>|<skewY()>|<matrix3d()>|<translate3d()>|<translateZ()>|<scale3d()>|<scaleZ()>|<rotate3d()>|<rotateX()>|<rotateY()>|<rotateZ()>|<perspective()>","transform-list":"<transform-function>+","transition-behavior-value":"normal|allow-discrete","translate()":"translate( <length-percentage> , <length-percentage>? )","translate3d()":"translate3d( <length-percentage> , <length-percentage> , <length> )","translateX()":"translateX( <length-percentage> )","translateY()":"translateY( <length-percentage> )","translateZ()":"translateZ( <length> )","type-or-unit":"string|color|url|integer|number|length|angle|time|frequency|cap|ch|em|ex|ic|lh|rlh|rem|vb|vi|vw|vh|vmin|vmax|mm|Q|cm|in|pt|pc|px|deg|grad|rad|turn|ms|s|Hz|kHz|%","type-selector":"<wq-name>|<ns-prefix>? '*'","var()":"var( <custom-property-name> , <declaration-value>? )","view()":"view( [<axis>||<'view-timeline-inset'>]? )","viewport-length":"auto|<length-percentage>","visual-box":"content-box|padding-box|border-box","wq-name":"<ns-prefix>? <ident-token>","-legacy-gradient":"<-webkit-gradient()>|<-legacy-linear-gradient>|<-legacy-repeating-linear-gradient>|<-legacy-radial-gradient>|<-legacy-repeating-radial-gradient>","-legacy-linear-gradient":"-moz-linear-gradient( <-legacy-linear-gradient-arguments> )|-webkit-linear-gradient( <-legacy-linear-gradient-arguments> )|-o-linear-gradient( <-legacy-linear-gradient-arguments> )","-legacy-repeating-linear-gradient":"-moz-repeating-linear-gradient( <-legacy-linear-gradient-arguments> )|-webkit-repeating-linear-gradient( <-legacy-linear-gradient-arguments> )|-o-repeating-linear-gradient( <-legacy-linear-gradient-arguments> )","-legacy-linear-gradient-arguments":"[<angle>|<side-or-corner>]? , <color-stop-list>","-legacy-radial-gradient":"-moz-radial-gradient( <-legacy-radial-gradient-arguments> )|-webkit-radial-gradient( <-legacy-radial-gradient-arguments> )|-o-radial-gradient( <-legacy-radial-gradient-arguments> )","-legacy-repeating-radial-gradient":"-moz-repeating-radial-gradient( <-legacy-radial-gradient-arguments> )|-webkit-repeating-radial-gradient( <-legacy-radial-gradient-arguments> )|-o-repeating-radial-gradient( <-legacy-radial-gradient-arguments> )","-legacy-radial-gradient-arguments":"[<position> ,]? [[[<-legacy-radial-gradient-shape>||<-legacy-radial-gradient-size>]|[<length>|<percentage>]{2}] ,]? <color-stop-list>","-legacy-radial-gradient-size":"closest-side|closest-corner|farthest-side|farthest-corner|contain|cover","-legacy-radial-gradient-shape":"circle|ellipse","-non-standard-font":"-apple-system-body|-apple-system-headline|-apple-system-subheadline|-apple-system-caption1|-apple-system-caption2|-apple-system-footnote|-apple-system-short-body|-apple-system-short-headline|-apple-system-short-subheadline|-apple-system-short-caption1|-apple-system-short-footnote|-apple-system-tall-body","-non-standard-color":"-moz-ButtonDefault|-moz-ButtonHoverFace|-moz-ButtonHoverText|-moz-CellHighlight|-moz-CellHighlightText|-moz-Combobox|-moz-ComboboxText|-moz-Dialog|-moz-DialogText|-moz-dragtargetzone|-moz-EvenTreeRow|-moz-Field|-moz-FieldText|-moz-html-CellHighlight|-moz-html-CellHighlightText|-moz-mac-accentdarkestshadow|-moz-mac-accentdarkshadow|-moz-mac-accentface|-moz-mac-accentlightesthighlight|-moz-mac-accentlightshadow|-moz-mac-accentregularhighlight|-moz-mac-accentregularshadow|-moz-mac-chrome-active|-moz-mac-chrome-inactive|-moz-mac-focusring|-moz-mac-menuselect|-moz-mac-menushadow|-moz-mac-menutextselect|-moz-MenuHover|-moz-MenuHoverText|-moz-MenuBarText|-moz-MenuBarHoverText|-moz-nativehyperlinktext|-moz-OddTreeRow|-moz-win-communicationstext|-moz-win-mediatext|-moz-activehyperlinktext|-moz-default-background-color|-moz-default-color|-moz-hyperlinktext|-moz-visitedhyperlinktext|-webkit-activelink|-webkit-focus-ring-color|-webkit-link|-webkit-text","-non-standard-image-rendering":"optimize-contrast|-moz-crisp-edges|-o-crisp-edges|-webkit-optimize-contrast","-non-standard-overflow":"overlay|-moz-scrollbars-none|-moz-scrollbars-horizontal|-moz-scrollbars-vertical|-moz-hidden-unscrollable","-non-standard-size":"intrinsic|min-intrinsic|-webkit-fill-available|-webkit-fit-content|-webkit-min-content|-webkit-max-content|-moz-available|-moz-fit-content|-moz-min-content|-moz-max-content","-webkit-gradient()":"-webkit-gradient( <-webkit-gradient-type> , <-webkit-gradient-point> [, <-webkit-gradient-point>|, <-webkit-gradient-radius> , <-webkit-gradient-point>] [, <-webkit-gradient-radius>]? [, <-webkit-gradient-color-stop>]* )","-webkit-gradient-color-stop":"from( <color> )|color-stop( [<number-zero-one>|<percentage>] , <color> )|to( <color> )","-webkit-gradient-point":"[left|center|right|<length-percentage>] [top|center|bottom|<length-percentage>]","-webkit-gradient-radius":"<length>|<percentage>","-webkit-gradient-type":"linear|radial","-webkit-mask-box-repeat":"repeat|stretch|round","-ms-filter-function-list":"<-ms-filter-function>+","-ms-filter-function":"<-ms-filter-function-progid>|<-ms-filter-function-legacy>","-ms-filter-function-progid":"'progid:' [<ident-token> '.']* [<ident-token>|<function-token> <any-value>? )]","-ms-filter-function-legacy":"<ident-token>|<function-token> <any-value>? )","absolute-color-base":"<hex-color>|<absolute-color-function>|<named-color>|transparent","absolute-color-function":"<rgb()>|<rgba()>|<hsl()>|<hsla()>|<hwb()>|<lab()>|<lch()>|<oklab()>|<oklch()>|<color()>",age:"child|young|old","anchor-name":"<dashed-ident>","attr-name":"<wq-name>","attr-fallback":"<any-value>","bg-clip":"<box>|border|text",bottom:"<length>|auto","container-name":"<custom-ident>","container-condition":"not <query-in-parens>|<query-in-parens> [[and <query-in-parens>]*|[or <query-in-parens>]*]","coord-box":"content-box|padding-box|border-box|fill-box|stroke-box|view-box","generic-voice":"[<age>? <gender> <integer>?]",gender:"male|female|neutral","generic-script-specific":"generic( kai )|generic( fangsong )|generic( nastaliq )","generic-complete":"serif|sans-serif|system-ui|cursive|fantasy|math|monospace","generic-incomplete":"ui-serif|ui-sans-serif|ui-monospace|ui-rounded","-non-standard-generic-family":"-apple-system|BlinkMacSystemFont",left:"<length>|auto","color-base":"<hex-color>|<color-function>|<named-color>|<color-mix()>|transparent","color-function":"<rgb()>|<rgba()>|<hsl()>|<hsla()>|<hwb()>|<lab()>|<lch()>|<oklab()>|<oklch()>|<color()>","device-cmyk()":"<legacy-device-cmyk-syntax>|<modern-device-cmyk-syntax>","legacy-device-cmyk-syntax":"device-cmyk( <number>#{4} )","modern-device-cmyk-syntax":"device-cmyk( <cmyk-component>{4} [/ [<alpha-value>|none]]? )","cmyk-component":"<number>|<percentage>|none","color-mix()":"color-mix( <color-interpolation-method> , [<color>&&<percentage [0,100]>?]#{2} )","color-space":"<rectangular-color-space>|<polar-color-space>|<custom-color-space>","custom-color-space":"<dashed-ident>",paint:"none|<color>|<url> [none|<color>]?|context-fill|context-stroke","palette-identifier":"<dashed-ident>",right:"<length>|auto","scope-start":"<forgiving-selector-list>","scope-end":"<forgiving-selector-list>","forgiving-selector-list":"<complex-real-selector-list>","forgiving-relative-selector-list":"<relative-real-selector-list>","selector-list":"<complex-selector-list>","complex-real-selector-list":"<complex-real-selector>#","simple-selector-list":"<simple-selector>#","relative-real-selector-list":"<relative-real-selector>#","complex-selector-unit":"[<compound-selector>? <pseudo-compound-selector>*]!","complex-real-selector":"<compound-selector> [<combinator>? <compound-selector>]*","relative-real-selector":"<combinator>? <complex-real-selector>","pseudo-compound-selector":"<pseudo-element-selector> <pseudo-class-selector>*","simple-selector":"<type-selector>|<subclass-selector>","legacy-pseudo-element-selector":"':' [before|after|first-line|first-letter]","single-animation-composition":"replace|add|accumulate","svg-length":"<percentage>|<length>|<number>","svg-writing-mode":"lr-tb|rl-tb|tb-rl|lr|rl|tb",top:"<length>|auto",x:"<number>",y:"<number>",declaration:"<ident-token> : <declaration-value>? ['!' important]?","declaration-list":"[<declaration>? ';']* <declaration>?",url:"url( <string> <url-modifier>* )|<url-token>","url-modifier":"<ident>|<function-token> <any-value> )","number-zero-one":"<number [0,1]>","number-one-or-greater":"<number [1,∞]>","color()":"color( <colorspace-params> [/ [<alpha-value>|none]]? )","colorspace-params":"[<predefined-rgb-params>|<xyz-params>]","predefined-rgb-params":"<predefined-rgb> [<number>|<percentage>|none]{3}","predefined-rgb":"srgb|srgb-linear|display-p3|a98-rgb|prophoto-rgb|rec2020","xyz-params":"<xyz-space> [<number>|<percentage>|none]{3}","xyz-space":"xyz|xyz-d50|xyz-d65","oklab()":"oklab( [<percentage>|<number>|none] [<percentage>|<number>|none] [<percentage>|<number>|none] [/ [<alpha-value>|none]]? )","oklch()":"oklch( [<percentage>|<number>|none] [<percentage>|<number>|none] [<hue>|none] [/ [<alpha-value>|none]]? )","offset-path":"<ray()>|<url>|<basic-shape>","rect()":"rect( [<length-percentage>|auto]{4} [round <'border-radius'>]? )","xywh()":"xywh( <length-percentage>{2} <length-percentage [0,∞]>{2} [round <'border-radius'>]? )","query-in-parens":"( <container-condition> )|( <size-feature> )|style( <style-query> )|<general-enclosed>","size-feature":"<mf-plain>|<mf-boolean>|<mf-range>","style-feature":"<declaration>","style-query":"<style-condition>|<style-feature>","style-condition":"not <style-in-parens>|<style-in-parens> [[and <style-in-parens>]*|[or <style-in-parens>]*]","style-in-parens":"( <style-condition> )|( <style-feature> )|<general-enclosed>","-non-standard-display":"-ms-inline-flexbox|-ms-grid|-ms-inline-grid|-webkit-flex|-webkit-inline-flex|-webkit-box|-webkit-inline-box|-moz-inline-stack|-moz-box|-moz-inline-box","inset-area":"[[left|center|right|span-left|span-right|x-start|x-end|span-x-start|span-x-end|x-self-start|x-self-end|span-x-self-start|span-x-self-end|span-all]||[top|center|bottom|span-top|span-bottom|y-start|y-end|span-y-start|span-y-end|y-self-start|y-self-end|span-y-self-start|span-y-self-end|span-all]|[block-start|center|block-end|span-block-start|span-block-end|span-all]||[inline-start|center|inline-end|span-inline-start|span-inline-end|span-all]|[self-block-start|self-block-end|span-self-block-start|span-self-block-end|span-all]||[self-inline-start|self-inline-end|span-self-inline-start|span-self-inline-end|span-all]|[start|center|end|span-start|span-end|span-all]{1,2}|[self-start|center|self-end|span-self-start|span-self-end|span-all]{1,2}]","position-area":"[[left|center|right|span-left|span-right|x-start|x-end|span-x-start|span-x-end|x-self-start|x-self-end|span-x-self-start|span-x-self-end|span-all]||[top|center|bottom|span-top|span-bottom|y-start|y-end|span-y-start|span-y-end|y-self-start|y-self-end|span-y-self-start|span-y-self-end|span-all]|[block-start|center|block-end|span-block-start|span-block-end|span-all]||[inline-start|center|inline-end|span-inline-start|span-inline-end|span-all]|[self-block-start|center|self-block-end|span-self-block-start|span-self-block-end|span-all]||[self-inline-start|center|self-inline-end|span-self-inline-start|span-self-inline-end|span-all]|[start|center|end|span-start|span-end|span-all]{1,2}|[self-start|center|self-end|span-self-start|span-self-end|span-all]{1,2}]","anchor()":"anchor( <anchor-element>?&&<anchor-side> , <length-percentage>? )","anchor-side":"inside|outside|top|left|right|bottom|start|end|self-start|self-end|<percentage>|center","anchor-size()":"anchor-size( [<anchor-element>||<anchor-size>]? , <length-percentage>? )","anchor-size":"width|height|block|inline|self-block|self-inline","anchor-element":"<dashed-ident>","try-size":"most-width|most-height|most-block-size|most-inline-size","try-tactic":"flip-block||flip-inline||flip-start","font-variant-css2":"normal|small-caps","font-width-css3":"normal|ultra-condensed|extra-condensed|condensed|semi-condensed|semi-expanded|expanded|extra-expanded|ultra-expanded","system-family-name":"caption|icon|menu|message-box|small-caption|status-bar"},properties:{"--*":"<declaration-value>","-ms-accelerator":"false|true","-ms-block-progression":"tb|rl|bt|lr","-ms-content-zoom-chaining":"none|chained","-ms-content-zooming":"none|zoom","-ms-content-zoom-limit":"<'-ms-content-zoom-limit-min'> <'-ms-content-zoom-limit-max'>","-ms-content-zoom-limit-max":"<percentage>","-ms-content-zoom-limit-min":"<percentage>","-ms-content-zoom-snap":"<'-ms-content-zoom-snap-type'>||<'-ms-content-zoom-snap-points'>","-ms-content-zoom-snap-points":"snapInterval( <percentage> , <percentage> )|snapList( <percentage># )","-ms-content-zoom-snap-type":"none|proximity|mandatory","-ms-filter":"<string>","-ms-flow-from":"[none|<custom-ident>]#","-ms-flow-into":"[none|<custom-ident>]#","-ms-grid-columns":"none|<track-list>|<auto-track-list>","-ms-grid-rows":"none|<track-list>|<auto-track-list>","-ms-high-contrast-adjust":"auto|none","-ms-hyphenate-limit-chars":"auto|<integer>{1,3}","-ms-hyphenate-limit-lines":"no-limit|<integer>","-ms-hyphenate-limit-zone":"<percentage>|<length>","-ms-ime-align":"auto|after","-ms-overflow-style":"auto|none|scrollbar|-ms-autohiding-scrollbar","-ms-scrollbar-3dlight-color":"<color>","-ms-scrollbar-arrow-color":"<color>","-ms-scrollbar-base-color":"<color>","-ms-scrollbar-darkshadow-color":"<color>","-ms-scrollbar-face-color":"<color>","-ms-scrollbar-highlight-color":"<color>","-ms-scrollbar-shadow-color":"<color>","-ms-scrollbar-track-color":"<color>","-ms-scroll-chaining":"chained|none","-ms-scroll-limit":"<'-ms-scroll-limit-x-min'> <'-ms-scroll-limit-y-min'> <'-ms-scroll-limit-x-max'> <'-ms-scroll-limit-y-max'>","-ms-scroll-limit-x-max":"auto|<length>","-ms-scroll-limit-x-min":"<length>","-ms-scroll-limit-y-max":"auto|<length>","-ms-scroll-limit-y-min":"<length>","-ms-scroll-rails":"none|railed","-ms-scroll-snap-points-x":"snapInterval( <length-percentage> , <length-percentage> )|snapList( <length-percentage># )","-ms-scroll-snap-points-y":"snapInterval( <length-percentage> , <length-percentage> )|snapList( <length-percentage># )","-ms-scroll-snap-type":"none|proximity|mandatory","-ms-scroll-snap-x":"<'-ms-scroll-snap-type'> <'-ms-scroll-snap-points-x'>","-ms-scroll-snap-y":"<'-ms-scroll-snap-type'> <'-ms-scroll-snap-points-y'>","-ms-scroll-translation":"none|vertical-to-horizontal","-ms-text-autospace":"none|ideograph-alpha|ideograph-numeric|ideograph-parenthesis|ideograph-space","-ms-touch-select":"grippers|none","-ms-user-select":"none|element|text","-ms-wrap-flow":"auto|both|start|end|maximum|clear","-ms-wrap-margin":"<length>","-ms-wrap-through":"wrap|none","-moz-appearance":"none|button|button-arrow-down|button-arrow-next|button-arrow-previous|button-arrow-up|button-bevel|button-focus|caret|checkbox|checkbox-container|checkbox-label|checkmenuitem|dualbutton|groupbox|listbox|listitem|menuarrow|menubar|menucheckbox|menuimage|menuitem|menuitemtext|menulist|menulist-button|menulist-text|menulist-textfield|menupopup|menuradio|menuseparator|meterbar|meterchunk|progressbar|progressbar-vertical|progresschunk|progresschunk-vertical|radio|radio-container|radio-label|radiomenuitem|range|range-thumb|resizer|resizerpanel|scale-horizontal|scalethumbend|scalethumb-horizontal|scalethumbstart|scalethumbtick|scalethumb-vertical|scale-vertical|scrollbarbutton-down|scrollbarbutton-left|scrollbarbutton-right|scrollbarbutton-up|scrollbarthumb-horizontal|scrollbarthumb-vertical|scrollbartrack-horizontal|scrollbartrack-vertical|searchfield|separator|sheet|spinner|spinner-downbutton|spinner-textfield|spinner-upbutton|splitter|statusbar|statusbarpanel|tab|tabpanel|tabpanels|tab-scroll-arrow-back|tab-scroll-arrow-forward|textfield|textfield-multiline|toolbar|toolbarbutton|toolbarbutton-dropdown|toolbargripper|toolbox|tooltip|treeheader|treeheadercell|treeheadersortarrow|treeitem|treeline|treetwisty|treetwistyopen|treeview|-moz-mac-unified-toolbar|-moz-win-borderless-glass|-moz-win-browsertabbar-toolbox|-moz-win-communicationstext|-moz-win-communications-toolbox|-moz-win-exclude-glass|-moz-win-glass|-moz-win-mediatext|-moz-win-media-toolbox|-moz-window-button-box|-moz-window-button-box-maximized|-moz-window-button-close|-moz-window-button-maximize|-moz-window-button-minimize|-moz-window-button-restore|-moz-window-frame-bottom|-moz-window-frame-left|-moz-window-frame-right|-moz-window-titlebar|-moz-window-titlebar-maximized","-moz-binding":"<url>|none","-moz-border-bottom-colors":"<color>+|none","-moz-border-left-colors":"<color>+|none","-moz-border-right-colors":"<color>+|none","-moz-border-top-colors":"<color>+|none","-moz-context-properties":"none|[fill|fill-opacity|stroke|stroke-opacity]#","-moz-float-edge":"border-box|content-box|margin-box|padding-box","-moz-force-broken-image-icon":"0|1","-moz-image-region":"<shape>|auto","-moz-orient":"inline|block|horizontal|vertical","-moz-outline-radius":"<outline-radius>{1,4} [/ <outline-radius>{1,4}]?","-moz-outline-radius-bottomleft":"<outline-radius>","-moz-outline-radius-bottomright":"<outline-radius>","-moz-outline-radius-topleft":"<outline-radius>","-moz-outline-radius-topright":"<outline-radius>","-moz-stack-sizing":"ignore|stretch-to-fit","-moz-text-blink":"none|blink","-moz-user-focus":"ignore|normal|select-after|select-before|select-menu|select-same|select-all|none","-moz-user-input":"auto|none|enabled|disabled","-moz-user-modify":"read-only|read-write|write-only","-moz-window-dragging":"drag|no-drag","-moz-window-shadow":"default|menu|tooltip|sheet|none","-webkit-appearance":"none|button|button-bevel|caps-lock-indicator|caret|checkbox|default-button|inner-spin-button|listbox|listitem|media-controls-background|media-controls-fullscreen-background|media-current-time-display|media-enter-fullscreen-button|media-exit-fullscreen-button|media-fullscreen-button|media-mute-button|media-overlay-play-button|media-play-button|media-seek-back-button|media-seek-forward-button|media-slider|media-sliderthumb|media-time-remaining-display|media-toggle-closed-captions-button|media-volume-slider|media-volume-slider-container|media-volume-sliderthumb|menulist|menulist-button|menulist-text|menulist-textfield|meter|progress-bar|progress-bar-value|push-button|radio|scrollbarbutton-down|scrollbarbutton-left|scrollbarbutton-right|scrollbarbutton-up|scrollbargripper-horizontal|scrollbargripper-vertical|scrollbarthumb-horizontal|scrollbarthumb-vertical|scrollbartrack-horizontal|scrollbartrack-vertical|searchfield|searchfield-cancel-button|searchfield-decoration|searchfield-results-button|searchfield-results-decoration|slider-horizontal|slider-vertical|sliderthumb-horizontal|sliderthumb-vertical|square-button|textarea|textfield|-apple-pay-button","-webkit-border-before":"<'border-width'>||<'border-style'>||<color>","-webkit-border-before-color":"<color>","-webkit-border-before-style":"<'border-style'>","-webkit-border-before-width":"<'border-width'>","-webkit-box-reflect":"[above|below|right|left]? <length>? <image>?","-webkit-line-clamp":"none|<integer>","-webkit-mask":"[<mask-reference>||<position> [/ <bg-size>]?||<repeat-style>||[<box>|border|padding|content|text]||[<box>|border|padding|content]]#","-webkit-mask-attachment":"<attachment>#","-webkit-mask-clip":"[<box>|border|padding|content|text]#","-webkit-mask-composite":"<composite-style>#","-webkit-mask-image":"<mask-reference>#","-webkit-mask-origin":"[<box>|border|padding|content]#","-webkit-mask-position":"<position>#","-webkit-mask-position-x":"[<length-percentage>|left|center|right]#","-webkit-mask-position-y":"[<length-percentage>|top|center|bottom]#","-webkit-mask-repeat":"<repeat-style>#","-webkit-mask-repeat-x":"repeat|no-repeat|space|round","-webkit-mask-repeat-y":"repeat|no-repeat|space|round","-webkit-mask-size":"<bg-size>#","-webkit-overflow-scrolling":"auto|touch","-webkit-tap-highlight-color":"<color>","-webkit-text-fill-color":"<color>","-webkit-text-stroke":"<length>||<color>","-webkit-text-stroke-color":"<color>","-webkit-text-stroke-width":"<length>","-webkit-touch-callout":"default|none","-webkit-user-modify":"read-only|read-write|read-write-plaintext-only","accent-color":"auto|<color>","align-content":"normal|<baseline-position>|<content-distribution>|<overflow-position>? <content-position>","align-items":"normal|stretch|<baseline-position>|[<overflow-position>? <self-position>]","align-self":"auto|normal|stretch|<baseline-position>|<overflow-position>? <self-position>","align-tracks":"[normal|<baseline-position>|<content-distribution>|<overflow-position>? <content-position>]#",all:"initial|inherit|unset|revert|revert-layer","anchor-name":"none|<dashed-ident>#","anchor-scope":"none|all|<dashed-ident>#",animation:"<single-animation>#","animation-composition":"<single-animation-composition>#","animation-delay":"<time>#","animation-direction":"<single-animation-direction>#","animation-duration":"<time>#","animation-fill-mode":"<single-animation-fill-mode>#","animation-iteration-count":"<single-animation-iteration-count>#","animation-name":"[none|<keyframes-name>]#","animation-play-state":"<single-animation-play-state>#","animation-range":"[<'animation-range-start'> <'animation-range-end'>?]#","animation-range-end":"[normal|<length-percentage>|<timeline-range-name> <length-percentage>?]#","animation-range-start":"[normal|<length-percentage>|<timeline-range-name> <length-percentage>?]#","animation-timing-function":"<easing-function>#","animation-timeline":"<single-animation-timeline>#",appearance:"none|auto|textfield|menulist-button|<compat-auto>","aspect-ratio":"auto||<ratio>",azimuth:"<angle>|[[left-side|far-left|left|center-left|center|center-right|right|far-right|right-side]||behind]|leftwards|rightwards","backdrop-filter":"none|<filter-function-list>","backface-visibility":"visible|hidden",background:"[<bg-layer> ,]* <final-bg-layer>","background-attachment":"<attachment>#","background-blend-mode":"<blend-mode>#","background-clip":"<bg-clip>#","background-color":"<color>","background-image":"<bg-image>#","background-origin":"<box>#","background-position":"<bg-position>#","background-position-x":"[center|[[left|right|x-start|x-end]? <length-percentage>?]!]#","background-position-y":"[center|[[top|bottom|y-start|y-end]? <length-percentage>?]!]#","background-repeat":"<repeat-style>#","background-size":"<bg-size>#","block-size":"<'width'>",border:"<line-width>||<line-style>||<color>","border-block":"<'border-top-width'>||<'border-top-style'>||<color>","border-block-color":"<'border-top-color'>{1,2}","border-block-style":"<'border-top-style'>","border-block-width":"<'border-top-width'>","border-block-end":"<'border-top-width'>||<'border-top-style'>||<color>","border-block-end-color":"<'border-top-color'>","border-block-end-style":"<'border-top-style'>","border-block-end-width":"<'border-top-width'>","border-block-start":"<'border-top-width'>||<'border-top-style'>||<color>","border-block-start-color":"<'border-top-color'>","border-block-start-style":"<'border-top-style'>","border-block-start-width":"<'border-top-width'>","border-bottom":"<line-width>||<line-style>||<color>","border-bottom-color":"<'border-top-color'>","border-bottom-left-radius":"<length-percentage>{1,2}","border-bottom-right-radius":"<length-percentage>{1,2}","border-bottom-style":"<line-style>","border-bottom-width":"<line-width>","border-collapse":"collapse|separate","border-color":"<color>{1,4}","border-end-end-radius":"<length-percentage>{1,2}","border-end-start-radius":"<length-percentage>{1,2}","border-image":"<'border-image-source'>||<'border-image-slice'> [/ <'border-image-width'>|/ <'border-image-width'>? / <'border-image-outset'>]?||<'border-image-repeat'>","border-image-outset":"[<length>|<number>]{1,4}","border-image-repeat":"[stretch|repeat|round|space]{1,2}","border-image-slice":"<number-percentage>{1,4}&&fill?","border-image-source":"none|<image>","border-image-width":"[<length-percentage>|<number>|auto]{1,4}","border-inline":"<'border-top-width'>||<'border-top-style'>||<color>","border-inline-end":"<'border-top-width'>||<'border-top-style'>||<color>","border-inline-color":"<'border-top-color'>{1,2}","border-inline-style":"<'border-top-style'>","border-inline-width":"<'border-top-width'>","border-inline-end-color":"<'border-top-color'>","border-inline-end-style":"<'border-top-style'>","border-inline-end-width":"<'border-top-width'>","border-inline-start":"<'border-top-width'>||<'border-top-style'>||<color>","border-inline-start-color":"<'border-top-color'>","border-inline-start-style":"<'border-top-style'>","border-inline-start-width":"<'border-top-width'>","border-left":"<line-width>||<line-style>||<color>","border-left-color":"<color>","border-left-style":"<line-style>","border-left-width":"<line-width>","border-radius":"<length-percentage>{1,4} [/ <length-percentage>{1,4}]?","border-right":"<line-width>||<line-style>||<color>","border-right-color":"<color>","border-right-style":"<line-style>","border-right-width":"<line-width>","border-spacing":"<length> <length>?","border-start-end-radius":"<length-percentage>{1,2}","border-start-start-radius":"<length-percentage>{1,2}","border-style":"<line-style>{1,4}","border-top":"<line-width>||<line-style>||<color>","border-top-color":"<color>","border-top-left-radius":"<length-percentage>{1,2}","border-top-right-radius":"<length-percentage>{1,2}","border-top-style":"<line-style>","border-top-width":"<line-width>","border-width":"<line-width>{1,4}",bottom:"<length>|<percentage>|auto","box-align":"start|center|end|baseline|stretch","box-decoration-break":"slice|clone","box-direction":"normal|reverse|inherit","box-flex":"<number>","box-flex-group":"<integer>","box-lines":"single|multiple","box-ordinal-group":"<integer>","box-orient":"horizontal|vertical|inline-axis|block-axis|inherit","box-pack":"start|center|end|justify","box-shadow":"none|<shadow>#","box-sizing":"content-box|border-box","break-after":"auto|avoid|always|all|avoid-page|page|left|right|recto|verso|avoid-column|column|avoid-region|region","break-before":"auto|avoid|always|all|avoid-page|page|left|right|recto|verso|avoid-column|column|avoid-region|region","break-inside":"auto|avoid|avoid-page|avoid-column|avoid-region","caption-side":"top|bottom|block-start|block-end|inline-start|inline-end",caret:"<'caret-color'>||<'caret-shape'>","caret-color":"auto|<color>","caret-shape":"auto|bar|block|underscore",clear:"none|left|right|both|inline-start|inline-end",clip:"<shape>|auto","clip-path":"<clip-source>|[<basic-shape>||<geometry-box>]|none","clip-rule":"nonzero|evenodd",color:"<color>","color-interpolation-filters":"auto|sRGB|linearRGB","color-scheme":"normal|[light|dark|<custom-ident>]+&&only?","column-count":"<integer>|auto","column-fill":"auto|balance","column-gap":"normal|<length-percentage>","column-rule":"<'column-rule-width'>||<'column-rule-style'>||<'column-rule-color'>","column-rule-color":"<color>","column-rule-style":"<'border-style'>","column-rule-width":"<'border-width'>","column-span":"none|all","column-width":"<length>|auto",columns:"<'column-width'>||<'column-count'>",contain:"none|strict|content|[[size||inline-size]||layout||style||paint]","contain-intrinsic-size":"[auto? [none|<length>]]{1,2}","contain-intrinsic-block-size":"auto? [none|<length>]","contain-intrinsic-height":"auto? [none|<length>]","contain-intrinsic-inline-size":"auto? [none|<length>]","contain-intrinsic-width":"auto? [none|<length>]",container:"<'container-name'> [/ <'container-type'>]?","container-name":"none|<custom-ident>+","container-type":"normal||[size|inline-size]",content:"normal|none|[<content-replacement>|<content-list>] [/ [<string>|<counter>]+]?","content-visibility":"visible|auto|hidden","counter-increment":"[<counter-name> <integer>?]+|none","counter-reset":"[<counter-name> <integer>?|<reversed-counter-name> <integer>?]+|none","counter-set":"[<counter-name> <integer>?]+|none",cursor:"[[<url> [<x> <y>]? ,]* [auto|default|none|context-menu|help|pointer|progress|wait|cell|crosshair|text|vertical-text|alias|copy|move|no-drop|not-allowed|e-resize|n-resize|ne-resize|nw-resize|s-resize|se-resize|sw-resize|w-resize|ew-resize|ns-resize|nesw-resize|nwse-resize|col-resize|row-resize|all-scroll|zoom-in|zoom-out|grab|grabbing|hand|-webkit-grab|-webkit-grabbing|-webkit-zoom-in|-webkit-zoom-out|-moz-grab|-moz-grabbing|-moz-zoom-in|-moz-zoom-out]]",d:"none|path( <string> )",cx:"<length>|<percentage>",cy:"<length>|<percentage>",direction:"ltr|rtl",display:"[<display-outside>||<display-inside>]|<display-listitem>|<display-internal>|<display-box>|<display-legacy>|<-non-standard-display>","dominant-baseline":"auto|use-script|no-change|reset-size|ideographic|alphabetic|hanging|mathematical|central|middle|text-after-edge|text-before-edge","empty-cells":"show|hide","field-sizing":"content|fixed",fill:"<paint>","fill-opacity":"<number-zero-one>","fill-rule":"nonzero|evenodd",filter:"none|<filter-function-list>|<-ms-filter-function-list>",flex:"none|[<'flex-grow'> <'flex-shrink'>?||<'flex-basis'>]","flex-basis":"content|<'width'>","flex-direction":"row|row-reverse|column|column-reverse","flex-flow":"<'flex-direction'>||<'flex-wrap'>","flex-grow":"<number>","flex-shrink":"<number>","flex-wrap":"nowrap|wrap|wrap-reverse",float:"left|right|none|inline-start|inline-end",font:"[[<'font-style'>||<font-variant-css2>||<'font-weight'>||<font-width-css3>]? <'font-size'> [/ <'line-height'>]? <'font-family'>#]|<system-family-name>|<-non-standard-font>","font-family":"[<family-name>|<generic-family>]#","font-feature-settings":"normal|<feature-tag-value>#","font-kerning":"auto|normal|none","font-language-override":"normal|<string>","font-optical-sizing":"auto|none","font-palette":"normal|light|dark|<palette-identifier>","font-variation-settings":"normal|[<string> <number>]#","font-size":"<absolute-size>|<relative-size>|<length-percentage>","font-size-adjust":"none|[ex-height|cap-height|ch-width|ic-width|ic-height]? [from-font|<number>]","font-smooth":"auto|never|always|<absolute-size>|<length>","font-stretch":"<font-stretch-absolute>","font-style":"normal|italic|oblique <angle>?","font-synthesis":"none|[weight||style||small-caps||position]","font-synthesis-position":"auto|none","font-synthesis-small-caps":"auto|none","font-synthesis-style":"auto|none","font-synthesis-weight":"auto|none","font-variant":"normal|none|[<common-lig-values>||<discretionary-lig-values>||<historical-lig-values>||<contextual-alt-values>||stylistic( <feature-value-name> )||historical-forms||styleset( <feature-value-name># )||character-variant( <feature-value-name># )||swash( <feature-value-name> )||ornaments( <feature-value-name> )||annotation( <feature-value-name> )||[small-caps|all-small-caps|petite-caps|all-petite-caps|unicase|titling-caps]||<numeric-figure-values>||<numeric-spacing-values>||<numeric-fraction-values>||ordinal||slashed-zero||<east-asian-variant-values>||<east-asian-width-values>||ruby]","font-variant-alternates":"normal|[stylistic( <feature-value-name> )||historical-forms||styleset( <feature-value-name># )||character-variant( <feature-value-name># )||swash( <feature-value-name> )||ornaments( <feature-value-name> )||annotation( <feature-value-name> )]","font-variant-caps":"normal|small-caps|all-small-caps|petite-caps|all-petite-caps|unicase|titling-caps","font-variant-east-asian":"normal|[<east-asian-variant-values>||<east-asian-width-values>||ruby]","font-variant-emoji":"normal|text|emoji|unicode","font-variant-ligatures":"normal|none|[<common-lig-values>||<discretionary-lig-values>||<historical-lig-values>||<contextual-alt-values>]","font-variant-numeric":"normal|[<numeric-figure-values>||<numeric-spacing-values>||<numeric-fraction-values>||ordinal||slashed-zero]","font-variant-position":"normal|sub|super","font-weight":"<font-weight-absolute>|bolder|lighter","forced-color-adjust":"auto|none|preserve-parent-color",gap:"<'row-gap'> <'column-gap'>?",grid:"<'grid-template'>|<'grid-template-rows'> / [auto-flow&&dense?] <'grid-auto-columns'>?|[auto-flow&&dense?] <'grid-auto-rows'>? / <'grid-template-columns'>","grid-area":"<grid-line> [/ <grid-line>]{0,3}","grid-auto-columns":"<track-size>+","grid-auto-flow":"[row|column]||dense","grid-auto-rows":"<track-size>+","grid-column":"<grid-line> [/ <grid-line>]?","grid-column-end":"<grid-line>","grid-column-gap":"<length-percentage>","grid-column-start":"<grid-line>","grid-gap":"<'grid-row-gap'> <'grid-column-gap'>?","grid-row":"<grid-line> [/ <grid-line>]?","grid-row-end":"<grid-line>","grid-row-gap":"<length-percentage>","grid-row-start":"<grid-line>","grid-template":"none|[<'grid-template-rows'> / <'grid-template-columns'>]|[<line-names>? <string> <track-size>? <line-names>?]+ [/ <explicit-track-list>]?","grid-template-areas":"none|<string>+","grid-template-columns":"none|<track-list>|<auto-track-list>|subgrid <line-name-list>?","grid-template-rows":"none|<track-list>|<auto-track-list>|subgrid <line-name-list>?","hanging-punctuation":"none|[first||[force-end|allow-end]||last]",height:"auto|<length>|<percentage>|min-content|max-content|fit-content|fit-content( <length-percentage> )|stretch|<-non-standard-size>","hyphenate-character":"auto|<string>","hyphenate-limit-chars":"[auto|<integer>]{1,3}",hyphens:"none|manual|auto","image-orientation":"from-image|<angle>|[<angle>? flip]","image-rendering":"auto|crisp-edges|pixelated|optimizeSpeed|optimizeQuality|<-non-standard-image-rendering>","image-resolution":"[from-image||<resolution>]&&snap?","ime-mode":"auto|normal|active|inactive|disabled","initial-letter":"normal|[<number> <integer>?]","initial-letter-align":"[auto|alphabetic|hanging|ideographic]","inline-size":"<'width'>","input-security":"auto|none",inset:"<'top'>{1,4}","inset-block":"<'top'>{1,2}","inset-block-end":"<'top'>","inset-block-start":"<'top'>","inset-inline":"<'top'>{1,2}","inset-inline-end":"<'top'>","inset-inline-start":"<'top'>","interpolate-size":"numeric-only|allow-keywords",isolation:"auto|isolate","justify-content":"normal|<content-distribution>|<overflow-position>? [<content-position>|left|right]","justify-items":"normal|stretch|<baseline-position>|<overflow-position>? [<self-position>|left|right]|legacy|legacy&&[left|right|center]","justify-self":"auto|normal|stretch|<baseline-position>|<overflow-position>? [<self-position>|left|right]","justify-tracks":"[normal|<content-distribution>|<overflow-position>? [<content-position>|left|right]]#",left:"<length>|<percentage>|auto","letter-spacing":"normal|<length-percentage>","line-break":"auto|loose|normal|strict|anywhere","line-clamp":"none|<integer>","line-height":"normal|<number>|<length>|<percentage>","line-height-step":"<length>","list-style":"<'list-style-type'>||<'list-style-position'>||<'list-style-image'>","list-style-image":"<image>|none","list-style-position":"inside|outside","list-style-type":"<counter-style>|<string>|none",margin:"[<length>|<percentage>|auto]{1,4}","margin-block":"<'margin-left'>{1,2}","margin-block-end":"<'margin-left'>","margin-block-start":"<'margin-left'>","margin-bottom":"<length>|<percentage>|auto","margin-inline":"<'margin-left'>{1,2}","margin-inline-end":"<'margin-left'>","margin-inline-start":"<'margin-left'>","margin-left":"<length>|<percentage>|auto","margin-right":"<length>|<percentage>|auto","margin-top":"<length>|<percentage>|auto","margin-trim":"none|in-flow|all",marker:"none|<url>","marker-end":"none|<url>","marker-mid":"none|<url>","marker-start":"none|<url>",mask:"<mask-layer>#","mask-border":"<'mask-border-source'>||<'mask-border-slice'> [/ <'mask-border-width'>? [/ <'mask-border-outset'>]?]?||<'mask-border-repeat'>||<'mask-border-mode'>","mask-border-mode":"luminance|alpha","mask-border-outset":"[<length>|<number>]{1,4}","mask-border-repeat":"[stretch|repeat|round|space]{1,2}","mask-border-slice":"<number-percentage>{1,4} fill?","mask-border-source":"none|<image>","mask-border-width":"[<length-percentage>|<number>|auto]{1,4}","mask-clip":"[<geometry-box>|no-clip]#","mask-composite":"<compositing-operator>#","mask-image":"<mask-reference>#","mask-mode":"<masking-mode>#","mask-origin":"<geometry-box>#","mask-position":"<position>#","mask-repeat":"<repeat-style>#","mask-size":"<bg-size>#","mask-type":"luminance|alpha","masonry-auto-flow":"[pack|next]||[definite-first|ordered]","math-depth":"auto-add|add( <integer> )|<integer>","math-shift":"normal|compact","math-style":"normal|compact","max-block-size":"<'max-width'>","max-height":"none|<length-percentage>|min-content|max-content|fit-content|fit-content( <length-percentage> )|stretch|<-non-standard-size>","max-inline-size":"<'max-width'>","max-lines":"none|<integer>","max-width":"none|<length-percentage>|min-content|max-content|fit-content|fit-content( <length-percentage> )|stretch|<-non-standard-size>","min-block-size":"<'min-width'>","min-height":"auto|<length>|<percentage>|min-content|max-content|fit-content|fit-content( <length-percentage> )|stretch|<-non-standard-size>","min-inline-size":"<'min-width'>","min-width":"auto|<length>|<percentage>|min-content|max-content|fit-content|fit-content( <length-percentage> )|stretch|<-non-standard-size>","mix-blend-mode":"<blend-mode>|plus-lighter","object-fit":"fill|contain|cover|none|scale-down","object-position":"<position>",offset:"[<'offset-position'>? [<'offset-path'> [<'offset-distance'>||<'offset-rotate'>]?]?]! [/ <'offset-anchor'>]?","offset-anchor":"auto|<position>","offset-distance":"<length-percentage>","offset-path":"none|<offset-path>||<coord-box>","offset-position":"normal|auto|<position>","offset-rotate":"[auto|reverse]||<angle>",opacity:"<alpha-value>",order:"<integer>",orphans:"<integer>",outline:"[<'outline-width'>||<'outline-style'>||<'outline-color'>]","outline-color":"auto|<color>","outline-offset":"<length>","outline-style":"auto|<'border-style'>","outline-width":"<line-width>",overflow:"[visible|hidden|clip|scroll|auto]{1,2}|<-non-standard-overflow>","overflow-anchor":"auto|none","overflow-block":"visible|hidden|clip|scroll|auto","overflow-clip-box":"padding-box|content-box","overflow-clip-margin":"<visual-box>||<length [0,∞]>","overflow-inline":"visible|hidden|clip|scroll|auto","overflow-wrap":"normal|break-word|anywhere","overflow-x":"visible|hidden|clip|scroll|auto","overflow-y":"visible|hidden|clip|scroll|auto",overlay:"none|auto","overscroll-behavior":"[contain|none|auto]{1,2}","overscroll-behavior-block":"contain|none|auto","overscroll-behavior-inline":"contain|none|auto","overscroll-behavior-x":"contain|none|auto","overscroll-behavior-y":"contain|none|auto",padding:"[<length>|<percentage>]{1,4}","padding-block":"<'padding-left'>{1,2}","padding-block-end":"<'padding-left'>","padding-block-start":"<'padding-left'>","padding-bottom":"<length>|<percentage>","padding-inline":"<'padding-left'>{1,2}","padding-inline-end":"<'padding-left'>","padding-inline-start":"<'padding-left'>","padding-left":"<length>|<percentage>","padding-right":"<length>|<percentage>","padding-top":"<length>|<percentage>",page:"auto|<custom-ident>","page-break-after":"auto|always|avoid|left|right|recto|verso","page-break-before":"auto|always|avoid|left|right|recto|verso","page-break-inside":"auto|avoid","paint-order":"normal|[fill||stroke||markers]",perspective:"none|<length>","perspective-origin":"<position>","place-content":"<'align-content'> <'justify-content'>?","place-items":"<'align-items'> <'justify-items'>?","place-self":"<'align-self'> <'justify-self'>?","pointer-events":"auto|none|visiblePainted|visibleFill|visibleStroke|visible|painted|fill|stroke|all|inherit",position:"static|relative|absolute|sticky|fixed|-webkit-sticky","position-anchor":"auto|<anchor-name>","position-area":"none|<position-area>","position-try":"<'position-try-order'>? <'position-try-fallbacks'>","position-try-fallbacks":"none|[[<dashed-ident>||<try-tactic>]|<'position-area'>]#","position-try-order":"normal|<try-size>","position-visibility":"always|[anchors-valid||anchors-visible||no-overflow]","print-color-adjust":"economy|exact",quotes:"none|auto|[<string> <string>]+",r:"<length>|<percentage>",resize:"none|both|horizontal|vertical|block|inline",right:"<length>|<percentage>|auto",rotate:"none|<angle>|[x|y|z|<number>{3}]&&<angle>","row-gap":"normal|<length-percentage>","ruby-align":"start|center|space-between|space-around","ruby-merge":"separate|collapse|auto","ruby-position":"[alternate||[over|under]]|inter-character",rx:"<length>|<percentage>",ry:"<length>|<percentage>",scale:"none|[<number>|<percentage>]{1,3}","scrollbar-color":"auto|<color>{2}","scrollbar-gutter":"auto|stable&&both-edges?","scrollbar-width":"auto|thin|none","scroll-behavior":"auto|smooth","scroll-margin":"<length>{1,4}","scroll-margin-block":"<length>{1,2}","scroll-margin-block-start":"<length>","scroll-margin-block-end":"<length>","scroll-margin-bottom":"<length>","scroll-margin-inline":"<length>{1,2}","scroll-margin-inline-start":"<length>","scroll-margin-inline-end":"<length>","scroll-margin-left":"<length>","scroll-margin-right":"<length>","scroll-margin-top":"<length>","scroll-padding":"[auto|<length-percentage>]{1,4}","scroll-padding-block":"[auto|<length-percentage>]{1,2}","scroll-padding-block-start":"auto|<length-percentage>","scroll-padding-block-end":"auto|<length-percentage>","scroll-padding-bottom":"auto|<length-percentage>","scroll-padding-inline":"[auto|<length-percentage>]{1,2}","scroll-padding-inline-start":"auto|<length-percentage>","scroll-padding-inline-end":"auto|<length-percentage>","scroll-padding-left":"auto|<length-percentage>","scroll-padding-right":"auto|<length-percentage>","scroll-padding-top":"auto|<length-percentage>","scroll-snap-align":"[none|start|end|center]{1,2}","scroll-snap-coordinate":"none|<position>#","scroll-snap-destination":"<position>","scroll-snap-points-x":"none|repeat( <length-percentage> )","scroll-snap-points-y":"none|repeat( <length-percentage> )","scroll-snap-stop":"normal|always","scroll-snap-type":"none|[x|y|block|inline|both] [mandatory|proximity]?","scroll-snap-type-x":"none|mandatory|proximity","scroll-snap-type-y":"none|mandatory|proximity","scroll-timeline":"[<'scroll-timeline-name'>||<'scroll-timeline-axis'>]#","scroll-timeline-axis":"[block|inline|x|y]#","scroll-timeline-name":"[none|<dashed-ident>]#","shape-image-threshold":"<alpha-value>","shape-margin":"<length-percentage>","shape-outside":"none|[<shape-box>||<basic-shape>]|<image>","shape-rendering":"auto|optimizeSpeed|crispEdges|geometricPrecision",stroke:"<paint>","stroke-dasharray":"none|[<svg-length>+]#","stroke-dashoffset":"<svg-length>","stroke-linecap":"butt|round|square","stroke-linejoin":"miter|round|bevel","stroke-miterlimit":"<number-one-or-greater>","stroke-opacity":"<'opacity'>","stroke-width":"<svg-length>","tab-size":"<integer>|<length>","table-layout":"auto|fixed","text-align":"start|end|left|right|center|justify|match-parent","text-align-last":"auto|start|end|left|right|center|justify","text-anchor":"start|middle|end","text-combine-upright":"none|all|[digits <integer>?]","text-decoration":"<'text-decoration-line'>||<'text-decoration-style'>||<'text-decoration-color'>||<'text-decoration-thickness'>","text-decoration-color":"<color>","text-decoration-line":"none|[underline||overline||line-through||blink]|spelling-error|grammar-error","text-decoration-skip":"none|[objects||[spaces|[leading-spaces||trailing-spaces]]||edges||box-decoration]","text-decoration-skip-ink":"auto|all|none","text-decoration-style":"solid|double|dotted|dashed|wavy","text-decoration-thickness":"auto|from-font|<length>|<percentage>","text-emphasis":"<'text-emphasis-style'>||<'text-emphasis-color'>","text-emphasis-color":"<color>","text-emphasis-position":"auto|[over|under]&&[right|left]?","text-emphasis-style":"none|[[filled|open]||[dot|circle|double-circle|triangle|sesame]]|<string>","text-indent":"<length-percentage>&&hanging?&&each-line?","text-justify":"auto|inter-character|inter-word|none","text-orientation":"mixed|upright|sideways","text-overflow":"[clip|ellipsis|<string>]{1,2}","text-rendering":"auto|optimizeSpeed|optimizeLegibility|geometricPrecision","text-shadow":"none|<shadow-t>#","text-size-adjust":"none|auto|<percentage>","text-spacing-trim":"space-all|normal|space-first|trim-start|trim-both|trim-all|auto","text-transform":"none|capitalize|uppercase|lowercase|full-width|full-size-kana","text-underline-offset":"auto|<length>|<percentage>","text-underline-position":"auto|from-font|[under||[left|right]]","text-wrap":"<'text-wrap-mode'>||<'text-wrap-style'>","text-wrap-mode":"auto|wrap|nowrap","text-wrap-style":"auto|balance|stable|pretty","timeline-scope":"none|<dashed-ident>#",top:"<length>|<percentage>|auto","touch-action":"auto|none|[[pan-x|pan-left|pan-right]||[pan-y|pan-up|pan-down]||pinch-zoom]|manipulation",transform:"none|<transform-list>","transform-box":"content-box|border-box|fill-box|stroke-box|view-box","transform-origin":"[<length-percentage>|left|center|right|top|bottom]|[[<length-percentage>|left|center|right]&&[<length-percentage>|top|center|bottom]] <length>?","transform-style":"flat|preserve-3d",transition:"<single-transition>#","transition-behavior":"<transition-behavior-value>#","transition-delay":"<time>#","transition-duration":"<time>#","transition-property":"none|<single-transition-property>#","transition-timing-function":"<easing-function>#",translate:"none|<length-percentage> [<length-percentage> <length>?]?","unicode-bidi":"normal|embed|isolate|bidi-override|isolate-override|plaintext|-moz-isolate|-moz-isolate-override|-moz-plaintext|-webkit-isolate|-webkit-isolate-override|-webkit-plaintext","user-select":"auto|text|none|contain|all","vector-effect":"none|non-scaling-stroke|non-scaling-size|non-rotation|fixed-position","vertical-align":"baseline|sub|super|text-top|text-bottom|middle|top|bottom|<percentage>|<length>","view-timeline":"[<'view-timeline-name'> <'view-timeline-axis'>?]#","view-timeline-axis":"[block|inline|x|y]#","view-timeline-inset":"[[auto|<length-percentage>]{1,2}]#","view-timeline-name":"none|<dashed-ident>#","view-transition-name":"none|<custom-ident>",visibility:"visible|hidden|collapse","white-space":"normal|pre|nowrap|pre-wrap|pre-line|break-spaces|[<'white-space-collapse'>||<'text-wrap'>||<'white-space-trim'>]","white-space-collapse":"collapse|discard|preserve|preserve-breaks|preserve-spaces|break-spaces",widows:"<integer>",width:"auto|<length>|<percentage>|min-content|max-content|fit-content|fit-content( <length-percentage> )|stretch|<-non-standard-size>","will-change":"auto|<animateable-feature>#","word-break":"normal|break-all|keep-all|break-word|auto-phrase","word-spacing":"normal|<length>","word-wrap":"normal|break-word","writing-mode":"horizontal-tb|vertical-rl|vertical-lr|sideways-rl|sideways-lr|<svg-writing-mode>",x:"<length>|<percentage>",y:"<length>|<percentage>","z-index":"auto|<integer>",zoom:"normal|reset|<number>|<percentage>","-moz-background-clip":"padding|border","-moz-border-radius-bottomleft":"<'border-bottom-left-radius'>","-moz-border-radius-bottomright":"<'border-bottom-right-radius'>","-moz-border-radius-topleft":"<'border-top-left-radius'>","-moz-border-radius-topright":"<'border-bottom-right-radius'>","-moz-control-character-visibility":"visible|hidden","-moz-osx-font-smoothing":"auto|grayscale","-moz-user-select":"none|text|all|-moz-none","-ms-flex-align":"start|end|center|baseline|stretch","-ms-flex-item-align":"auto|start|end|center|baseline|stretch","-ms-flex-line-pack":"start|end|center|justify|distribute|stretch","-ms-flex-negative":"<'flex-shrink'>","-ms-flex-pack":"start|end|center|justify|distribute","-ms-flex-order":"<integer>","-ms-flex-positive":"<'flex-grow'>","-ms-flex-preferred-size":"<'flex-basis'>","-ms-interpolation-mode":"nearest-neighbor|bicubic","-ms-grid-column-align":"start|end|center|stretch","-ms-grid-row-align":"start|end|center|stretch","-ms-hyphenate-limit-last":"none|always|column|page|spread","-webkit-background-clip":"[<box>|border|padding|content|text]#","-webkit-column-break-after":"always|auto|avoid","-webkit-column-break-before":"always|auto|avoid","-webkit-column-break-inside":"always|auto|avoid","-webkit-font-smoothing":"auto|none|antialiased|subpixel-antialiased","-webkit-mask-box-image":"[<url>|<gradient>|none] [<length-percentage>{4} <-webkit-mask-box-repeat>{2}]?","-webkit-print-color-adjust":"economy|exact","-webkit-text-security":"none|circle|disc|square","-webkit-user-drag":"none|element|auto","-webkit-user-select":"auto|none|text|all","alignment-baseline":"auto|baseline|before-edge|text-before-edge|middle|central|after-edge|text-after-edge|ideographic|alphabetic|hanging|mathematical","baseline-shift":"baseline|sub|super|<svg-length>",behavior:"<url>+",cue:"<'cue-before'> <'cue-after'>?","cue-after":"<url> <decibel>?|none","cue-before":"<url> <decibel>?|none","glyph-orientation-horizontal":"<angle>","glyph-orientation-vertical":"<angle>",kerning:"auto|<svg-length>",pause:"<'pause-before'> <'pause-after'>?","pause-after":"<time>|none|x-weak|weak|medium|strong|x-strong","pause-before":"<time>|none|x-weak|weak|medium|strong|x-strong",rest:"<'rest-before'> <'rest-after'>?","rest-after":"<time>|none|x-weak|weak|medium|strong|x-strong","rest-before":"<time>|none|x-weak|weak|medium|strong|x-strong",src:"[<url> [format( <string># )]?|local( <family-name> )]#",speak:"auto|never|always","speak-as":"normal|spell-out||digits||[literal-punctuation|no-punctuation]","unicode-range":"<urange>#","voice-balance":"<number>|left|center|right|leftwards|rightwards","voice-duration":"auto|<time>","voice-family":"[[<family-name>|<generic-voice>] ,]* [<family-name>|<generic-voice>]|preserve","voice-pitch":"<frequency>&&absolute|[[x-low|low|medium|high|x-high]||[<frequency>|<semitones>|<percentage>]]","voice-range":"<frequency>&&absolute|[[x-low|low|medium|high|x-high]||[<frequency>|<semitones>|<percentage>]]","voice-rate":"[normal|x-slow|slow|medium|fast|x-fast]||<percentage>","voice-stress":"normal|strong|moderate|none|reduced","voice-volume":"silent|[[x-soft|soft|medium|loud|x-loud]||<decibel>]","white-space-trim":"none|discard-before||discard-after||discard-inner"},atrules:{charset:{prelude:"<string>",descriptors:null},"counter-style":{prelude:"<counter-style-name>",descriptors:{"additive-symbols":"[<integer>&&<symbol>]#",fallback:"<counter-style-name>",negative:"<symbol> <symbol>?",pad:"<integer>&&<symbol>",prefix:"<symbol>",range:"[[<integer>|infinite]{2}]#|auto","speak-as":"auto|bullets|numbers|words|spell-out|<counter-style-name>",suffix:"<symbol>",symbols:"<symbol>+",system:"cyclic|numeric|alphabetic|symbolic|additive|[fixed <integer>?]|[extends <counter-style-name>]"}},document:{prelude:"[<url>|url-prefix( <string> )|domain( <string> )|media-document( <string> )|regexp( <string> )]#",descriptors:null},"font-palette-values":{prelude:"<dashed-ident>",descriptors:{"base-palette":"light|dark|<integer [0,∞]>","font-family":"<family-name>#","override-colors":"[<integer [0,∞]> <absolute-color-base>]#"}},"font-face":{prelude:null,descriptors:{"ascent-override":"normal|<percentage>","descent-override":"normal|<percentage>","font-display":"[auto|block|swap|fallback|optional]","font-family":"<family-name>","font-feature-settings":"normal|<feature-tag-value>#","font-variation-settings":"normal|[<string> <number>]#","font-stretch":"<font-stretch-absolute>{1,2}","font-style":"normal|italic|oblique <angle>{0,2}","font-weight":"<font-weight-absolute>{1,2}","line-gap-override":"normal|<percentage>","size-adjust":"<percentage>",src:"[<url> [format( <string># )]?|local( <family-name> )]#","unicode-range":"<urange>#"}},"font-feature-values":{prelude:"<family-name>#",descriptors:null},import:{prelude:"[<string>|<url>] [layer|layer( <layer-name> )]? [supports( [<supports-condition>|<declaration>] )]? <media-query-list>?",descriptors:null},keyframes:{prelude:"<keyframes-name>",descriptors:null},layer:{prelude:"[<layer-name>#|<layer-name>?]",descriptors:null},media:{prelude:"<media-query-list>",descriptors:null},namespace:{prelude:"<namespace-prefix>? [<string>|<url>]",descriptors:null},page:{prelude:"<page-selector-list>",descriptors:{bleed:"auto|<length>",marks:"none|[crop||cross]","page-orientation":"upright|rotate-left|rotate-right",size:"<length>{1,2}|auto|[<page-size>||[portrait|landscape]]"}},"position-try":{prelude:"<dashed-ident>",descriptors:{top:"<'top'>",left:"<'left'>",bottom:"<'bottom'>",right:"<'right'>","inset-block-start":"<'inset-block-start'>","inset-block-end":"<'inset-block-end'>","inset-inline-start":"<'inset-inline-start'>","inset-inline-end":"<'inset-inline-end'>","inset-block":"<'inset-block'>","inset-inline":"<'inset-inline'>",inset:"<'inset'>","margin-top":"<'margin-top'>","margin-left":"<'margin-left'>","margin-bottom":"<'margin-bottom'>","margin-right":"<'margin-right'>","margin-block-start":"<'margin-block-start'>","margin-block-end":"<'margin-block-end'>","margin-inline-start":"<'margin-inline-start'>","margin-inline-end":"<'margin-inline-end'>",margin:"<'margin'>","margin-block":"<'margin-block'>","margin-inline":"<'margin-inline'>",width:"<'width'>",height:"<'height'>","min-width":"<'min-width'>","min-height":"<'min-height'>","max-width":"<'max-width'>","max-height":"<'max-height'>","block-size":"<'block-size'>","inline-size":"<'inline-size'>","min-block-size":"<'min-block-size'>","min-inline-size":"<'min-inline-size'>","max-block-size":"<'max-block-size'>","max-inline-size":"<'max-inline-size'>","align-self":"<'align-self'>|anchor-center","justify-self":"<'justify-self'>|anchor-center"}},property:{prelude:"<custom-property-name>",descriptors:{syntax:"<string>",inherits:"true|false","initial-value":"<declaration-value>?"}},scope:{prelude:"[( <scope-start> )]? [to ( <scope-end> )]?",descriptors:null},"starting-style":{prelude:null,descriptors:null},supports:{prelude:"<supports-condition>",descriptors:null},container:{prelude:"[<container-name>]? <container-condition>",descriptors:null},nest:{prelude:"<complex-selector-list>",descriptors:null}}};var Nn={};a(Nn,{WhiteSpace:()=>au,Value:()=>pu,Url:()=>ju,UnicodeRange:()=>zu,TypeSelector:()=>xu,SupportsDeclaration:()=>vu,StyleSheet:()=>bu,String:()=>uu,SelectorList:()=>eu,Selector:()=>iu,Scope:()=>tu,Rule:()=>sc,Raw:()=>Tc,Ratio:()=>Zc,PseudoElementSelector:()=>Rc,PseudoClassSelector:()=>Ac,Percentage:()=>Bc,Parentheses:()=>Sc,Operator:()=>Fc,Number:()=>Ec,Nth:()=>Lc,NestingSelector:()=>Kc,MediaQueryList:()=>qc,MediaQuery:()=>Xc,LayerList:()=>kc,Layer:()=>ac,Identifier:()=>jc,IdSelector:()=>pc,Hash:()=>Dc,GeneralEnclosed:()=>fc,Function:()=>xc,FeatureRange:()=>hc,FeatureFunction:()=>bc,Feature:()=>uc,Dimension:()=>lc,DeclarationList:()=>oc,Declaration:()=>tc,Condition:()=>sl,Comment:()=>Tl,Combinator:()=>Zl,ClassSelector:()=>Rl,CDO:()=>Al,CDC:()=>Bl,Brackets:()=>Sl,Block:()=>Fl,AttributeSelector:()=>El,AtrulePrelude:()=>Vl,Atrule:()=>Wl,AnPlusB:()=>Pl});var Pl={};a(Pl,{structure:()=>X2,parse:()=>Xl,name:()=>J2,generate:()=>P2});var Mr=43,Pr=45,eo=110,kt=!0,k2=!1;function lo(r,t){let i=this.tokenStart+r,o=this.charCodeAt(i);if(o===Mr||o===Pr){if(t)this.error("Number sign is not allowed");i++}for(;i<this.tokenEnd;i++)if(!cr(this.charCodeAt(i)))this.error("Integer is expected",i)}function st(r){return lo.call(this,0,r)}function xt(r,t){if(!this.cmpChar(this.tokenStart+r,t)){let i="";switch(t){case eo:i="N is expected";break;case Pr:i="HyphenMinus is expected";break}this.error(i,this.tokenStart+r)}}function Jl(){let r=0,t=0,i=this.tokenType;while(i===Y||i===A)i=this.lookupType(++r);if(i!==k)if(this.isDelim(Mr,r)||this.isDelim(Pr,r)){t=this.isDelim(Mr,r)?Mr:Pr;do i=this.lookupType(++r);while(i===Y||i===A);if(i!==k)this.skip(r),st.call(this,kt)}else return null;if(r>0)this.skip(r);if(t===0){if(i=this.charCodeAt(this.tokenStart),i!==Mr&&i!==Pr)this.error("Number sign is expected")}return st.call(this,t!==0),t===Pr?"-"+this.consume(k):this.consume(k)}var J2="AnPlusB",X2={a:[String,null],b:[String,null]};function Xl(){let r=this.tokenStart,t=null,i=null;if(this.tokenType===k)st.call(this,k2),i=this.consume(k);else if(this.tokenType===f&&this.cmpChar(this.tokenStart,Pr))switch(t="-1",xt.call(this,1,eo),this.tokenEnd-this.tokenStart){case 2:this.next(),i=Jl.call(this);break;case 3:xt.call(this,2,Pr),this.next(),this.skipSC(),st.call(this,kt),i="-"+this.consume(k);break;default:xt.call(this,2,Pr),lo.call(this,3,kt),this.next(),i=this.substrToCursor(r+2)}else if(this.tokenType===f||this.isDelim(Mr)&&this.lookupType(1)===f){let o=0;if(t="1",this.isDelim(Mr))o=1,this.next();switch(xt.call(this,0,eo),this.tokenEnd-this.tokenStart){case 1:this.next(),i=Jl.call(this);break;case 2:xt.call(this,1,Pr),this.next(),this.skipSC(),st.call(this,kt),i="-"+this.consume(k);break;default:xt.call(this,1,Pr),lo.call(this,2,kt),this.next(),i=this.substrToCursor(r+o+1)}}else if(this.tokenType===q){let o=this.charCodeAt(this.tokenStart),n=o===Mr||o===Pr,e=this.tokenStart+n;for(;e<this.tokenEnd;e++)if(!cr(this.charCodeAt(e)))break;if(e===this.tokenStart+n)this.error("Integer is expected",this.tokenStart+n);if(xt.call(this,e-this.tokenStart,eo),t=this.substring(r,e),e+1===this.tokenEnd)this.next(),i=Jl.call(this);else if(xt.call(this,e-this.tokenStart+1,Pr),e+2===this.tokenEnd)this.next(),this.skipSC(),st.call(this,kt),i="-"+this.consume(k);else lo.call(this,e-this.tokenStart+2,kt),this.next(),i=this.substrToCursor(e+1)}else this.error();if(t!==null&&t.charCodeAt(0)===Mr)t=t.substr(1);if(i!==null&&i.charCodeAt(0)===Mr)i=i.substr(1);return{type:"AnPlusB",loc:this.getLocation(r,this.tokenStart),a:t,b:i}}function P2(r){if(r.a){let t=r.a==="+1"&&"n"||r.a==="1"&&"n"||r.a==="-1"&&"-n"||r.a+"n";if(r.b){let i=r.b[0]==="-"||r.b[0]==="+"?r.b:"+"+r.b;this.tokenize(t+i)}else this.tokenize(t)}else this.tokenize(r.b)}var Wl={};a(Wl,{walkContext:()=>K2,structure:()=>V2,parse:()=>ql,name:()=>W2,generate:()=>L2});function $x(){return this.Raw(this.consumeUntilLeftCurlyBracketOrSemicolon,!0)}function q2(){for(let r=1,t;t=this.lookupType(r);r++){if(t===xr)return!0;if(t===Z||t===R)return!1}return!1}var W2="Atrule",K2="atrule",V2={name:String,prelude:["AtrulePrelude","Raw",null],block:["Block",null]};function ql(r=!1){let t=this.tokenStart,i,o,n=null,e=null;if(this.eat(R),i=this.substrToCursor(t+1),o=i.toLowerCase(),this.skipSC(),this.eof===!1&&this.tokenType!==Z&&this.tokenType!==nr){if(this.parseAtrulePrelude)n=this.parseWithFallback(this.AtrulePrelude.bind(this,i,r),$x);else n=$x.call(this,this.tokenIndex);this.skipSC()}switch(this.tokenType){case nr:this.next();break;case Z:if(hasOwnProperty.call(this.atrule,o)&&typeof this.atrule[o].block==="function")e=this.atrule[o].block.call(this,r);else e=this.Block(q2.call(this));break}return{type:"Atrule",loc:this.getLocation(t,this.tokenStart),name:i,prelude:n,block:e}}function L2(r){if(this.token(R,"@"+r.name),r.prelude!==null)this.node(r.prelude);if(r.block)this.node(r.block);else this.token(nr,";")}var Vl={};a(Vl,{walkContext:()=>E2,structure:()=>Q2,parse:()=>Kl,name:()=>Y2,generate:()=>F2});var Y2="AtrulePrelude",E2="atrulePrelude",Q2={children:[[]]};function Kl(r){let t=null;if(r!==null)r=r.toLowerCase();if(this.skipSC(),hasOwnProperty.call(this.atrule,r)&&typeof this.atrule[r].prelude==="function")t=this.atrule[r].prelude.call(this);else t=this.readSequence(this.scope.AtrulePrelude);if(this.skipSC(),this.eof!==!0&&this.tokenType!==Z&&this.tokenType!==nr)this.error("Semicolon or block is expected");return{type:"AtrulePrelude",loc:this.getLocationFromList(t),children:t}}function F2(r){this.children(r)}var El={};a(El,{structure:()=>H2,parse:()=>Yl,name:()=>A2,generate:()=>R2});var G2=36,xx=42,co=61,S2=94,Ll=124,N2=126;function B2(){if(this.eof)this.error("Unexpected end of input");let r=this.tokenStart,t=!1;if(this.isDelim(xx))t=!0,this.next();else if(!this.isDelim(Ll))this.eat(f);if(this.isDelim(Ll)){if(this.charCodeAt(this.tokenStart+1)!==co)this.next(),this.eat(f);else if(t)this.error("Identifier is expected",this.tokenEnd)}else if(t)this.error("Vertical line is expected");return{type:"Identifier",loc:this.getLocation(r,this.tokenStart),name:this.substrToCursor(r)}}function y2(){let r=this.tokenStart,t=this.charCodeAt(r);if(t!==co&&t!==N2&&t!==S2&&t!==G2&&t!==xx&&t!==Ll)this.error("Attribute selector (=, ~=, ^=, $=, *=, |=) is expected");if(this.next(),t!==co){if(!this.isDelim(co))this.error("Equal sign is expected");this.next()}return this.substrToCursor(r)}var A2="AttributeSelector",H2={name:"Identifier",matcher:[String,null],value:["String","Identifier",null],flags:[String,null]};function Yl(){let r=this.tokenStart,t,i=null,o=null,n=null;if(this.eat(br),this.skipSC(),t=B2.call(this),this.skipSC(),this.tokenType!==fr){if(this.tokenType!==f)i=y2.call(this),this.skipSC(),o=this.tokenType===gr?this.String():this.Identifier(),this.skipSC();if(this.tokenType===f)n=this.consume(f),this.skipSC()}return this.eat(fr),{type:"AttributeSelector",loc:this.getLocation(r,this.tokenStart),name:t,matcher:i,value:o,flags:n}}function R2(r){if(this.token(X,"["),this.node(r.name),r.matcher!==null)this.tokenize(r.matcher),this.node(r.value);if(r.flags!==null)this.token(f,r.flags);this.token(X,"]")}var Fl={};a(Fl,{walkContext:()=>T2,structure:()=>d2,parse:()=>Ql,name:()=>C2,generate:()=>s2});var M2=38;function zx(){return this.Raw(null,!0)}function wx(){return this.parseWithFallback(this.Rule,zx)}function fx(){return this.Raw(this.consumeUntilSemicolonIncluded,!0)}function Z2(){if(this.tokenType===nr)return fx.call(this,this.tokenIndex);let r=this.parseWithFallback(this.Declaration,fx);if(this.tokenType===nr)this.next();return r}var C2="Block",T2="block",d2={children:[["Atrule","Rule","Declaration"]]};function Ql(r){let t=r?Z2:wx,i=this.tokenStart,o=this.createList();this.eat(Z);r:while(!this.eof)switch(this.tokenType){case xr:break r;case Y:case A:this.next();break;case R:o.push(this.parseWithFallback(this.Atrule.bind(this,r),zx));break;default:if(r&&this.isDelim(M2))o.push(wx.call(this));else o.push(t.call(this))}if(!this.eof)this.eat(xr);return{type:"Block",loc:this.getLocation(i,this.tokenStart),children:o}}function s2(r){this.token(Z,"{"),this.children(r,(t)=>{if(t.type==="Declaration")this.token(nr,";")}),this.token(xr,"}")}var Sl={};a(Sl,{structure:()=>tD,parse:()=>Gl,name:()=>rD,generate:()=>nD});var rD="Brackets",tD={children:[[]]};function Gl(r,t){let i=this.tokenStart,o=null;if(this.eat(br),o=r.call(this,t),!this.eof)this.eat(fr);return{type:"Brackets",loc:this.getLocation(i,this.tokenStart),children:o}}function nD(r){this.token(X,"["),this.children(r),this.token(X,"]")}var Bl={};a(Bl,{structure:()=>oD,parse:()=>Nl,name:()=>iD,generate:()=>eD});var iD="CDC",oD=[];function Nl(){let r=this.tokenStart;return this.eat(hr),{type:"CDC",loc:this.getLocation(r,this.tokenStart)}}function eD(){this.token(hr,"-->")}var Al={};a(Al,{structure:()=>cD,parse:()=>yl,name:()=>lD,generate:()=>uD});var lD="CDO",cD=[];function yl(){let r=this.tokenStart;return this.eat(dr),{type:"CDO",loc:this.getLocation(r,this.tokenStart)}}function uD(){this.token(dr,"<!--")}var Rl={};a(Rl,{structure:()=>mD,parse:()=>Hl,name:()=>bD,generate:()=>vD});var gD=46,bD="ClassSelector",mD={name:String};function Hl(){return this.eatDelim(gD),{type:"ClassSelector",loc:this.getLocation(this.tokenStart-1,this.tokenEnd),name:this.consume(f)}}function vD(r){this.token(X,"."),this.token(f,r.name)}var Zl={};a(Zl,{structure:()=>fD,parse:()=>Ml,name:()=>wD,generate:()=>zD});var hD=43,Dx=47,$D=62,xD=126,wD="Combinator",fD={name:String};function Ml(){let r=this.tokenStart,t;switch(this.tokenType){case Y:t=" ";break;case X:switch(this.charCodeAt(this.tokenStart)){case $D:case hD:case xD:this.next();break;case Dx:this.next(),this.eatIdent("deep"),this.eatDelim(Dx);break;default:this.error("Combinator is expected")}t=this.substrToCursor(r);break}return{type:"Combinator",loc:this.getLocation(r,this.tokenStart),name:t}}function zD(r){this.tokenize(r.name)}var Tl={};a(Tl,{structure:()=>OD,parse:()=>Cl,name:()=>jD,generate:()=>pD});var DD=42,_D=47,jD="Comment",OD={value:String};function Cl(){let r=this.tokenStart,t=this.tokenEnd;if(this.eat(A),t-r+2>=2&&this.charCodeAt(t-2)===DD&&this.charCodeAt(t-1)===_D)t-=2;return{type:"Comment",loc:this.getLocation(r,this.tokenStart),value:this.substring(r+2,t)}}function pD(r){this.token(A,"/*"+r.value+"*/")}var sl={};a(sl,{structure:()=>UD,parse:()=>dl,name:()=>aD,generate:()=>JD});var ID=new Set([d,p,Qr]),aD="Condition",UD={kind:String,children:[["Identifier","Feature","FeatureFunction","FeatureRange","SupportsDeclaration"]]};function _x(r){if(this.lookupTypeNonSC(1)===f&&ID.has(this.lookupTypeNonSC(2)))return this.Feature(r);return this.FeatureRange(r)}var kD={media:_x,container:_x,supports(){return this.SupportsDeclaration()}};function dl(r="media"){let t=this.createList();r:while(!this.eof)switch(this.tokenType){case A:case Y:this.next();continue;case f:t.push(this.Identifier());break;case K:{let i=this.parseWithFallback(()=>kD[r].call(this,r),()=>null);if(!i)i=this.parseWithFallback(()=>{this.eat(K);let o=this.Condition(r);return this.eat(p),o},()=>{return this.GeneralEnclosed(r)});t.push(i);break}case U:{let i=this.parseWithFallback(()=>this.FeatureFunction(r),()=>null);if(!i)i=this.GeneralEnclosed(r);t.push(i);break}default:break r}if(t.isEmpty)this.error("Condition is expected");return{type:"Condition",loc:this.getLocationFromList(t),kind:r,children:t}}function JD(r){r.children.forEach((t)=>{if(t.type==="Condition")this.token(K,"("),this.node(t),this.token(p,")");else this.node(t)})}var tc={};a(tc,{walkContext:()=>QD,structure:()=>FD,parse:()=>rc,name:()=>ED,generate:()=>GD});var Ox=33,XD=35,PD=36,qD=38,WD=42,KD=43,jx=47;function VD(){return this.Raw(this.consumeUntilExclamationMarkOrSemicolon,!0)}function LD(){return this.Raw(this.consumeUntilExclamationMarkOrSemicolon,!1)}function YD(){let r=this.tokenIndex,t=this.Value();if(t.type!=="Raw"&&this.eof===!1&&this.tokenType!==nr&&this.isDelim(Ox)===!1&&this.isBalanceEdge(r)===!1)this.error();return t}var ED="Declaration",QD="declaration",FD={important:[Boolean,String],property:String,value:["Value","Raw"]};function rc(){let r=this.tokenStart,t=this.tokenIndex,i=SD.call(this),o=di(i),n=o?this.parseCustomProperty:this.parseValue,e=o?LD:VD,l=!1,u;this.skipSC(),this.eat(d);let g=this.tokenIndex;if(!o)this.skipSC();if(n)u=this.parseWithFallback(YD,e);else u=e.call(this,this.tokenIndex);if(o&&u.type==="Value"&&u.children.isEmpty){for(let c=g-this.tokenIndex;c<=0;c++)if(this.lookupType(c)===Y){u.children.appendData({type:"WhiteSpace",loc:null,value:" "});break}}if(this.isDelim(Ox))l=ND.call(this),this.skipSC();if(this.eof===!1&&this.tokenType!==nr&&this.isBalanceEdge(t)===!1)this.error();return{type:"Declaration",loc:this.getLocation(r,this.tokenStart),important:l,property:i,value:u}}function GD(r){if(this.token(f,r.property),this.token(d,":"),this.node(r.value),r.important)this.token(X,"!"),this.token(f,r.important===!0?"important":r.important)}function SD(){let r=this.tokenStart;if(this.tokenType===X)switch(this.charCodeAt(this.tokenStart)){case WD:case PD:case KD:case XD:case qD:this.next();break;case jx:if(this.next(),this.isDelim(jx))this.next();break}if(this.tokenType===G)this.eat(G);else this.eat(f);return this.substrToCursor(r)}function ND(){this.eat(X),this.skipSC();let r=this.consume(f);return r==="important"?!0:r}var oc={};a(oc,{structure:()=>AD,parse:()=>ic,name:()=>yD,generate:()=>HD});var BD=38;function nc(){return this.Raw(this.consumeUntilSemicolonIncluded,!0)}var yD="DeclarationList",AD={children:[["Declaration","Atrule","Rule"]]};function ic(){let r=this.createList();r:while(!this.eof)switch(this.tokenType){case Y:case A:case nr:this.next();break;case R:r.push(this.parseWithFallback(this.Atrule.bind(this,!0),nc));break;default:if(this.isDelim(BD))r.push(this.parseWithFallback(this.Rule,nc));else r.push(this.parseWithFallback(this.Declaration,nc))}return{type:"DeclarationList",loc:this.getLocationFromList(r),children:r}}function HD(r){this.children(r,(t)=>{if(t.type==="Declaration")this.token(nr,";")})}var lc={};a(lc,{structure:()=>MD,parse:()=>ec,name:()=>RD,generate:()=>ZD});var RD="Dimension",MD={value:String,unit:String};function ec(){let r=this.tokenStart,t=this.consumeNumber(q);return{type:"Dimension",loc:this.getLocation(r,this.tokenStart),value:t,unit:this.substring(r+t.length,this.tokenStart)}}function ZD(r){this.token(q,r.value+r.unit)}var uc={};a(uc,{structure:()=>dD,parse:()=>cc,name:()=>TD,generate:()=>sD});var CD=47,TD="Feature",dD={kind:String,name:String,value:["Identifier","Number","Dimension","Ratio","Function",null]};function cc(r){let t=this.tokenStart,i,o=null;if(this.eat(K),this.skipSC(),i=this.consume(f),this.skipSC(),this.tokenType!==p){switch(this.eat(d),this.skipSC(),this.tokenType){case k:if(this.lookupNonWSType(1)===X)o=this.Ratio();else o=this.Number();break;case q:o=this.Dimension();break;case f:o=this.Identifier();break;case U:o=this.parseWithFallback(()=>{let n=this.Function(this.readSequence,this.scope.Value);if(this.skipSC(),this.isDelim(CD))this.error();return n},()=>{return this.Ratio()});break;default:this.error("Number, dimension, ratio or identifier is expected")}this.skipSC()}if(!this.eof)this.eat(p);return{type:"Feature",loc:this.getLocation(t,this.tokenStart),kind:r,name:i,value:o}}function sD(r){if(this.token(K,"("),this.token(f,r.name),r.value!==null)this.token(d,":"),this.node(r.value);this.token(p,")")}var bc={};a(bc,{structure:()=>t_,parse:()=>gc,name:()=>r_,generate:()=>i_});var r_="FeatureFunction",t_={kind:String,feature:String,value:["Declaration","Selector"]};function n_(r,t){let o=(this.features[r]||{})[t];if(typeof o!=="function")this.error(`Unknown feature ${t}()`);return o}function gc(r="unknown"){let t=this.tokenStart,i=this.consumeFunctionName(),o=n_.call(this,r,i.toLowerCase());this.skipSC();let n=this.parseWithFallback(()=>{let e=this.tokenIndex,l=o.call(this);if(this.eof===!1&&this.isBalanceEdge(e)===!1)this.error();return l},()=>this.Raw(null,!1));if(!this.eof)this.eat(p);return{type:"FeatureFunction",loc:this.getLocation(t,this.tokenStart),kind:r,feature:i,value:n}}function i_(r){this.token(U,r.feature+"("),this.node(r.value),this.token(p,")")}var hc={};a(hc,{structure:()=>c_,parse:()=>vc,name:()=>l_,generate:()=>u_});var px=47,o_=60,Ix=61,e_=62,l_="FeatureRange",c_={kind:String,left:["Identifier","Number","Dimension","Ratio","Function"],leftComparison:String,middle:["Identifier","Number","Dimension","Ratio","Function"],rightComparison:[String,null],right:["Identifier","Number","Dimension","Ratio","Function",null]};function mc(){switch(this.skipSC(),this.tokenType){case k:if(this.isDelim(px,this.lookupOffsetNonSC(1)))return this.Ratio();else return this.Number();case q:return this.Dimension();case f:return this.Identifier();case U:return this.parseWithFallback(()=>{let r=this.Function(this.readSequence,this.scope.Value);if(this.skipSC(),this.isDelim(px))this.error();return r},()=>{return this.Ratio()});default:this.error("Number, dimension, ratio or identifier is expected")}}function ax(r){if(this.skipSC(),this.isDelim(o_)||this.isDelim(e_)){let t=this.source[this.tokenStart];if(this.next(),this.isDelim(Ix))return this.next(),t+"=";return t}if(this.isDelim(Ix))return"=";this.error(`Expected ${r?'":", ':""}"<", ">", "=" or ")"`)}function vc(r="unknown"){let t=this.tokenStart;this.skipSC(),this.eat(K);let i=mc.call(this),o=ax.call(this,i.type==="Identifier"),n=mc.call(this),e=null,l=null;if(this.lookupNonWSType(0)!==p)e=ax.call(this),l=mc.call(this);return this.skipSC(),this.eat(p),{type:"FeatureRange",loc:this.getLocation(t,this.tokenStart),kind:r,left:i,leftComparison:o,middle:n,rightComparison:e,right:l}}function u_(r){if(this.token(K,"("),this.node(r.left),this.tokenize(r.leftComparison),this.node(r.middle),r.right)this.tokenize(r.rightComparison),this.node(r.right);this.token(p,")")}var xc={};a(xc,{walkContext:()=>b_,structure:()=>m_,parse:()=>$c,name:()=>g_,generate:()=>v_});var g_="Function",b_="function",m_={name:String,children:[[]]};function $c(r,t){let i=this.tokenStart,o=this.consumeFunctionName(),n=o.toLowerCase(),e;if(e=t.hasOwnProperty(n)?t[n].call(this,t):r.call(this,t),!this.eof)this.eat(p);return{type:"Function",loc:this.getLocation(i,this.tokenStart),name:o,children:e}}function v_(r){this.token(U,r.name+"("),this.children(r),this.token(p,")")}var fc={};a(fc,{structure:()=>$_,parse:()=>wc,name:()=>h_,generate:()=>x_});var h_="GeneralEnclosed",$_={kind:String,function:[String,null],children:[[]]};function wc(r){let t=this.tokenStart,i=null;if(this.tokenType===U)i=this.consumeFunctionName();else this.eat(K);let o=this.parseWithFallback(()=>{let n=this.tokenIndex,e=this.readSequence(this.scope.Value);if(this.eof===!1&&this.isBalanceEdge(n)===!1)this.error();return e},()=>this.createSingleNodeList(this.Raw(null,!1)));if(!this.eof)this.eat(p);return{type:"GeneralEnclosed",loc:this.getLocation(t,this.tokenStart),kind:r,function:i,children:o}}function x_(r){if(r.function)this.token(U,r.function+"(");else this.token(K,"(");this.children(r),this.token(p,")")}var Dc={};a(Dc,{xxx:()=>w_,structure:()=>z_,parse:()=>zc,name:()=>f_,generate:()=>D_});var w_="XXX",f_="Hash",z_={value:String};function zc(){let r=this.tokenStart;return this.eat(G),{type:"Hash",loc:this.getLocation(r,this.tokenStart),value:this.substrToCursor(r+1)}}function D_(r){this.token(G,"#"+r.value)}var jc={};a(jc,{structure:()=>j_,parse:()=>_c,name:()=>__,generate:()=>O_});var __="Identifier",j_={name:String};function _c(){return{type:"Identifier",loc:this.getLocation(this.tokenStart,this.tokenEnd),name:this.consume(f)}}function O_(r){this.token(f,r.name)}var pc={};a(pc,{structure:()=>I_,parse:()=>Oc,name:()=>p_,generate:()=>a_});var p_="IdSelector",I_={name:String};function Oc(){let r=this.tokenStart;return this.eat(G),{type:"IdSelector",loc:this.getLocation(r,this.tokenStart),name:this.substrToCursor(r+1)}}function a_(r){this.token(X,"#"+r.name)}var ac={};a(ac,{structure:()=>J_,parse:()=>Ic,name:()=>k_,generate:()=>X_});var U_=46,k_="Layer",J_={name:String};function Ic(){let r=this.tokenStart,t=this.consume(f);while(this.isDelim(U_))this.eat(X),t+="."+this.consume(f);return{type:"Layer",loc:this.getLocation(r,this.tokenStart),name:t}}function X_(r){this.tokenize(r.name)}var kc={};a(kc,{structure:()=>q_,parse:()=>Uc,name:()=>P_,generate:()=>W_});var P_="LayerList",q_={children:[["Layer"]]};function Uc(){let r=this.createList();this.skipSC();while(!this.eof){if(r.push(this.Layer()),this.lookupTypeNonSC(0)!==ir)break;this.skipSC(),this.next(),this.skipSC()}return{type:"LayerList",loc:this.getLocationFromList(r),children:r}}function W_(r){this.children(r,()=>this.token(ir,","))}var Xc={};a(Xc,{structure:()=>V_,parse:()=>Jc,name:()=>K_,generate:()=>L_});var K_="MediaQuery",V_={modifier:[String,null],mediaType:[String,null],condition:["Condition",null]};function Jc(){let r=this.tokenStart,t=null,i=null,o=null;if(this.skipSC(),this.tokenType===f&&this.lookupTypeNonSC(1)!==K){let n=this.consume(f),e=n.toLowerCase();if(e==="not"||e==="only")this.skipSC(),t=e,i=this.consume(f);else i=n;switch(this.lookupTypeNonSC(0)){case f:{this.skipSC(),this.eatIdent("and"),o=this.Condition("media");break}case Z:case nr:case ir:case Qr:break;default:this.error("Identifier or parenthesis is expected")}}else switch(this.tokenType){case f:case K:case U:{o=this.Condition("media");break}case Z:case nr:case Qr:break;default:this.error("Identifier or parenthesis is expected")}return{type:"MediaQuery",loc:this.getLocation(r,this.tokenStart),modifier:t,mediaType:i,condition:o}}function L_(r){if(r.mediaType){if(r.modifier)this.token(f,r.modifier);if(this.token(f,r.mediaType),r.condition)this.token(f,"and"),this.node(r.condition)}else if(r.condition)this.node(r.condition)}var qc={};a(qc,{structure:()=>E_,parse:()=>Pc,name:()=>Y_,generate:()=>Q_});var Y_="MediaQueryList",E_={children:[["MediaQuery"]]};function Pc(){let r=this.createList();this.skipSC();while(!this.eof){if(r.push(this.MediaQuery()),this.tokenType!==ir)break;this.next()}return{type:"MediaQueryList",loc:this.getLocationFromList(r),children:r}}function Q_(r){this.children(r,()=>this.token(ir,","))}var Kc={};a(Kc,{structure:()=>S_,parse:()=>Wc,name:()=>G_,generate:()=>N_});var F_=38,G_="NestingSelector",S_={};function Wc(){let r=this.tokenStart;return this.eatDelim(F_),{type:"NestingSelector",loc:this.getLocation(r,this.tokenStart)}}function N_(){this.token(X,"&")}var Lc={};a(Lc,{structure:()=>y_,parse:()=>Vc,name:()=>B_,generate:()=>A_});var B_="Nth",y_={nth:["AnPlusB","Identifier"],selector:["SelectorList",null]};function Vc(){this.skipSC();let r=this.tokenStart,t=r,i=null,o;if(this.lookupValue(0,"odd")||this.lookupValue(0,"even"))o=this.Identifier();else o=this.AnPlusB();if(t=this.tokenStart,this.skipSC(),this.lookupValue(0,"of"))this.next(),i=this.SelectorList(),t=this.tokenStart;return{type:"Nth",loc:this.getLocation(r,t),nth:o,selector:i}}function A_(r){if(this.node(r.nth),r.selector!==null)this.token(f,"of"),this.node(r.selector)}var Ec={};a(Ec,{structure:()=>R_,parse:()=>Yc,name:()=>H_,generate:()=>M_});var H_="Number",R_={value:String};function Yc(){return{type:"Number",loc:this.getLocation(this.tokenStart,this.tokenEnd),value:this.consume(k)}}function M_(r){this.token(k,r.value)}var Fc={};a(Fc,{structure:()=>C_,parse:()=>Qc,name:()=>Z_,generate:()=>T_});var Z_="Operator",C_={value:String};function Qc(){let r=this.tokenStart;return this.next(),{type:"Operator",loc:this.getLocation(r,this.tokenStart),value:this.substrToCursor(r)}}function T_(r){this.tokenize(r.value)}var Sc={};a(Sc,{structure:()=>s_,parse:()=>Gc,name:()=>d_,generate:()=>rj});var d_="Parentheses",s_={children:[[]]};function Gc(r,t){let i=this.tokenStart,o=null;if(this.eat(K),o=r.call(this,t),!this.eof)this.eat(p);return{type:"Parentheses",loc:this.getLocation(i,this.tokenStart),children:o}}function rj(r){this.token(K,"("),this.children(r),this.token(p,")")}var Bc={};a(Bc,{structure:()=>nj,parse:()=>Nc,name:()=>tj,generate:()=>ij});var tj="Percentage",nj={value:String};function Nc(){return{type:"Percentage",loc:this.getLocation(this.tokenStart,this.tokenEnd),value:this.consumeNumber(y)}}function ij(r){this.token(y,r.value+"%")}var Ac={};a(Ac,{walkContext:()=>ej,structure:()=>lj,parse:()=>yc,name:()=>oj,generate:()=>cj});var oj="PseudoClassSelector",ej="function",lj={name:String,children:[["Raw"],null]};function yc(){let r=this.tokenStart,t=null,i,o;if(this.eat(d),this.tokenType===U){if(i=this.consumeFunctionName(),o=i.toLowerCase(),this.lookupNonWSType(0)==p)t=this.createList();else if(hasOwnProperty.call(this.pseudo,o))this.skipSC(),t=this.pseudo[o].call(this),this.skipSC();else t=this.createList(),t.push(this.Raw(null,!1));this.eat(p)}else i=this.consume(f);return{type:"PseudoClassSelector",loc:this.getLocation(r,this.tokenStart),name:i,children:t}}function cj(r){if(this.token(d,":"),r.children===null)this.token(f,r.name);else this.token(U,r.name+"("),this.children(r),this.token(p,")")}var Rc={};a(Rc,{walkContext:()=>gj,structure:()=>bj,parse:()=>Hc,name:()=>uj,generate:()=>mj});var uj="PseudoElementSelector",gj="function",bj={name:String,children:[["Raw"],null]};function Hc(){let r=this.tokenStart,t=null,i,o;if(this.eat(d),this.eat(d),this.tokenType===U){if(i=this.consumeFunctionName(),o=i.toLowerCase(),this.lookupNonWSType(0)==p)t=this.createList();else if(hasOwnProperty.call(this.pseudo,o))this.skipSC(),t=this.pseudo[o].call(this),this.skipSC();else t=this.createList(),t.push(this.Raw(null,!1));this.eat(p)}else i=this.consume(f);return{type:"PseudoElementSelector",loc:this.getLocation(r,this.tokenStart),name:i,children:t}}function mj(r){if(this.token(d,":"),this.token(d,":"),r.children===null)this.token(f,r.name);else this.token(U,r.name+"("),this.children(r),this.token(p,")")}var Zc={};a(Zc,{structure:()=>hj,parse:()=>Mc,name:()=>vj,generate:()=>$j});var Ux=47;function kx(){switch(this.skipSC(),this.tokenType){case k:return this.Number();case U:return this.Function(this.readSequence,this.scope.Value);default:this.error("Number of function is expected")}}var vj="Ratio",hj={left:["Number","Function"],right:["Number","Function",null]};function Mc(){let r=this.tokenStart,t=kx.call(this),i=null;if(this.skipSC(),this.isDelim(Ux))this.eatDelim(Ux),i=kx.call(this);return{type:"Ratio",loc:this.getLocation(r,this.tokenStart),left:t,right:i}}function $j(r){if(this.node(r.left),this.token(X,"/"),r.right)this.node(r.right);else this.node(k,1)}var Tc={};a(Tc,{structure:()=>fj,parse:()=>Cc,name:()=>wj,generate:()=>zj});function xj(){if(this.tokenIndex>0){if(this.lookupType(-1)===Y)return this.tokenIndex>1?this.getTokenStart(this.tokenIndex-1):this.firstCharOffset}return this.tokenStart}var wj="Raw",fj={value:String};function Cc(r,t){let i=this.getTokenStart(this.tokenIndex),o;if(this.skipUntilBalanced(this.tokenIndex,r||this.consumeUntilBalanceEnd),t&&this.tokenStart>i)o=xj.call(this);else o=this.tokenStart;return{type:"Raw",loc:this.getLocation(i,o),value:this.substring(i,o)}}function zj(r){this.tokenize(r.value)}var sc={};a(sc,{walkContext:()=>jj,structure:()=>Oj,parse:()=>dc,name:()=>_j,generate:()=>pj});function Jx(){return this.Raw(this.consumeUntilLeftCurlyBracket,!0)}function Dj(){let r=this.SelectorList();if(r.type!=="Raw"&&this.eof===!1&&this.tokenType!==Z)this.error();return r}var _j="Rule",jj="rule",Oj={prelude:["SelectorList","Raw"],block:["Block"]};function dc(){let r=this.tokenIndex,t=this.tokenStart,i,o;if(this.parseRulePrelude)i=this.parseWithFallback(Dj,Jx);else i=Jx.call(this,r);return o=this.Block(!0),{type:"Rule",loc:this.getLocation(t,this.tokenStart),prelude:i,block:o}}function pj(r){this.node(r.prelude),this.node(r.block)}var tu={};a(tu,{structure:()=>aj,parse:()=>ru,name:()=>Ij,generate:()=>Uj});var Ij="Scope",aj={root:["SelectorList","Raw",null],limit:["SelectorList","Raw",null]};function ru(){let r=null,t=null;this.skipSC();let i=this.tokenStart;if(this.tokenType===K)this.next(),this.skipSC(),r=this.parseWithFallback(this.SelectorList,()=>this.Raw(!1,!0)),this.skipSC(),this.eat(p);if(this.lookupNonWSType(0)===f)this.skipSC(),this.eatIdent("to"),this.skipSC(),this.eat(K),this.skipSC(),t=this.parseWithFallback(this.SelectorList,()=>this.Raw(!1,!0)),this.skipSC(),this.eat(p);return{type:"Scope",loc:this.getLocation(i,this.tokenStart),root:r,limit:t}}function Uj(r){if(r.root)this.token(K,"("),this.node(r.root),this.token(p,")");if(r.limit)this.token(f,"to"),this.token(K,"("),this.node(r.limit),this.token(p,")")}var iu={};a(iu,{structure:()=>Jj,parse:()=>nu,name:()=>kj,generate:()=>Xj});var kj="Selector",Jj={children:[["TypeSelector","IdSelector","ClassSelector","AttributeSelector","PseudoClassSelector","PseudoElementSelector","Combinator"]]};function nu(){let r=this.readSequence(this.scope.Selector);if(this.getFirstListNode(r)===null)this.error("Selector is expected");return{type:"Selector",loc:this.getLocationFromList(r),children:r}}function Xj(r){this.children(r)}var eu={};a(eu,{walkContext:()=>qj,structure:()=>Wj,parse:()=>ou,name:()=>Pj,generate:()=>Kj});var Pj="SelectorList",qj="selector",Wj={children:[["Selector","Raw"]]};function ou(){let r=this.createList();while(!this.eof){if(r.push(this.Selector()),this.tokenType===ir){this.next();continue}break}return{type:"SelectorList",loc:this.getLocationFromList(r),children:r}}function Kj(r){this.children(r,()=>this.token(ir,","))}var uu={};a(uu,{structure:()=>Lj,parse:()=>cu,name:()=>Vj,generate:()=>Yj});var lu=92,Xx=34,Px=39;function uo(r){let t=r.length,i=r.charCodeAt(0),o=i===Xx||i===Px?1:0,n=o===1&&t>1&&r.charCodeAt(t-1)===i?t-2:t-1,e="";for(let l=o;l<=n;l++){let u=r.charCodeAt(l);if(u===lu){if(l===n){if(l!==t-1)e=r.substr(l+1);break}if(u=r.charCodeAt(++l),Or(lu,u)){let g=l-1,c=yr(r,g);l=c-1,e+=In(r.substring(g+1,c))}else if(u===13&&r.charCodeAt(l+1)===10)l++}else e+=r[l]}return e}function qx(r,t){let i=t?"'":'"',o=t?Px:Xx,n="",e=!1;for(let l=0;l<r.length;l++){let u=r.charCodeAt(l);if(u===0){n+="�";continue}if(u<=31||u===127){n+="\\"+u.toString(16),e=!0;continue}if(u===o||u===lu)n+="\\"+r.charAt(l),e=!1;else{if(e&&(Ir(u)||Br(u)))n+=" ";n+=r.charAt(l),e=!1}}return i+n+i}var Vj="String",Lj={value:String};function cu(){return{type:"String",loc:this.getLocation(this.tokenStart,this.tokenEnd),value:uo(this.consume(gr))}}function Yj(r){this.token(gr,qx(r.value))}var bu={};a(bu,{walkContext:()=>Fj,structure:()=>Gj,parse:()=>gu,name:()=>Qj,generate:()=>Sj});var Ej=33;function Kx(){return this.Raw(null,!1)}var Qj="StyleSheet",Fj="stylesheet",Gj={children:[["Comment","CDO","CDC","Atrule","Rule","Raw"]]};function gu(){let r=this.tokenStart,t=this.createList(),i;r:while(!this.eof){switch(this.tokenType){case Y:this.next();continue;case A:if(this.charCodeAt(this.tokenStart+2)!==Ej){this.next();continue}i=this.Comment();break;case dr:i=this.CDO();break;case hr:i=this.CDC();break;case R:i=this.parseWithFallback(this.Atrule,Kx);break;default:i=this.parseWithFallback(this.Rule,Kx)}t.push(i)}return{type:"StyleSheet",loc:this.getLocation(r,this.tokenStart),children:t}}function Sj(r){this.children(r)}var vu={};a(vu,{structure:()=>Bj,parse:()=>mu,name:()=>Nj,generate:()=>yj});var Nj="SupportsDeclaration",Bj={declaration:"Declaration"};function mu(){let r=this.tokenStart;this.eat(K),this.skipSC();let t=this.Declaration();if(!this.eof)this.eat(p);return{type:"SupportsDeclaration",loc:this.getLocation(r,this.tokenStart),declaration:t}}function yj(r){this.token(K,"("),this.node(r.declaration),this.token(p,")")}var xu={};a(xu,{structure:()=>Rj,parse:()=>$u,name:()=>Hj,generate:()=>Mj});var Aj=42,Vx=124;function hu(){if(this.tokenType!==f&&this.isDelim(Aj)===!1)this.error("Identifier or asterisk is expected");this.next()}var Hj="TypeSelector",Rj={name:String};function $u(){let r=this.tokenStart;if(this.isDelim(Vx))this.next(),hu.call(this);else if(hu.call(this),this.isDelim(Vx))this.next(),hu.call(this);return{type:"TypeSelector",loc:this.getLocation(r,this.tokenStart),name:this.substrToCursor(r)}}function Mj(r){this.tokenize(r.name)}var zu={};a(zu,{structure:()=>dj,parse:()=>fu,name:()=>Tj,generate:()=>sj});var Lx=43,Yx=45,wu=63;function Sn(r,t){let i=0;for(let o=this.tokenStart+r;o<this.tokenEnd;o++){let n=this.charCodeAt(o);if(n===Yx&&t&&i!==0)return Sn.call(this,r+i+1,!1),-1;if(!Ir(n))this.error(t&&i!==0?"Hyphen minus"+(i<6?" or hex digit":"")+" is expected":i<6?"Hex digit is expected":"Unexpected input",o);if(++i>6)this.error("Too many hex digits",o)}return this.next(),i}function go(r){let t=0;while(this.isDelim(wu)){if(++t>r)this.error("Too many question marks");this.next()}}function Zj(r){if(this.charCodeAt(this.tokenStart)!==r)this.error((r===Lx?"Plus sign":"Hyphen minus")+" is expected")}function Cj(){let r=0;switch(this.tokenType){case k:if(r=Sn.call(this,1,!0),this.isDelim(wu)){go.call(this,6-r);break}if(this.tokenType===q||this.tokenType===k){Zj.call(this,Yx),Sn.call(this,1,!1);break}break;case q:if(r=Sn.call(this,1,!0),r>0)go.call(this,6-r);break;default:if(this.eatDelim(Lx),this.tokenType===f){if(r=Sn.call(this,0,!0),r>0)go.call(this,6-r);break}if(this.isDelim(wu)){this.next(),go.call(this,5);break}this.error("Hex digit or question mark is expected")}}var Tj="UnicodeRange",dj={value:String};function fu(){let r=this.tokenStart;return this.eatIdent("u"),Cj.call(this),{type:"UnicodeRange",loc:this.getLocation(r,this.tokenStart),value:this.substrToCursor(r)}}function sj(r){this.tokenize(r.value)}var ju={};a(ju,{structure:()=>eO,parse:()=>_u,name:()=>oO,generate:()=>lO});var rO=32,Du=92,tO=34,nO=39,iO=40,Ex=41;function Qx(r){let t=r.length,i=4,o=r.charCodeAt(t-1)===Ex?t-2:t-1,n="";while(i<o&&Br(r.charCodeAt(i)))i++;while(i<o&&Br(r.charCodeAt(o)))o--;for(let e=i;e<=o;e++){let l=r.charCodeAt(e);if(l===Du){if(e===o){if(e!==t-1)n=r.substr(e+1);break}if(l=r.charCodeAt(++e),Or(Du,l)){let u=e-1,g=yr(r,u);e=g-1,n+=In(r.substring(u+1,g))}else if(l===13&&r.charCodeAt(e+1)===10)e++}else n+=r[e]}return n}function Fx(r){let t="",i=!1;for(let o=0;o<r.length;o++){let n=r.charCodeAt(o);if(n===0){t+="�";continue}if(n<=31||n===127){t+="\\"+n.toString(16),i=!0;continue}if(n===rO||n===Du||n===tO||n===nO||n===iO||n===Ex)t+="\\"+r.charAt(o),i=!1;else{if(i&&Ir(n))t+=" ";t+=r.charAt(o),i=!1}}return"url("+t+")"}var oO="Url",eO={value:String};function _u(){let r=this.tokenStart,t;switch(this.tokenType){case er:t=Qx(this.consume(er));break;case U:if(!this.cmpStr(this.tokenStart,this.tokenEnd,"url("))this.error("Function name must be `url`");if(this.eat(U),this.skipSC(),t=uo(this.consume(gr)),this.skipSC(),!this.eof)this.eat(p);break;default:this.error("Url or Function is expected")}return{type:"Url",loc:this.getLocation(r,this.tokenStart),value:t}}function lO(r){this.token(er,Fx(r.value))}var pu={};a(pu,{structure:()=>uO,parse:()=>Ou,name:()=>cO,generate:()=>gO});var cO="Value",uO={children:[[]]};function Ou(){let r=this.tokenStart,t=this.readSequence(this.scope.Value);return{type:"Value",loc:this.getLocation(r,this.tokenStart),children:t}}function gO(r){this.children(r)}var au={};a(au,{structure:()=>vO,parse:()=>Iu,name:()=>mO,generate:()=>hO});var bO=Object.freeze({type:"WhiteSpace",loc:null,value:" "}),mO="WhiteSpace",vO={value:String};function Iu(){return this.eat(Y),bO}function hO(r){this.token(Y,r.value)}var Sx={generic:!0,cssWideKeywords:Zt,...hx,node:Nn};var Ju={};a(Ju,{Value:()=>Rx,Selector:()=>Ax,AtrulePrelude:()=>Bx});var $O=35,xO=42,Nx=43,wO=45,fO=47,zO=117;function Bn(r){switch(this.tokenType){case G:return this.Hash();case ir:return this.Operator();case K:return this.Parentheses(this.readSequence,r.recognizer);case br:return this.Brackets(this.readSequence,r.recognizer);case gr:return this.String();case q:return this.Dimension();case y:return this.Percentage();case k:return this.Number();case U:return this.cmpStr(this.tokenStart,this.tokenEnd,"url(")?this.Url():this.Function(this.readSequence,r.recognizer);case er:return this.Url();case f:if(this.cmpChar(this.tokenStart,zO)&&this.cmpChar(this.tokenStart+1,Nx))return this.UnicodeRange();else return this.Identifier();case X:{let t=this.charCodeAt(this.tokenStart);if(t===fO||t===xO||t===Nx||t===wO)return this.Operator();if(t===$O)this.error("Hex or identifier is expected",this.tokenStart+1);break}}}var Bx={getNode:Bn};var DO=35,_O=38,jO=42,OO=43,pO=47,yx=46,IO=62,aO=124,UO=126;function kO(r,t){if(t.last!==null&&t.last.type!=="Combinator"&&r!==null&&r.type!=="Combinator")t.push({type:"Combinator",loc:null,name:" "})}function JO(){switch(this.tokenType){case br:return this.AttributeSelector();case G:return this.IdSelector();case d:if(this.lookupType(1)===d)return this.PseudoElementSelector();else return this.PseudoClassSelector();case f:return this.TypeSelector();case k:case y:return this.Percentage();case q:if(this.charCodeAt(this.tokenStart)===yx)this.error("Identifier is expected",this.tokenStart+1);break;case X:{switch(this.charCodeAt(this.tokenStart)){case OO:case IO:case UO:case pO:return this.Combinator();case yx:return this.ClassSelector();case jO:case aO:return this.TypeSelector();case DO:return this.IdSelector();case _O:return this.NestingSelector()}break}}}var Ax={onWhiteSpace:kO,getNode:JO};function Uu(){return this.createSingleNodeList(this.Raw(null,!1))}function ku(){let r=this.createList();if(this.skipSC(),r.push(this.Identifier()),this.skipSC(),this.tokenType===ir){r.push(this.Operator());let t=this.tokenIndex,i=this.parseCustomProperty?this.Value(null):this.Raw(this.consumeUntilExclamationMarkOrSemicolon,!1);if(i.type==="Value"&&i.children.isEmpty){for(let o=t-this.tokenIndex;o<=0;o++)if(this.lookupType(o)===Y){i.children.appendData({type:"WhiteSpace",loc:null,value:" "});break}}r.push(i)}return r}function Hx(r){return r!==null&&r.type==="Operator"&&(r.value[r.value.length-1]==="-"||r.value[r.value.length-1]==="+")}var Rx={getNode:Bn,onWhiteSpace(r,t){if(Hx(r))r.value=" "+r.value;if(Hx(t.last))t.last.value+=" "},expression:Uu,var:ku};var XO=new Set(["none","and","not","or"]),Mx={parse:{prelude(){let r=this.createList();if(this.tokenType===f){let t=this.substring(this.tokenStart,this.tokenEnd);if(!XO.has(t.toLowerCase()))r.push(this.Identifier())}return r.push(this.Condition("container")),r},block(r=!1){return this.Block(r)}}};var Zx={parse:{prelude:null,block(){return this.Block(!0)}}};function Xu(r,t){return this.parseWithFallback(()=>{try{return r.call(this)}finally{if(this.skipSC(),this.lookupNonWSType(0)!==p)this.error()}},t||(()=>this.Raw(null,!0)))}var Cx={layer(){this.skipSC();let r=this.createList(),t=Xu.call(this,this.Layer);if(t.type!=="Raw"||t.value!=="")r.push(t);return r},supports(){this.skipSC();let r=this.createList(),t=Xu.call(this,this.Declaration,()=>Xu.call(this,()=>this.Condition("supports")));if(t.type!=="Raw"||t.value!=="")r.push(t);return r}},Tx={parse:{prelude(){let r=this.createList();switch(this.tokenType){case gr:r.push(this.String());break;case er:case U:r.push(this.Url());break;default:this.error("String or url() is expected")}if(this.skipSC(),this.tokenType===f&&this.cmpStr(this.tokenStart,this.tokenEnd,"layer"))r.push(this.Identifier());else if(this.tokenType===U&&this.cmpStr(this.tokenStart,this.tokenEnd,"layer("))r.push(this.Function(null,Cx));if(this.skipSC(),this.tokenType===U&&this.cmpStr(this.tokenStart,this.tokenEnd,"supports("))r.push(this.Function(null,Cx));if(this.lookupNonWSType(0)===f||this.lookupNonWSType(0)===K)r.push(this.MediaQueryList());return r},block:null}};var dx={parse:{prelude(){return this.createSingleNodeList(this.LayerList())},block(){return this.Block(!1)}}};var sx={parse:{prelude(){return this.createSingleNodeList(this.MediaQueryList())},block(r=!1){return this.Block(r)}}};var rw={parse:{prelude(){return this.createSingleNodeList(this.SelectorList())},block(){return this.Block(!0)}}};var tw={parse:{prelude(){return this.createSingleNodeList(this.SelectorList())},block(){return this.Block(!0)}}};var nw={parse:{prelude(){return this.createSingleNodeList(this.Scope())},block(r=!1){return this.Block(r)}}};var iw={parse:{prelude:null,block(r=!1){return this.Block(r)}}};var ow={parse:{prelude(){return this.createSingleNodeList(this.Condition("supports"))},block(r=!1){return this.Block(r)}}};var ew={container:Mx,"font-face":Zx,import:Tx,layer:dx,media:sx,nest:rw,page:tw,scope:nw,"starting-style":iw,supports:ow};function lw(){let r=this.createList();this.skipSC();r:while(!this.eof){switch(this.tokenType){case f:r.push(this.Identifier());break;case gr:r.push(this.String());break;case ir:r.push(this.Operator());break;case p:break r;default:this.error("Identifier, string or comma is expected")}this.skipSC()}return r}var Jt={parse(){return this.createSingleNodeList(this.SelectorList())}},Pu={parse(){return this.createSingleNodeList(this.Selector())}},PO={parse(){return this.createSingleNodeList(this.Identifier())}},qO={parse:lw},bo={parse(){return this.createSingleNodeList(this.Nth())}},cw={dir:PO,has:Jt,lang:qO,matches:Jt,is:Jt,"-moz-any":Jt,"-webkit-any":Jt,where:Jt,not:Jt,"nth-child":bo,"nth-last-child":bo,"nth-last-of-type":bo,"nth-of-type":bo,slotted:Pu,host:Pu,"host-context":Pu};var qu={};a(qu,{WhiteSpace:()=>Iu,Value:()=>Ou,Url:()=>_u,UnicodeRange:()=>fu,TypeSelector:()=>$u,SupportsDeclaration:()=>mu,StyleSheet:()=>gu,String:()=>cu,SelectorList:()=>ou,Selector:()=>nu,Scope:()=>ru,Rule:()=>dc,Raw:()=>Cc,Ratio:()=>Mc,PseudoElementSelector:()=>Hc,PseudoClassSelector:()=>yc,Percentage:()=>Nc,Parentheses:()=>Gc,Operator:()=>Qc,Number:()=>Yc,Nth:()=>Vc,NestingSelector:()=>Wc,MediaQueryList:()=>Pc,MediaQuery:()=>Jc,LayerList:()=>Uc,Layer:()=>Ic,Identifier:()=>_c,IdSelector:()=>Oc,Hash:()=>zc,GeneralEnclosed:()=>wc,Function:()=>$c,FeatureRange:()=>vc,FeatureFunction:()=>gc,Feature:()=>cc,Dimension:()=>ec,DeclarationList:()=>ic,Declaration:()=>rc,Condition:()=>dl,Comment:()=>Cl,Combinator:()=>Ml,ClassSelector:()=>Hl,CDO:()=>yl,CDC:()=>Nl,Brackets:()=>Gl,Block:()=>Ql,AttributeSelector:()=>Yl,AtrulePrelude:()=>Kl,Atrule:()=>ql,AnPlusB:()=>Xl});var uw={parseContext:{default:"StyleSheet",stylesheet:"StyleSheet",atrule:"Atrule",atrulePrelude(r){return this.AtrulePrelude(r.atrule?String(r.atrule):null)},mediaQueryList:"MediaQueryList",mediaQuery:"MediaQuery",condition(r){return this.Condition(r.kind)},rule:"Rule",selectorList:"SelectorList",selector:"Selector",block(){return this.Block(!0)},declarationList:"DeclarationList",declaration:"Declaration",value:"Value"},features:{supports:{selector(){return this.Selector()}},container:{style(){return this.Declaration()}}},scope:Ju,atrule:ew,pseudo:cw,node:qu};var gw={node:Nn};var bw=kl({...Sx,...uw,...gw});var{tokenize:KJ,parse:Wu,generate:Ku,lexer:VJ,createLexer:LJ,walk:mo,find:YJ,findLast:EJ,findAll:QJ,toPlainObject:FJ,fromPlainObject:GJ,fork:SJ}=bw;class rn{static instance;constructor(){}injectDefaultStyles(){try{let r=document.createElement("style");r.id="onlook-stylesheet",r.textContent=`
            [${"data-onlook-editing-text"}="true"] {
                opacity: 0;
            }
        `,document.head.appendChild(r)}catch(r){console.warn("Error injecting default styles",r)}}static getInstance(){if(!rn.instance)rn.instance=new rn;return rn.instance}get stylesheet(){let r=document.getElementById("onlook-stylesheet")||this.createStylesheet();return r.textContent=r.textContent||"",Wu(r.textContent)}set stylesheet(r){let t=document.getElementById("onlook-stylesheet")||this.createStylesheet();t.textContent=Ku(r)}createStylesheet(){let r=document.createElement("style");return r.id="onlook-stylesheet",document.head.appendChild(r),r}find(r,t){let i=[];return mo(r,{visit:"Rule",enter:(o)=>{if(o.type==="Rule"){let n=o;if(n.prelude.type==="SelectorList")n.prelude.children.forEach((e)=>{if(Ku(e)===t)i.push(o)})}}}),i}updateStyle(r,t){let i=We(r,!1),o=this.stylesheet;for(let[n,e]of Object.entries(t)){let l=this.jsToCssProperty(n),u=this.find(o,i);if(!u.length)this.addRule(o,i,l,e.value);else u.forEach((g)=>{if(g.type==="Rule")this.updateRule(g,l,e.value)})}this.stylesheet=o}addRule(r,t,i,o){let n={type:"Rule",prelude:{type:"SelectorList",children:[{type:"Selector",children:[{type:"TypeSelector",name:t}]}]},block:{type:"Block",children:[{type:"Declaration",property:i,value:{type:"Raw",value:o}}]}};if(r.type==="StyleSheet")r.children.push(n)}updateRule(r,t,i){let o=!1;if(mo(r.block,{visit:"Declaration",enter:(n)=>{if(n.property===t){if(n.value={type:"Raw",value:i},i==="")r.block.children=r.block.children.filter((e)=>e.property!==t);o=!0}}}),!o)if(i==="")r.block.children=r.block.children.filter((n)=>n.property!==t);else r.block.children.push({type:"Declaration",property:t,value:{type:"Raw",value:i},important:!1})}getJsStyle(r){let t=this.stylesheet,i=this.find(t,r),o={};if(!i.length)return o;return i.forEach((n)=>{if(n.type==="Rule")mo(n,{visit:"Declaration",enter:(e)=>{o[this.cssToJsProperty(e.property)]=e.value.value}})}),o}jsToCssProperty(r){if(!r)return"";return r.replace(/([A-Z])/g,"-$1").toLowerCase()}cssToJsProperty(r){if(!r)return"";return r.replace(/-([a-z])/g,(t)=>t[1]?.toUpperCase()??"")}removeStyles(r,t){let i=We(r,!1),o=this.stylesheet;this.find(o,i).forEach((e)=>{if(e.type==="Rule"){let l=t.map((u)=>this.jsToCssProperty(u));e.block.children=e.block.children.filter((u)=>!l.includes(u.property))}}),this.stylesheet=o}clear(){this.stylesheet=Wu("")}}var ot=rn.getInstance();function mw(r,t){return ot.updateStyle(r,t.updated),qi(r,!0)}function vw(r,t){ot.updateStyle(r,{backgroundImage:{value:`url(${t})`,type:"value"}})}function hw(r){ot.updateStyle(r,{backgroundImage:{value:"none",type:"value"}})}function KO(r,t){let i=Array.from(r.children);if(i.length===0)return 0;let o=0,n=1/0;i.forEach((u,g)=>{let c=u.getBoundingClientRect(),b=c.top+c.height/2,v=Math.abs(t-b);if(v<n)n=v,o=g});let e=i[o]?.getBoundingClientRect();if(!e)return 0;let l=e.top+e.height/2;return t>l?o+1:o}function $w(r,t){let i=VO(r,t);if(!i)return null;let o=window.getComputedStyle(i).display;if(o==="flex"||o==="grid"){let e=KO(i,t);return{type:"index",targetDomId:Xr(i),targetOid:Nr(i)||Sr(i)||null,index:e,originalIndex:e}}return{type:"append",targetDomId:Xr(i),targetOid:Nr(i)||Sr(i)||null}}function VO(r,t){let i=k$(r,t);if(!i)return null;let o=!0;while(i&&o)if(o=f$.has(i.tagName.toLowerCase()),o)i=i.parentElement;return i}function xw(r,t){let i=S(t.targetDomId);if(!i){console.warn(`Target element not found: ${t.targetDomId}`);return}let o=ww(r);switch(t.type){case"append":i.appendChild(o);break;case"prepend":i.prepend(o);break;case"index":if(t.index===void 0||t.index<0){console.warn(`Invalid index: ${t.index}`);return}if(t.index>=i.children.length)i.appendChild(o);else i.insertBefore(o,i.children.item(t.index));break;default:console.warn(`Invalid position: ${t}`),Ve(t)}let n=lr(o,!0),e=zr(o);return{domEl:n,newMap:e}}function ww(r){let t=document.createElement(r.tagName);t.setAttribute("data-onlook-inserted","true");for(let[i,o]of Object.entries(r.attributes))t.setAttribute(i,o);if(r.textContent!==null&&r.textContent!==void 0)t.textContent=r.textContent;for(let[i,o]of Object.entries(r.styles))t.style.setProperty(ot.jsToCssProperty(i),o);for(let i of r.children){let o=ww(i);t.appendChild(o)}return t}function fw(r){let t=S(r.targetDomId);if(!t)return console.warn(`Target element not found: ${r.targetDomId}`),null;let i=null;switch(r.type){case"append":i=t.lastElementChild;break;case"prepend":i=t.firstElementChild;break;case"index":if(r.index!==-1)i=t.children.item(r.index);else return console.warn(`Invalid index: ${r.index}`),null;break;default:console.warn(`Invalid position: ${r}`),Ve(r)}if(i){let o=lr(i,!0);i.style.display="none";let n=t.parentElement?zr(t.parentElement):null;return{domEl:o,newMap:n}}else return console.warn("No element found to remove at the specified location"),null}function zw(r,t){let i=S(r);if(!i)return console.warn("Element not found for domId:",r),null;let o=J$(i);if(!o)return console.warn("Failed to get location for element:",i),null;let n=Wi(r);if(!n)return console.warn("Failed to get action element for element:",i),null;return{type:"remove-element",targets:[{frameId:t,domId:n.domId,oid:n.oid}],location:o,element:n,editText:!1,pasteParams:null,codeBlock:null}}function Dw(r,t){let i=S(r);if(!i)return console.warn(`Move element not found: ${r}`),null;let o=LO(i,t);if(!o)return console.warn(`Failed to move element: ${r}`),null;let n=lr(o,!0),e=o.parentElement?zr(o.parentElement):null;return{domEl:n,newMap:e}}function _w(r){let t=S(r);if(!t)return console.warn(`Element not found: ${r}`),-1;return Array.from(t.parentElement?.children||[]).filter(Ot).indexOf(t)}function LO(r,t){let i=r.parentElement;if(!i){console.warn("Parent not found");return}if(i.removeChild(r),t>=i.children.length)return i.appendChild(r),r;let o=i.children[t];return i.insertBefore(r,o??null),r}function vo(r){if(!r||!r.children||r.children.length<2)return"vertical";let t=Array.from(r.children),i=t[0],o=t[1],n=i?.getBoundingClientRect(),e=o?.getBoundingClientRect();if(n&&e&&Math.abs(n.left-e.left)<Math.abs(n.top-e.top))return"vertical";else return"horizontal"}function jw(r,t,i,o){if(r.length===0)return 0;let n=r.map((e)=>{let l=e.getBoundingClientRect();return{x:l.left+l.width/2,y:l.top+l.height/2}});if(o==="horizontal")for(let e=0;e<n.length;e++){let l=n[e];if(l&&t<l.x)return e}else for(let e=0;e<n.length;e++){let l=n[e];if(l&&i<l.y)return e}return r.length}function Ow(r,t,i,o){let n=r.getBoundingClientRect(),e=window.getComputedStyle(r),l=e.gridTemplateColumns.split(" ").length,u=e.gridTemplateRows.split(" ").length,g=n.width/l,c=n.height/u,b=Math.floor((i-n.left)/g),h=Math.floor((o-n.top)/c)*l+b;return Math.min(Math.max(h,0),t.length)}function pw(r){let t=document.createElement("div"),i=window.getComputedStyle(r),o=r.className;t.id="onlook-drag-stub",t.style.width=i.width,t.style.height=i.height,t.style.margin=i.margin,t.style.padding=i.padding,t.style.borderRadius=i.borderRadius,t.style.backgroundColor="rgba(0, 0, 0, 0.2)",t.style.display="none",t.className=o,document.body.appendChild(t)}function Iw(r,t,i){let o=document.getElementById("onlook-drag-stub");if(!o)return;let n=r.parentElement;if(!n)return;let e=r.getAttribute("data-onlook-drag-direction");if(!e)e=vo(n);let l=window.getComputedStyle(n),u=l.display==="grid";if(!u&&l.display==="flex"&&(l.flexDirection==="row"||l.flexDirection===""))e="horizontal";let c=Array.from(n.children).filter((v)=>v!==r&&v!==o),b;if(u)b=Ow(n,c,t,i);else b=jw(c,t,i,e);if(o.remove(),b>=c.length)n.appendChild(o);else n.insertBefore(o,c[b]??null);o.style.display="block"}function Vu(){let r=document.getElementById("onlook-drag-stub");if(!r)return;r.remove()}function aw(r,t){let i=document.getElementById("onlook-drag-stub");if(!i)return-1;return Array.from(r.children).filter((n)=>n!==t).indexOf(i)}function Uw(r){let t=S(r);if(!t)return console.warn(`Start drag element not found: ${r}`),null;let i=t.parentElement;if(!i)return console.warn("Start drag parent not found"),null;let n=Array.from(i.children).filter(Ot).indexOf(t),e=window.getComputedStyle(t);if(YO(t),e.position!=="absolute")pw(t);let l=EO(t),u=t.getBoundingClientRect(),g=e.position==="absolute"?{x:l.left,y:l.top}:{x:l.left-u.left,y:l.top-u.top};return t.setAttribute("data-onlook-drag-start-position",JSON.stringify({...l,offset:g})),n}function kw(r,t,i,o){let n=S(r);if(!n){console.warn("Dragging element not found");return}let e=n.parentElement;if(e){let l=JSON.parse(n.getAttribute("data-onlook-drag-start-position")||"{}"),u=e.getBoundingClientRect(),g=t-u.left-(o.x-l.offset.x),c=i-u.top-(o.y-l.offset.y);n.style.left=`${g}px`,n.style.top=`${c}px`}n.style.transform="none"}function Jw(r,t,i,o,n){let e=S(r);if(!e){console.warn("Dragging element not found");return}if(!e.style.transition)e.style.transition="transform 0.05s cubic-bezier(0.2, 0, 0, 1)";let l=JSON.parse(e.getAttribute("data-onlook-drag-start-position")||"{}");if(e.style.position!=="fixed"){let g=window.getComputedStyle(e);e.style.position="fixed",e.style.width=g.width,e.style.height=g.height,e.style.left=`${l.left}px`,e.style.top=`${l.top}px`}if(e.style.transform=`translate(${t}px, ${i}px)`,e.parentElement)Iw(e,o,n)}function Xw(r){let t=S(r);if(!t)return console.warn("End drag element not found"),null;let i=window.getComputedStyle(t);return qw(t),Xr(t),{left:i.left,top:i.top}}function Pw(r){let t=S(r);if(!t)return console.warn("End drag element not found"),Yu(),null;let i=t.parentElement;if(!i)return console.warn("End drag parent not found"),Lu(t),null;let o=aw(i,t);if(Lu(t),Vu(),o===-1)return null;let n=Array.from(i.children).indexOf(t);if(o===n)return null;return{newIndex:o,child:lr(t,!1),parent:lr(i,!1)}}function YO(r){if(r.getAttribute("data-onlook-drag-saved-style"))return;let i={position:r.style.position,transform:r.style.transform,width:r.style.width,height:r.style.height,left:r.style.left,top:r.style.top};if(r.setAttribute("data-onlook-drag-saved-style",JSON.stringify(i)),r.setAttribute("data-onlook-dragging","true"),r.style.zIndex="1000",r.getAttribute("data-onlook-drag-direction")!==null){let o=r.parentElement;if(o){let n=vo(o);r.setAttribute("data-onlook-drag-direction",n)}}}function Lu(r){Pi(r),qw(r),Xr(r)}function qw(r){r.removeAttribute("data-onlook-drag-saved-style"),r.removeAttribute("data-onlook-dragging"),r.removeAttribute("data-onlook-drag-direction"),r.removeAttribute("data-onlook-drag-start-position")}function EO(r){let t=r.getBoundingClientRect();return{left:t.left+window.scrollX,top:t.top+window.scrollY}}function Yu(){let r=document.querySelectorAll(`[${"data-onlook-dragging"}]`);for(let t of Array.from(r))Lu(t);Vu()}function Ww(r){let t=S(r);if(!t)return console.warn("Start editing text failed. No element for selector:",r),null;let i=Array.from(t.childNodes).filter((l)=>l.nodeType!==Node.COMMENT_NODE),o=null,n=i.every((l)=>l.nodeType===Node.TEXT_NODE||l.nodeType===Node.ELEMENT_NODE&&l.tagName.toLowerCase()==="br");if(i.length===0)o=t;else if(i.length===1&&i[0]?.nodeType===Node.TEXT_NODE)o=t;else if(n)o=t;if(!o)return console.warn("Start editing text failed. No target element found for selector:",r),null;let e=Yw(t);return Lw(o),{originalContent:e}}function Kw(r,t){let i=S(r);if(!i)return console.warn("Edit text failed. No element for selector:",r),null;return Lw(i),GO(i,t),{domEl:lr(i,!0),newMap:zr(i)}}function Vw(r){let t=S(r);if(!t)return console.warn("Stop editing text failed. No element for selector:",r),null;return QO(t),{newContent:Yw(t),domEl:lr(t,!0)}}function Lw(r){r.setAttribute("data-onlook-editing-text","true")}function QO(r){Pi(r),FO(r)}function FO(r){r.removeAttribute("data-onlook-editing-text")}function GO(r,t){let i=t.replace(/\n/g,"<br>");r.innerHTML=i}function Yw(r){let t=r.innerHTML;t=t.replace(/<br\s*\/?>/gi,`
`),t=t.replace(/<[^>]*>/g,"");let i=document.createElement("textarea");return i.innerHTML=t,i.value}function Ew(r){return!0}function Fw(){let r=document.body,t={childList:!0,subtree:!0};new MutationObserver((o)=>{let n=new Map,e=new Map;for(let l of o)if(l.type==="childList"){let u=l.target;l.addedNodes.forEach((g)=>{let c=g;if(g.nodeType===Node.ELEMENT_NODE&&c.hasAttribute("data-odid")&&!Qw(c)){if(SO(c),u){let b=zr(u);if(b)n=new Map([...n,...b])}}}),l.removedNodes.forEach((g)=>{let c=g;if(g.nodeType===Node.ELEMENT_NODE&&c.hasAttribute("data-odid")&&!Qw(c)){if(u){let b=zr(u);if(b)e=new Map([...e,...b])}}})}if(n.size>0||e.size>0){if(jr)jr.onWindowMutated({added:Object.fromEntries(n),removed:Object.fromEntries(e)}).catch((l)=>{console.error("Failed to send window mutation event:",l)})}}).observe(r,t)}function Gw(){function r(){if(jr)jr.onWindowResized().catch((t)=>{console.error("Failed to send window resize event:",t)})}window.addEventListener("resize",r)}function Qw(r){if(r.id==="onlook-drag-stub")return!0;if(r.getAttribute("data-onlook-inserted"))return!0;return!1}function SO(r){let t=r.getAttribute("data-oid");if(!t)return;document.querySelectorAll(`[${"data-oid"}="${t}"][${"data-onlook-inserted"}]`).forEach((i)=>{["data-odid","data-onlook-drag-saved-style","data-onlook-editing-text","data-oiid"].forEach((n)=>{let e=i.getAttribute(n);if(e)r.setAttribute(n,e)}),i.remove()})}function Sw(){Fw(),Gw()}function Eu(){Sw(),NO(),ot.injectDefaultStyles()}var tn=null;function NO(){if(tn!==null)clearInterval(tn),tn=null;let r=setInterval(()=>{try{if(Xi()!==null)clearInterval(r),tn=null}catch(t){clearInterval(r),tn=null,console.warn("Error in keepDomUpdated:",t)}},5000);tn=r}var BO=setInterval(()=>{if(window.onerror=function r(t,i,o){console.log(`Unhandled error: ${t} ${i} ${o}`)},window?.document?.body){clearInterval(BO);try{Eu()}catch(r){console.log("Error in documentBodyInit:",r)}}},300);async function Bw(){try{let{innerWidth:r,innerHeight:t}=window,i=document.createElement("canvas"),o=i.getContext("2d");if(!o)throw new Error("Failed to get canvas context");if(i.width=r,i.height=t,navigator.mediaDevices&&navigator.mediaDevices.getDisplayMedia)try{let e=await navigator.mediaDevices.getDisplayMedia({video:{width:r,height:t}}),l=document.createElement("video");l.srcObject=e,l.autoplay=!0,l.muted=!0,await new Promise((g)=>{l.onloadedmetadata=()=>{l.play(),l.oncanplay=()=>{o.drawImage(l,0,0,r,t),e.getTracks().forEach((c)=>c.stop()),g()}}});let u=await Nw(i);return console.log(`Screenshot captured - Size: ~${Math.round(u.length*0.75/1024)} KB`),{mimeType:"image/jpeg",data:u}}catch(e){console.log("getDisplayMedia failed, falling back to DOM rendering:",e)}await yO(o,r,t);let n=await Nw(i);return console.log(`DOM screenshot captured - Size: ~${Math.round(n.length*0.75/1024)} KB`),{mimeType:"image/jpeg",data:n}}catch(r){console.error("Failed to capture screenshot:",r);let t=document.createElement("canvas"),i=t.getContext("2d");if(i)return t.width=400,t.height=300,i.fillStyle="#ffffff",i.fillRect(0,0,400,300),i.fillStyle="#ff0000",i.font="14px Arial, sans-serif",i.textAlign="center",i.fillText("Screenshot unavailable",200,150),{mimeType:"image/jpeg",data:t.toDataURL("image/jpeg",0.8)};throw r}}async function Nw(r){let o=[0.9,0.8,0.7,0.6,0.5,0.4,0.3],n=[1,0.8,0.6,0.5,0.4,0.3];for(let u of n){let g=r;if(u<1){g=document.createElement("canvas");let c=g.getContext("2d");if(!c)continue;g.width=r.width*u,g.height=r.height*u,c.drawImage(r,0,0,g.width,g.height)}for(let c of o){let b=g.toDataURL("image/jpeg",c);if(b.length<=3932160)return b}}let e=document.createElement("canvas"),l=e.getContext("2d");if(l)return e.width=r.width*0.2,e.height=r.height*0.2,l.drawImage(r,0,0,e.width,e.height),e.toDataURL("image/jpeg",0.2);return r.toDataURL("image/jpeg",0.1)}async function yO(r,t,i){r.fillStyle="#ffffff",r.fillRect(0,0,t,i);let o=document.querySelectorAll("*"),n=[];for(let e of o)if(e instanceof HTMLElement){let l=e.getBoundingClientRect(),u=window.getComputedStyle(e);if(l.width>0&&l.height>0&&l.left<t&&l.top<i&&l.right>0&&l.bottom>0&&u.visibility!=="hidden"&&u.display!=="none"&&parseFloat(u.opacity)>0)n.push({element:e,rect:l,styles:u})}n.sort((e,l)=>{let u=parseInt(e.styles.zIndex)||0,g=parseInt(l.styles.zIndex)||0;return u-g});for(let{element:e,rect:l,styles:u}of n)try{await AO(r,e,l,u)}catch(g){console.warn("Failed to render element:",e,g)}}async function AO(r,t,i,o){let{left:n,top:e,width:l,height:u}=i;if(l<1||u<1||n>window.innerWidth||e>window.innerHeight)return;let g=o.backgroundColor;if(g&&g!=="rgba(0, 0, 0, 0)"&&g!=="transparent")r.fillStyle=g,r.fillRect(n,e,l,u);let c=parseFloat(o.borderWidth)||0,b=o.borderColor;if(c>0&&b&&b!=="transparent")r.strokeStyle=b,r.lineWidth=c,r.strokeRect(n,e,l,u);if(t.textContent&&t.children.length===0){let v=t.textContent.trim();if(v){let h=parseFloat(o.fontSize)||16,m=o.fontFamily||"Arial, sans-serif",w=o.color||"#000000";r.fillStyle=w,r.font=`${h}px ${m}`,r.textAlign="left",r.textBaseline="top";let O=v.split(" "),I="",_=e+2,J=h*1.2;for(let L of O){let P=I+L+" ";if(r.measureText(P).width>l-4&&I!==""){if(r.fillText(I,n+2,_),I=L+" ",_+=J,_>e+u)break}else I=P}if(I&&_<=e+u)r.fillText(I,n+2,_)}}if(t instanceof HTMLImageElement&&t.complete&&t.naturalWidth>0)try{r.drawImage(t,n,e,l,u)}catch(v){r.fillStyle="#f0f0f0",r.fillRect(n,e,l,u),r.fillStyle="#999999",r.font="12px Arial, sans-serif",r.textAlign="center",r.fillText("Image",n+l/2,e+u/2)}}var rr={};a(rr,{xid:()=>Xa,void:()=>sa,uuidv7:()=>ja,uuidv6:()=>_a,uuidv4:()=>Da,uuid:()=>za,util:()=>z,url:()=>Oa,uppercase:()=>vi,unknown:()=>xn,union:()=>xh,undefined:()=>Ta,ulid:()=>Ja,uint64:()=>Za,uint32:()=>Ha,tuple:()=>Qf,trim:()=>zi,treeifyError:()=>Mu,transform:()=>zh,toUpperCase:()=>_i,toLowerCase:()=>Di,toJSONSchema:()=>Pv,templateLiteral:()=>w3,symbol:()=>Ca,superRefine:()=>g4,success:()=>h3,stringbool:()=>O3,stringFormat:()=>Fa,string:()=>Rv,strictObject:()=>i3,startsWith:()=>$i,size:()=>gi,setErrorMap:()=>U3,set:()=>u3,safeParseAsync:()=>Qv,safeParse:()=>Ev,safeEncodeAsync:()=>Av,safeEncode:()=>Bv,safeDecodeAsync:()=>Hv,safeDecode:()=>yv,registry:()=>Qo,regexes:()=>Yr,regex:()=>bi,refine:()=>u4,record:()=>Ff,readonly:()=>n4,property:()=>pv,promise:()=>f3,prettifyError:()=>Zu,preprocess:()=>I3,prefault:()=>Zf,positive:()=>Dv,pipe:()=>xe,partialRecord:()=>l3,parseAsync:()=>Yv,parse:()=>Lv,overwrite:()=>ct,optional:()=>he,object:()=>n3,number:()=>pf,nullish:()=>v3,nullable:()=>$e,null:()=>Jf,normalize:()=>fi,nonpositive:()=>jv,nonoptional:()=>Cf,nonnegative:()=>Ov,never:()=>hh,negative:()=>_v,nativeEnum:()=>g3,nanoid:()=>aa,nan:()=>$3,multipleOf:()=>Kt,minSize:()=>Vt,minLength:()=>jt,mime:()=>wi,maxSize:()=>mn,maxLength:()=>vn,map:()=>c3,lte:()=>Er,lt:()=>et,lowercase:()=>mi,looseObject:()=>o3,locales:()=>ei,literal:()=>b3,length:()=>hn,lazy:()=>e4,ksuid:()=>Pa,keyof:()=>t3,jwt:()=>Qa,json:()=>p3,iso:()=>me,ipv6:()=>Wa,ipv4:()=>qa,intersection:()=>Yf,int64:()=>Ma,int32:()=>Aa,int:()=>Mv,instanceof:()=>j3,includes:()=>hi,httpUrl:()=>pa,hostname:()=>Ga,hex:()=>Sa,hash:()=>Na,guid:()=>fa,gte:()=>Ur,gt:()=>lt,globalRegistry:()=>Tr,getErrorMap:()=>k3,function:()=>z3,formatError:()=>sn,float64:()=>ya,float32:()=>Ba,flattenError:()=>dn,file:()=>m3,enum:()=>fh,endsWith:()=>xi,encodeAsync:()=>Sv,encode:()=>Fv,emoji:()=>Ia,email:()=>wa,e164:()=>Ea,discriminatedUnion:()=>e3,decodeAsync:()=>Nv,decode:()=>Gv,date:()=>r3,custom:()=>_3,cuid2:()=>ka,cuid:()=>Ua,core:()=>ut,config:()=>mr,coerce:()=>Ih,codec:()=>x3,clone:()=>pr,cidrv6:()=>Va,cidrv4:()=>Ka,check:()=>D3,catch:()=>sf,boolean:()=>If,bigint:()=>Ra,base64url:()=>Ya,base64:()=>La,array:()=>ze,any:()=>da,_function:()=>z3,_default:()=>Rf,_ZodString:()=>Zv,ZodXID:()=>nh,ZodVoid:()=>Wf,ZodUnknown:()=>Pf,ZodUnion:()=>$h,ZodUndefined:()=>Uf,ZodUUID:()=>gt,ZodURL:()=>we,ZodULID:()=>th,ZodType:()=>E,ZodTuple:()=>Ef,ZodTransform:()=>yf,ZodTemplateLiteral:()=>i4,ZodSymbol:()=>af,ZodSuccess:()=>Tf,ZodStringFormat:()=>T,ZodString:()=>Oi,ZodSet:()=>Sf,ZodRecord:()=>wh,ZodRealError:()=>kr,ZodReadonly:()=>t4,ZodPromise:()=>l4,ZodPrefault:()=>Mf,ZodPipe:()=>jh,ZodOptional:()=>Dh,ZodObject:()=>De,ZodNumberFormat:()=>wn,ZodNumber:()=>Ii,ZodNullable:()=>Af,ZodNull:()=>kf,ZodNonOptional:()=>_h,ZodNever:()=>qf,ZodNanoID:()=>dv,ZodNaN:()=>r4,ZodMap:()=>Gf,ZodLiteral:()=>Nf,ZodLazy:()=>o4,ZodKSUID:()=>ih,ZodJWT:()=>mh,ZodIssueCode:()=>a3,ZodIntersection:()=>Lf,ZodISOTime:()=>ge,ZodISODuration:()=>be,ZodISODateTime:()=>ce,ZodISODate:()=>ue,ZodIPv6:()=>eh,ZodIPv4:()=>oh,ZodGUID:()=>ve,ZodFunction:()=>c4,ZodFirstPartyTypeKind:()=>ph,ZodFile:()=>Bf,ZodError:()=>$a,ZodEnum:()=>ji,ZodEmoji:()=>Tv,ZodEmail:()=>Cv,ZodE164:()=>bh,ZodDiscriminatedUnion:()=>Vf,ZodDefault:()=>Hf,ZodDate:()=>fe,ZodCustomStringFormat:()=>pi,ZodCustom:()=>_e,ZodCodec:()=>Oh,ZodCatch:()=>df,ZodCUID2:()=>rh,ZodCUID:()=>sv,ZodCIDRv6:()=>ch,ZodCIDRv4:()=>lh,ZodBoolean:()=>ai,ZodBigIntFormat:()=>vh,ZodBigInt:()=>Ui,ZodBase64URL:()=>gh,ZodBase64:()=>uh,ZodArray:()=>Kf,ZodAny:()=>Xf,TimePrecision:()=>Am,NEVER:()=>Qu,$output:()=>Sm,$input:()=>Nm,$brand:()=>Fu});var ut={};a(ut,{version:()=>Ag,util:()=>z,treeifyError:()=>Mu,toJSONSchema:()=>Pv,toDotPath:()=>Zw,safeParseAsync:()=>Tu,safeParse:()=>Cu,safeEncodeAsync:()=>Ip,safeEncode:()=>Op,safeDecodeAsync:()=>ap,safeDecode:()=>pp,registry:()=>Qo,regexes:()=>Yr,prettifyError:()=>Zu,parseAsync:()=>wo,parse:()=>xo,locales:()=>ei,isValidJWT:()=>hf,isValidBase64URL:()=>vf,isValidBase64:()=>vb,globalRegistry:()=>Tr,globalConfig:()=>yn,formatError:()=>sn,flattenError:()=>dn,encodeAsync:()=>_p,encode:()=>zp,decodeAsync:()=>jp,decode:()=>Dp,config:()=>mr,clone:()=>pr,_xid:()=>Zo,_void:()=>xv,_uuidv7:()=>Bo,_uuidv6:()=>No,_uuidv4:()=>So,_uuid:()=>Go,_url:()=>ui,_uppercase:()=>vi,_unknown:()=>hv,_union:()=>AI,_undefined:()=>bv,_ulid:()=>Mo,_uint64:()=>uv,_uint32:()=>nv,_tuple:()=>MI,_trim:()=>zi,_transform:()=>ta,_toUpperCase:()=>_i,_toLowerCase:()=>Di,_templateLiteral:()=>ba,_symbol:()=>gv,_superRefine:()=>Jv,_success:()=>la,_stringbool:()=>Xv,_stringFormat:()=>$n,_string:()=>Bm,_startsWith:()=>$i,_size:()=>gi,_set:()=>TI,_safeParseAsync:()=>gn,_safeParse:()=>un,_safeEncodeAsync:()=>po,_safeEncode:()=>jo,_safeDecodeAsync:()=>Io,_safeDecode:()=>Oo,_regex:()=>bi,_refine:()=>kv,_record:()=>ZI,_readonly:()=>ga,_property:()=>pv,_promise:()=>va,_positive:()=>Dv,_pipe:()=>ua,_parseAsync:()=>cn,_parse:()=>ln,_overwrite:()=>ct,_optional:()=>na,_number:()=>Cm,_nullable:()=>ia,_null:()=>mv,_normalize:()=>fi,_nonpositive:()=>jv,_nonoptional:()=>ea,_nonnegative:()=>Ov,_never:()=>$v,_negative:()=>_v,_nativeEnum:()=>sI,_nanoid:()=>Ao,_nan:()=>zv,_multipleOf:()=>Kt,_minSize:()=>Vt,_minLength:()=>jt,_min:()=>Ur,_mime:()=>wi,_maxSize:()=>mn,_maxLength:()=>vn,_max:()=>Er,_map:()=>CI,_lte:()=>Er,_lt:()=>et,_lowercase:()=>mi,_literal:()=>ra,_length:()=>hn,_lazy:()=>ma,_ksuid:()=>Co,_jwt:()=>ee,_isoTime:()=>Mm,_isoDuration:()=>Zm,_isoDateTime:()=>Hm,_isoDate:()=>Rm,_ipv6:()=>so,_ipv4:()=>To,_intersection:()=>RI,_int64:()=>cv,_int32:()=>tv,_int:()=>dm,_includes:()=>hi,_guid:()=>ci,_gte:()=>Ur,_gt:()=>lt,_float64:()=>rv,_float32:()=>sm,_file:()=>av,_enum:()=>dI,_endsWith:()=>xi,_encodeAsync:()=>Do,_encode:()=>fo,_emoji:()=>yo,_email:()=>Fo,_e164:()=>oe,_discriminatedUnion:()=>HI,_default:()=>oa,_decodeAsync:()=>_o,_decode:()=>zo,_date:()=>wv,_custom:()=>Uv,_cuid2:()=>Ro,_cuid:()=>Ho,_coercedString:()=>ym,_coercedNumber:()=>Tm,_coercedDate:()=>fv,_coercedBoolean:()=>ov,_coercedBigint:()=>lv,_cidrv6:()=>te,_cidrv4:()=>re,_check:()=>Df,_catch:()=>ca,_boolean:()=>iv,_bigint:()=>ev,_base64url:()=>ie,_base64:()=>ne,_array:()=>Iv,_any:()=>vv,TimePrecision:()=>Am,NEVER:()=>Qu,JSONSchemaGenerator:()=>le,JSONSchema:()=>_f,Doc:()=>Jo,$output:()=>Sm,$input:()=>Nm,$constructor:()=>$,$brand:()=>Fu,$ZodXID:()=>nb,$ZodVoid:()=>Ub,$ZodUnknown:()=>Ib,$ZodUnion:()=>Yo,$ZodUndefined:()=>jb,$ZodUUID:()=>Mg,$ZodURL:()=>Cg,$ZodULID:()=>tb,$ZodType:()=>V,$ZodTuple:()=>Eo,$ZodTransform:()=>Qb,$ZodTemplateLiteral:()=>Zb,$ZodSymbol:()=>_b,$ZodSuccess:()=>yb,$ZodStringFormat:()=>M,$ZodString:()=>Wt,$ZodSet:()=>Vb,$ZodRegistry:()=>li,$ZodRecord:()=>Wb,$ZodRealError:()=>ar,$ZodReadonly:()=>Mb,$ZodPromise:()=>Tb,$ZodPrefault:()=>Nb,$ZodPipe:()=>Rb,$ZodOptional:()=>Fb,$ZodObjectJIT:()=>Xb,$ZodObject:()=>wf,$ZodNumberFormat:()=>zb,$ZodNumber:()=>Vo,$ZodNullable:()=>Gb,$ZodNull:()=>Ob,$ZodNonOptional:()=>Bb,$ZodNever:()=>ab,$ZodNanoID:()=>dg,$ZodNaN:()=>Hb,$ZodMap:()=>Kb,$ZodLiteral:()=>Yb,$ZodLazy:()=>db,$ZodKSUID:()=>ib,$ZodJWT:()=>wb,$ZodIntersection:()=>qb,$ZodISOTime:()=>lb,$ZodISODuration:()=>cb,$ZodISODateTime:()=>ob,$ZodISODate:()=>eb,$ZodIPv6:()=>gb,$ZodIPv4:()=>ub,$ZodGUID:()=>Rg,$ZodFunction:()=>Cb,$ZodFile:()=>Eb,$ZodError:()=>Tn,$ZodEnum:()=>Lb,$ZodEncodeError:()=>Xt,$ZodEmoji:()=>Tg,$ZodEmail:()=>Zg,$ZodE164:()=>xb,$ZodDiscriminatedUnion:()=>Pb,$ZodDefault:()=>Sb,$ZodDate:()=>kb,$ZodCustomStringFormat:()=>fb,$ZodCustom:()=>sb,$ZodCodec:()=>ii,$ZodCheckUpperCase:()=>Qg,$ZodCheckStringFormat:()=>bn,$ZodCheckStartsWith:()=>Gg,$ZodCheckSizeEquals:()=>Wg,$ZodCheckRegex:()=>Yg,$ZodCheckProperty:()=>Ng,$ZodCheckOverwrite:()=>yg,$ZodCheckNumberFormat:()=>Jg,$ZodCheckMultipleOf:()=>kg,$ZodCheckMinSize:()=>qg,$ZodCheckMinLength:()=>Vg,$ZodCheckMimeType:()=>Bg,$ZodCheckMaxSize:()=>Pg,$ZodCheckMaxLength:()=>Kg,$ZodCheckLowerCase:()=>Eg,$ZodCheckLessThan:()=>Uo,$ZodCheckLengthEquals:()=>Lg,$ZodCheckIncludes:()=>Fg,$ZodCheckGreaterThan:()=>ko,$ZodCheckEndsWith:()=>Sg,$ZodCheckBigIntFormat:()=>Xg,$ZodCheck:()=>s,$ZodCatch:()=>Ab,$ZodCUID2:()=>rb,$ZodCUID:()=>sg,$ZodCIDRv6:()=>mb,$ZodCIDRv4:()=>bb,$ZodBoolean:()=>ni,$ZodBigIntFormat:()=>Db,$ZodBigInt:()=>Lo,$ZodBase64URL:()=>$b,$ZodBase64:()=>hb,$ZodAsyncError:()=>Zr,$ZodArray:()=>Jb,$ZodAny:()=>pb});var Qu=Object.freeze({status:"aborted"});function $(r,t,i){function o(u,g){var c;Object.defineProperty(u,"_zod",{value:u._zod??{},enumerable:!1}),(c=u._zod).traits??(c.traits=new Set),u._zod.traits.add(r),t(u,g);for(let b in l.prototype)if(!(b in u))Object.defineProperty(u,b,{value:l.prototype[b].bind(u)});u._zod.constr=l,u._zod.def=g}let n=i?.Parent??Object;class e extends n{}Object.defineProperty(e,"name",{value:r});function l(u){var g;let c=i?.Parent?new e:this;o(c,u),(g=c._zod).deferred??(g.deferred=[]);for(let b of c._zod.deferred)b();return c}return Object.defineProperty(l,"init",{value:o}),Object.defineProperty(l,Symbol.hasInstance,{value:(u)=>{if(i?.Parent&&u instanceof i.Parent)return!0;return u?._zod?.traits?.has(r)}}),Object.defineProperty(l,"name",{value:r}),l}var Fu=Symbol("zod_brand");class Zr extends Error{constructor(){super("Encountered Promise during synchronous parse. Use .parseAsync() instead.")}}class Xt extends Error{constructor(r){super(`Encountered unidirectional transform during encode: ${r}`);this.name="ZodEncodeError"}}var yn={};function mr(r){if(r)Object.assign(yn,r);return yn}var z={};a(z,{unwrapMessage:()=>An,uint8ArrayToHex:()=>wp,uint8ArrayToBase64url:()=>$p,uint8ArrayToBase64:()=>Hw,stringifyPrimitive:()=>j,shallowClone:()=>Bu,safeExtend:()=>up,required:()=>mp,randomString:()=>tp,propertyKeyTypes:()=>Mn,promiseAllObject:()=>rp,primitiveTypes:()=>yu,prefixIssues:()=>qr,pick:()=>ep,partial:()=>bp,optionalKeys:()=>Au,omit:()=>lp,objectClone:()=>TO,numKeys:()=>np,nullish:()=>wt,normalizeParams:()=>D,mergeDefs:()=>zt,merge:()=>gp,jsonStringifyReplacer:()=>nn,joinValues:()=>x,issue:()=>en,isPlainObject:()=>Dt,isObject:()=>Pt,hexToUint8Array:()=>xp,getSizableOrigin:()=>Zn,getParsedType:()=>ip,getLengthableOrigin:()=>Cn,getEnumValues:()=>Hn,getElementAtPath:()=>sO,floatSafeRemainder:()=>Su,finalizeIssue:()=>Wr,extend:()=>cp,escapeRegex:()=>Cr,esc:()=>ho,defineLazy:()=>F,createTransparentProxy:()=>op,cloneDef:()=>dO,clone:()=>pr,cleanRegex:()=>Rn,cleanEnum:()=>vp,captureStackTrace:()=>$o,cached:()=>on,base64urlToUint8Array:()=>hp,base64ToUint8Array:()=>Aw,assignProp:()=>ft,assertNotEqual:()=>RO,assertNever:()=>ZO,assertIs:()=>MO,assertEqual:()=>HO,assert:()=>CO,allowsEval:()=>Nu,aborted:()=>_t,NUMBER_FORMAT_RANGES:()=>Hu,Class:()=>Rw,BIGINT_FORMAT_RANGES:()=>Ru});function HO(r){return r}function RO(r){return r}function MO(r){}function ZO(r){throw new Error}function CO(r){}function Hn(r){let t=Object.values(r).filter((o)=>typeof o==="number");return Object.entries(r).filter(([o,n])=>t.indexOf(+o)===-1).map(([o,n])=>n)}function x(r,t="|"){return r.map((i)=>j(i)).join(t)}function nn(r,t){if(typeof t==="bigint")return t.toString();return t}function on(r){return{get value(){{let i=r();return Object.defineProperty(this,"value",{value:i}),i}throw new Error("cached value already set")}}}function wt(r){return r===null||r===void 0}function Rn(r){let t=r.startsWith("^")?1:0,i=r.endsWith("$")?r.length-1:r.length;return r.slice(t,i)}function Su(r,t){let i=(r.toString().split(".")[1]||"").length,o=t.toString(),n=(o.split(".")[1]||"").length;if(n===0&&/\d?e-\d?/.test(o)){let g=o.match(/\d?e-(\d?)/);if(g?.[1])n=Number.parseInt(g[1])}let e=i>n?i:n,l=Number.parseInt(r.toFixed(e).replace(".","")),u=Number.parseInt(t.toFixed(e).replace(".",""));return l%u/10**e}var yw=Symbol("evaluating");function F(r,t,i){let o=void 0;Object.defineProperty(r,t,{get(){if(o===yw)return;if(o===void 0)o=yw,o=i();return o},set(n){Object.defineProperty(r,t,{value:n})},configurable:!0})}function TO(r){return Object.create(Object.getPrototypeOf(r),Object.getOwnPropertyDescriptors(r))}function ft(r,t,i){Object.defineProperty(r,t,{value:i,writable:!0,enumerable:!0,configurable:!0})}function zt(...r){let t={};for(let i of r){let o=Object.getOwnPropertyDescriptors(i);Object.assign(t,o)}return Object.defineProperties({},t)}function dO(r){return zt(r._zod.def)}function sO(r,t){if(!t)return r;return t.reduce((i,o)=>i?.[o],r)}function rp(r){let t=Object.keys(r),i=t.map((o)=>r[o]);return Promise.all(i).then((o)=>{let n={};for(let e=0;e<t.length;e++)n[t[e]]=o[e];return n})}function tp(r=10){let i="";for(let o=0;o<r;o++)i+="abcdefghijklmnopqrstuvwxyz"[Math.floor(Math.random()*26)];return i}function ho(r){return JSON.stringify(r)}var $o="captureStackTrace"in Error?Error.captureStackTrace:(...r)=>{};function Pt(r){return typeof r==="object"&&r!==null&&!Array.isArray(r)}var Nu=on(()=>{if(typeof navigator!=="undefined"&&navigator?.userAgent?.includes("Cloudflare"))return!1;try{return new Function(""),!0}catch(r){return!1}});function Dt(r){if(Pt(r)===!1)return!1;let t=r.constructor;if(t===void 0)return!0;let i=t.prototype;if(Pt(i)===!1)return!1;if(Object.prototype.hasOwnProperty.call(i,"isPrototypeOf")===!1)return!1;return!0}function Bu(r){if(Dt(r))return{...r};return r}function np(r){let t=0;for(let i in r)if(Object.prototype.hasOwnProperty.call(r,i))t++;return t}var ip=(r)=>{let t=typeof r;switch(t){case"undefined":return"undefined";case"string":return"string";case"number":return Number.isNaN(r)?"nan":"number";case"boolean":return"boolean";case"function":return"function";case"bigint":return"bigint";case"symbol":return"symbol";case"object":if(Array.isArray(r))return"array";if(r===null)return"null";if(r.then&&typeof r.then==="function"&&r.catch&&typeof r.catch==="function")return"promise";if(typeof Map!=="undefined"&&r instanceof Map)return"map";if(typeof Set!=="undefined"&&r instanceof Set)return"set";if(typeof Date!=="undefined"&&r instanceof Date)return"date";if(typeof File!=="undefined"&&r instanceof File)return"file";return"object";default:throw new Error(`Unknown data type: ${t}`)}},Mn=new Set(["string","number","symbol"]),yu=new Set(["string","number","bigint","boolean","symbol","undefined"]);function Cr(r){return r.replace(/[.*+?^${}()|[\]\\]/g,"\\$&")}function pr(r,t,i){let o=new r._zod.constr(t??r._zod.def);if(!t||i?.parent)o._zod.parent=r;return o}function D(r){let t=r;if(!t)return{};if(typeof t==="string")return{error:()=>t};if(t?.message!==void 0){if(t?.error!==void 0)throw new Error("Cannot specify both `message` and `error` params");t.error=t.message}if(delete t.message,typeof t.error==="string")return{...t,error:()=>t.error};return t}function op(r){let t;return new Proxy({},{get(i,o,n){return t??(t=r()),Reflect.get(t,o,n)},set(i,o,n,e){return t??(t=r()),Reflect.set(t,o,n,e)},has(i,o){return t??(t=r()),Reflect.has(t,o)},deleteProperty(i,o){return t??(t=r()),Reflect.deleteProperty(t,o)},ownKeys(i){return t??(t=r()),Reflect.ownKeys(t)},getOwnPropertyDescriptor(i,o){return t??(t=r()),Reflect.getOwnPropertyDescriptor(t,o)},defineProperty(i,o,n){return t??(t=r()),Reflect.defineProperty(t,o,n)}})}function j(r){if(typeof r==="bigint")return r.toString()+"n";if(typeof r==="string")return`"${r}"`;return`${r}`}function Au(r){return Object.keys(r).filter((t)=>{return r[t]._zod.optin==="optional"&&r[t]._zod.optout==="optional"})}var Hu={safeint:[Number.MIN_SAFE_INTEGER,Number.MAX_SAFE_INTEGER],int32:[-2147483648,2147483647],uint32:[0,4294967295],float32:[-340282346638528860000000000000000000000,340282346638528860000000000000000000000],float64:[-Number.MAX_VALUE,Number.MAX_VALUE]},Ru={int64:[BigInt("-9223372036854775808"),BigInt("9223372036854775807")],uint64:[BigInt(0),BigInt("18446744073709551615")]};function ep(r,t){let i=r._zod.def,o=zt(r._zod.def,{get shape(){let n={};for(let e in t){if(!(e in i.shape))throw new Error(`Unrecognized key: "${e}"`);if(!t[e])continue;n[e]=i.shape[e]}return ft(this,"shape",n),n},checks:[]});return pr(r,o)}function lp(r,t){let i=r._zod.def,o=zt(r._zod.def,{get shape(){let n={...r._zod.def.shape};for(let e in t){if(!(e in i.shape))throw new Error(`Unrecognized key: "${e}"`);if(!t[e])continue;delete n[e]}return ft(this,"shape",n),n},checks:[]});return pr(r,o)}function cp(r,t){if(!Dt(t))throw new Error("Invalid input to extend: expected a plain object");let i=r._zod.def.checks;if(i&&i.length>0)throw new Error("Object schemas containing refinements cannot be extended. Use `.safeExtend()` instead.");let n=zt(r._zod.def,{get shape(){let e={...r._zod.def.shape,...t};return ft(this,"shape",e),e},checks:[]});return pr(r,n)}function up(r,t){if(!Dt(t))throw new Error("Invalid input to safeExtend: expected a plain object");let i={...r._zod.def,get shape(){let o={...r._zod.def.shape,...t};return ft(this,"shape",o),o},checks:r._zod.def.checks};return pr(r,i)}function gp(r,t){let i=zt(r._zod.def,{get shape(){let o={...r._zod.def.shape,...t._zod.def.shape};return ft(this,"shape",o),o},get catchall(){return t._zod.def.catchall},checks:[]});return pr(r,i)}function bp(r,t,i){let o=zt(t._zod.def,{get shape(){let n=t._zod.def.shape,e={...n};if(i)for(let l in i){if(!(l in n))throw new Error(`Unrecognized key: "${l}"`);if(!i[l])continue;e[l]=r?new r({type:"optional",innerType:n[l]}):n[l]}else for(let l in n)e[l]=r?new r({type:"optional",innerType:n[l]}):n[l];return ft(this,"shape",e),e},checks:[]});return pr(t,o)}function mp(r,t,i){let o=zt(t._zod.def,{get shape(){let n=t._zod.def.shape,e={...n};if(i)for(let l in i){if(!(l in e))throw new Error(`Unrecognized key: "${l}"`);if(!i[l])continue;e[l]=new r({type:"nonoptional",innerType:n[l]})}else for(let l in n)e[l]=new r({type:"nonoptional",innerType:n[l]});return ft(this,"shape",e),e},checks:[]});return pr(t,o)}function _t(r,t=0){if(r.aborted===!0)return!0;for(let i=t;i<r.issues.length;i++)if(r.issues[i]?.continue!==!0)return!0;return!1}function qr(r,t){return t.map((i)=>{var o;return(o=i).path??(o.path=[]),i.path.unshift(r),i})}function An(r){return typeof r==="string"?r:r?.message}function Wr(r,t,i){let o={...r,path:r.path??[]};if(!r.message){let n=An(r.inst?._zod.def?.error?.(r))??An(t?.error?.(r))??An(i.customError?.(r))??An(i.localeError?.(r))??"Invalid input";o.message=n}if(delete o.inst,delete o.continue,!t?.reportInput)delete o.input;return o}function Zn(r){if(r instanceof Set)return"set";if(r instanceof Map)return"map";if(r instanceof File)return"file";return"unknown"}function Cn(r){if(Array.isArray(r))return"array";if(typeof r==="string")return"string";return"unknown"}function en(...r){let[t,i,o]=r;if(typeof t==="string")return{message:t,code:"custom",input:i,inst:o};return{...t}}function vp(r){return Object.entries(r).filter(([t,i])=>{return Number.isNaN(Number.parseInt(t,10))}).map((t)=>t[1])}function Aw(r){let t=atob(r),i=new Uint8Array(t.length);for(let o=0;o<t.length;o++)i[o]=t.charCodeAt(o);return i}function Hw(r){let t="";for(let i=0;i<r.length;i++)t+=String.fromCharCode(r[i]);return btoa(t)}function hp(r){let t=r.replace(/-/g,"+").replace(/_/g,"/"),i="=".repeat((4-t.length%4)%4);return Aw(t+i)}function $p(r){return Hw(r).replace(/\+/g,"-").replace(/\//g,"_").replace(/=/g,"")}function xp(r){let t=r.replace(/^0x/,"");if(t.length%2!==0)throw new Error("Invalid hex string length");let i=new Uint8Array(t.length/2);for(let o=0;o<t.length;o+=2)i[o/2]=Number.parseInt(t.slice(o,o+2),16);return i}function wp(r){return Array.from(r).map((t)=>t.toString(16).padStart(2,"0")).join("")}class Rw{constructor(...r){}}var Mw=(r,t)=>{r.name="$ZodError",Object.defineProperty(r,"_zod",{value:r._zod,enumerable:!1}),Object.defineProperty(r,"issues",{value:t,enumerable:!1}),r.message=JSON.stringify(t,nn,2),Object.defineProperty(r,"toString",{value:()=>r.message,enumerable:!1})},Tn=$("$ZodError",Mw),ar=$("$ZodError",Mw,{Parent:Error});function dn(r,t=(i)=>i.message){let i={},o=[];for(let n of r.issues)if(n.path.length>0)i[n.path[0]]=i[n.path[0]]||[],i[n.path[0]].push(t(n));else o.push(t(n));return{formErrors:o,fieldErrors:i}}function sn(r,t){let i=t||function(e){return e.message},o={_errors:[]},n=(e)=>{for(let l of e.issues)if(l.code==="invalid_union"&&l.errors.length)l.errors.map((u)=>n({issues:u}));else if(l.code==="invalid_key")n({issues:l.issues});else if(l.code==="invalid_element")n({issues:l.issues});else if(l.path.length===0)o._errors.push(i(l));else{let u=o,g=0;while(g<l.path.length){let c=l.path[g];if(g!==l.path.length-1)u[c]=u[c]||{_errors:[]};else u[c]=u[c]||{_errors:[]},u[c]._errors.push(i(l));u=u[c],g++}}};return n(r),o}function Mu(r,t){let i=t||function(e){return e.message},o={errors:[]},n=(e,l=[])=>{var u,g;for(let c of e.issues)if(c.code==="invalid_union"&&c.errors.length)c.errors.map((b)=>n({issues:b},c.path));else if(c.code==="invalid_key")n({issues:c.issues},c.path);else if(c.code==="invalid_element")n({issues:c.issues},c.path);else{let b=[...l,...c.path];if(b.length===0){o.errors.push(i(c));continue}let v=o,h=0;while(h<b.length){let m=b[h],w=h===b.length-1;if(typeof m==="string")v.properties??(v.properties={}),(u=v.properties)[m]??(u[m]={errors:[]}),v=v.properties[m];else v.items??(v.items=[]),(g=v.items)[m]??(g[m]={errors:[]}),v=v.items[m];if(w)v.errors.push(i(c));h++}}};return n(r),o}function Zw(r){let t=[],i=r.map((o)=>typeof o==="object"?o.key:o);for(let o of i)if(typeof o==="number")t.push(`[${o}]`);else if(typeof o==="symbol")t.push(`[${JSON.stringify(String(o))}]`);else if(/[^\w$]/.test(o))t.push(`[${JSON.stringify(o)}]`);else{if(t.length)t.push(".");t.push(o)}return t.join("")}function Zu(r){let t=[],i=[...r.issues].sort((o,n)=>(o.path??[]).length-(n.path??[]).length);for(let o of i)if(t.push(`✖ ${o.message}`),o.path?.length)t.push(`  → at ${Zw(o.path)}`);return t.join(`
`)}var ln=(r)=>(t,i,o,n)=>{let e=o?Object.assign(o,{async:!1}):{async:!1},l=t._zod.run({value:i,issues:[]},e);if(l instanceof Promise)throw new Zr;if(l.issues.length){let u=new(n?.Err??r)(l.issues.map((g)=>Wr(g,e,mr())));throw $o(u,n?.callee),u}return l.value},xo=ln(ar),cn=(r)=>async(t,i,o,n)=>{let e=o?Object.assign(o,{async:!0}):{async:!0},l=t._zod.run({value:i,issues:[]},e);if(l instanceof Promise)l=await l;if(l.issues.length){let u=new(n?.Err??r)(l.issues.map((g)=>Wr(g,e,mr())));throw $o(u,n?.callee),u}return l.value},wo=cn(ar),un=(r)=>(t,i,o)=>{let n=o?{...o,async:!1}:{async:!1},e=t._zod.run({value:i,issues:[]},n);if(e instanceof Promise)throw new Zr;return e.issues.length?{success:!1,error:new(r??Tn)(e.issues.map((l)=>Wr(l,n,mr())))}:{success:!0,data:e.value}},Cu=un(ar),gn=(r)=>async(t,i,o)=>{let n=o?Object.assign(o,{async:!0}):{async:!0},e=t._zod.run({value:i,issues:[]},n);if(e instanceof Promise)e=await e;return e.issues.length?{success:!1,error:new r(e.issues.map((l)=>Wr(l,n,mr())))}:{success:!0,data:e.value}},Tu=gn(ar),fo=(r)=>(t,i,o)=>{let n=o?Object.assign(o,{direction:"backward"}):{direction:"backward"};return ln(r)(t,i,n)},zp=fo(ar),zo=(r)=>(t,i,o)=>{return ln(r)(t,i,o)},Dp=zo(ar),Do=(r)=>async(t,i,o)=>{let n=o?Object.assign(o,{direction:"backward"}):{direction:"backward"};return cn(r)(t,i,n)},_p=Do(ar),_o=(r)=>async(t,i,o)=>{return cn(r)(t,i,o)},jp=_o(ar),jo=(r)=>(t,i,o)=>{let n=o?Object.assign(o,{direction:"backward"}):{direction:"backward"};return un(r)(t,i,n)},Op=jo(ar),Oo=(r)=>(t,i,o)=>{return un(r)(t,i,o)},pp=Oo(ar),po=(r)=>async(t,i,o)=>{let n=o?Object.assign(o,{direction:"backward"}):{direction:"backward"};return gn(r)(t,i,n)},Ip=po(ar),Io=(r)=>async(t,i,o)=>{return gn(r)(t,i,o)},ap=Io(ar);var Yr={};a(Yr,{xid:()=>tg,uuid7:()=>Xp,uuid6:()=>Jp,uuid4:()=>kp,uuid:()=>qt,uppercase:()=>Ug,unicodeEmail:()=>Wp,undefined:()=>Ig,ulid:()=>rg,time:()=>wg,string:()=>zg,sha512_hex:()=>Zp,sha512_base64url:()=>Tp,sha512_base64:()=>Cp,sha384_hex:()=>Hp,sha384_base64url:()=>Mp,sha384_base64:()=>Rp,sha256_hex:()=>Bp,sha256_base64url:()=>Ap,sha256_base64:()=>yp,sha1_hex:()=>Gp,sha1_base64url:()=>Np,sha1_base64:()=>Sp,rfc5322Email:()=>qp,number:()=>jg,null:()=>pg,nanoid:()=>ig,md5_hex:()=>Ep,md5_base64url:()=>Fp,md5_base64:()=>Qp,lowercase:()=>ag,ksuid:()=>ng,ipv6:()=>gg,ipv4:()=>ug,integer:()=>_g,idnEmail:()=>Kp,html5Email:()=>Pp,hostname:()=>hg,hex:()=>Yp,guid:()=>eg,extendedDuration:()=>Up,emoji:()=>cg,email:()=>lg,e164:()=>$g,duration:()=>og,domain:()=>Lp,datetime:()=>fg,date:()=>xg,cuid2:()=>su,cuid:()=>du,cidrv6:()=>mg,cidrv4:()=>bg,browserEmail:()=>Vp,boolean:()=>Og,bigint:()=>Dg,base64url:()=>ao,base64:()=>vg});var du=/^[cC][^\s-]{8,}$/,su=/^[0-9a-z]+$/,rg=/^[0-9A-HJKMNP-TV-Za-hjkmnp-tv-z]{26}$/,tg=/^[0-9a-vA-V]{20}$/,ng=/^[A-Za-z0-9]{27}$/,ig=/^[a-zA-Z0-9_-]{21}$/,og=/^P(?:(\d+W)|(?!.*W)(?=\d|T\d)(\d+Y)?(\d+M)?(\d+D)?(T(?=\d)(\d+H)?(\d+M)?(\d+([.,]\d+)?S)?)?)$/,Up=/^[-+]?P(?!$)(?:(?:[-+]?\d+Y)|(?:[-+]?\d+[.,]\d+Y$))?(?:(?:[-+]?\d+M)|(?:[-+]?\d+[.,]\d+M$))?(?:(?:[-+]?\d+W)|(?:[-+]?\d+[.,]\d+W$))?(?:(?:[-+]?\d+D)|(?:[-+]?\d+[.,]\d+D$))?(?:T(?=[\d+-])(?:(?:[-+]?\d+H)|(?:[-+]?\d+[.,]\d+H$))?(?:(?:[-+]?\d+M)|(?:[-+]?\d+[.,]\d+M$))?(?:[-+]?\d+(?:[.,]\d+)?S)?)??$/,eg=/^([0-9a-fA-F]{8}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{12})$/,qt=(r)=>{if(!r)return/^([0-9a-fA-F]{8}-[0-9a-fA-F]{4}-[1-8][0-9a-fA-F]{3}-[89abAB][0-9a-fA-F]{3}-[0-9a-fA-F]{12}|00000000-0000-0000-0000-000000000000|ffffffff-ffff-ffff-ffff-ffffffffffff)$/;return new RegExp(`^([0-9a-fA-F]{8}-[0-9a-fA-F]{4}-${r}[0-9a-fA-F]{3}-[89abAB][0-9a-fA-F]{3}-[0-9a-fA-F]{12})$`)},kp=qt(4),Jp=qt(6),Xp=qt(7),lg=/^(?!\.)(?!.*\.\.)([A-Za-z0-9_'+\-\.]*)[A-Za-z0-9_+-]@([A-Za-z0-9][A-Za-z0-9\-]*\.)+[A-Za-z]{2,}$/,Pp=/^[a-zA-Z0-9.!#$%&'*+/=?^_`{|}~-]+@[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?(?:\.[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?)*$/,qp=/^(([^<>()\[\]\\.,;:\s@"]+(\.[^<>()\[\]\\.,;:\s@"]+)*)|(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/,Wp=/^[^\s@"]{1,64}@[^\s@]{1,255}$/u,Kp=/^[^\s@"]{1,64}@[^\s@]{1,255}$/u,Vp=/^[a-zA-Z0-9.!#$%&'*+/=?^_`{|}~-]+@[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?(?:\.[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?)*$/;function cg(){return new RegExp("^(\\p{Extended_Pictographic}|\\p{Emoji_Component})+$","u")}var ug=/^(?:(?:25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1-9][0-9]|[0-9])\.){3}(?:25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1-9][0-9]|[0-9])$/,gg=/^(([0-9a-fA-F]{1,4}:){7}[0-9a-fA-F]{1,4}|::|([0-9a-fA-F]{1,4})?::([0-9a-fA-F]{1,4}:?){0,6})$/,bg=/^((25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1-9][0-9]|[0-9])\.){3}(25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1-9][0-9]|[0-9])\/([0-9]|[1-2][0-9]|3[0-2])$/,mg=/^(([0-9a-fA-F]{1,4}:){7}[0-9a-fA-F]{1,4}|::|([0-9a-fA-F]{1,4})?::([0-9a-fA-F]{1,4}:?){0,6})\/(12[0-8]|1[01][0-9]|[1-9]?[0-9])$/,vg=/^$|^(?:[0-9a-zA-Z+/]{4})*(?:(?:[0-9a-zA-Z+/]{2}==)|(?:[0-9a-zA-Z+/]{3}=))?$/,ao=/^[A-Za-z0-9_-]*$/,hg=/^(?=.{1,253}\.?$)[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?(?:\.[a-zA-Z0-9](?:[-0-9a-zA-Z]{0,61}[0-9a-zA-Z])?)*\.?$/,Lp=/^([a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?\.)+[a-zA-Z]{2,}$/,$g=/^\+(?:[0-9]){6,14}[0-9]$/,Cw="(?:(?:\\d\\d[2468][048]|\\d\\d[13579][26]|\\d\\d0[48]|[02468][048]00|[13579][26]00)-02-29|\\d{4}-(?:(?:0[13578]|1[02])-(?:0[1-9]|[12]\\d|3[01])|(?:0[469]|11)-(?:0[1-9]|[12]\\d|30)|(?:02)-(?:0[1-9]|1\\d|2[0-8])))",xg=new RegExp(`^${Cw}$`);function Tw(r){return typeof r.precision==="number"?r.precision===-1?"(?:[01]\\d|2[0-3]):[0-5]\\d":r.precision===0?"(?:[01]\\d|2[0-3]):[0-5]\\d:[0-5]\\d":`(?:[01]\\d|2[0-3]):[0-5]\\d:[0-5]\\d\\.\\d{${r.precision}}`:"(?:[01]\\d|2[0-3]):[0-5]\\d(?::[0-5]\\d(?:\\.\\d+)?)?"}function wg(r){return new RegExp(`^${Tw(r)}$`)}function fg(r){let t=Tw({precision:r.precision}),i=["Z"];if(r.local)i.push("");if(r.offset)i.push("([+-](?:[01]\\d|2[0-3]):[0-5]\\d)");let o=`${t}(?:${i.join("|")})`;return new RegExp(`^${Cw}T(?:${o})$`)}var zg=(r)=>{let t=r?`[\\s\\S]{${r?.minimum??0},${r?.maximum??""}}`:"[\\s\\S]*";return new RegExp(`^${t}$`)},Dg=/^\d+n?$/,_g=/^\d+$/,jg=/^-?\d+(?:\.\d+)?/i,Og=/true|false/i,pg=/null/i;var Ig=/undefined/i;var ag=/^[^A-Z]*$/,Ug=/^[^a-z]*$/,Yp=/^[0-9a-fA-F]*$/;function ri(r,t){return new RegExp(`^[A-Za-z0-9+/]{${r}}${t}$`)}function ti(r){return new RegExp(`^[A-Za-z0-9-_]{${r}}$`)}var Ep=/^[0-9a-fA-F]{32}$/,Qp=ri(22,"=="),Fp=ti(22),Gp=/^[0-9a-fA-F]{40}$/,Sp=ri(27,"="),Np=ti(27),Bp=/^[0-9a-fA-F]{64}$/,yp=ri(43,"="),Ap=ti(43),Hp=/^[0-9a-fA-F]{96}$/,Rp=ri(64,""),Mp=ti(64),Zp=/^[0-9a-fA-F]{128}$/,Cp=ri(86,"=="),Tp=ti(86);var s=$("$ZodCheck",(r,t)=>{var i;r._zod??(r._zod={}),r._zod.def=t,(i=r._zod).onattach??(i.onattach=[])}),sw={number:"number",bigint:"bigint",object:"date"},Uo=$("$ZodCheckLessThan",(r,t)=>{s.init(r,t);let i=sw[typeof t.value];r._zod.onattach.push((o)=>{let n=o._zod.bag,e=(t.inclusive?n.maximum:n.exclusiveMaximum)??Number.POSITIVE_INFINITY;if(t.value<e)if(t.inclusive)n.maximum=t.value;else n.exclusiveMaximum=t.value}),r._zod.check=(o)=>{if(t.inclusive?o.value<=t.value:o.value<t.value)return;o.issues.push({origin:i,code:"too_big",maximum:t.value,input:o.value,inclusive:t.inclusive,inst:r,continue:!t.abort})}}),ko=$("$ZodCheckGreaterThan",(r,t)=>{s.init(r,t);let i=sw[typeof t.value];r._zod.onattach.push((o)=>{let n=o._zod.bag,e=(t.inclusive?n.minimum:n.exclusiveMinimum)??Number.NEGATIVE_INFINITY;if(t.value>e)if(t.inclusive)n.minimum=t.value;else n.exclusiveMinimum=t.value}),r._zod.check=(o)=>{if(t.inclusive?o.value>=t.value:o.value>t.value)return;o.issues.push({origin:i,code:"too_small",minimum:t.value,input:o.value,inclusive:t.inclusive,inst:r,continue:!t.abort})}}),kg=$("$ZodCheckMultipleOf",(r,t)=>{s.init(r,t),r._zod.onattach.push((i)=>{var o;(o=i._zod.bag).multipleOf??(o.multipleOf=t.value)}),r._zod.check=(i)=>{if(typeof i.value!==typeof t.value)throw new Error("Cannot mix number and bigint in multiple_of check.");if(typeof i.value==="bigint"?i.value%t.value===BigInt(0):Su(i.value,t.value)===0)return;i.issues.push({origin:typeof i.value,code:"not_multiple_of",divisor:t.value,input:i.value,inst:r,continue:!t.abort})}}),Jg=$("$ZodCheckNumberFormat",(r,t)=>{s.init(r,t),t.format=t.format||"float64";let i=t.format?.includes("int"),o=i?"int":"number",[n,e]=Hu[t.format];r._zod.onattach.push((l)=>{let u=l._zod.bag;if(u.format=t.format,u.minimum=n,u.maximum=e,i)u.pattern=_g}),r._zod.check=(l)=>{let u=l.value;if(i){if(!Number.isInteger(u)){l.issues.push({expected:o,format:t.format,code:"invalid_type",continue:!1,input:u,inst:r});return}if(!Number.isSafeInteger(u)){if(u>0)l.issues.push({input:u,code:"too_big",maximum:Number.MAX_SAFE_INTEGER,note:"Integers must be within the safe integer range.",inst:r,origin:o,continue:!t.abort});else l.issues.push({input:u,code:"too_small",minimum:Number.MIN_SAFE_INTEGER,note:"Integers must be within the safe integer range.",inst:r,origin:o,continue:!t.abort});return}}if(u<n)l.issues.push({origin:"number",input:u,code:"too_small",minimum:n,inclusive:!0,inst:r,continue:!t.abort});if(u>e)l.issues.push({origin:"number",input:u,code:"too_big",maximum:e,inst:r})}}),Xg=$("$ZodCheckBigIntFormat",(r,t)=>{s.init(r,t);let[i,o]=Ru[t.format];r._zod.onattach.push((n)=>{let e=n._zod.bag;e.format=t.format,e.minimum=i,e.maximum=o}),r._zod.check=(n)=>{let e=n.value;if(e<i)n.issues.push({origin:"bigint",input:e,code:"too_small",minimum:i,inclusive:!0,inst:r,continue:!t.abort});if(e>o)n.issues.push({origin:"bigint",input:e,code:"too_big",maximum:o,inst:r})}}),Pg=$("$ZodCheckMaxSize",(r,t)=>{var i;s.init(r,t),(i=r._zod.def).when??(i.when=(o)=>{let n=o.value;return!wt(n)&&n.size!==void 0}),r._zod.onattach.push((o)=>{let n=o._zod.bag.maximum??Number.POSITIVE_INFINITY;if(t.maximum<n)o._zod.bag.maximum=t.maximum}),r._zod.check=(o)=>{let n=o.value;if(n.size<=t.maximum)return;o.issues.push({origin:Zn(n),code:"too_big",maximum:t.maximum,inclusive:!0,input:n,inst:r,continue:!t.abort})}}),qg=$("$ZodCheckMinSize",(r,t)=>{var i;s.init(r,t),(i=r._zod.def).when??(i.when=(o)=>{let n=o.value;return!wt(n)&&n.size!==void 0}),r._zod.onattach.push((o)=>{let n=o._zod.bag.minimum??Number.NEGATIVE_INFINITY;if(t.minimum>n)o._zod.bag.minimum=t.minimum}),r._zod.check=(o)=>{let n=o.value;if(n.size>=t.minimum)return;o.issues.push({origin:Zn(n),code:"too_small",minimum:t.minimum,inclusive:!0,input:n,inst:r,continue:!t.abort})}}),Wg=$("$ZodCheckSizeEquals",(r,t)=>{var i;s.init(r,t),(i=r._zod.def).when??(i.when=(o)=>{let n=o.value;return!wt(n)&&n.size!==void 0}),r._zod.onattach.push((o)=>{let n=o._zod.bag;n.minimum=t.size,n.maximum=t.size,n.size=t.size}),r._zod.check=(o)=>{let n=o.value,e=n.size;if(e===t.size)return;let l=e>t.size;o.issues.push({origin:Zn(n),...l?{code:"too_big",maximum:t.size}:{code:"too_small",minimum:t.size},inclusive:!0,exact:!0,input:o.value,inst:r,continue:!t.abort})}}),Kg=$("$ZodCheckMaxLength",(r,t)=>{var i;s.init(r,t),(i=r._zod.def).when??(i.when=(o)=>{let n=o.value;return!wt(n)&&n.length!==void 0}),r._zod.onattach.push((o)=>{let n=o._zod.bag.maximum??Number.POSITIVE_INFINITY;if(t.maximum<n)o._zod.bag.maximum=t.maximum}),r._zod.check=(o)=>{let n=o.value;if(n.length<=t.maximum)return;let l=Cn(n);o.issues.push({origin:l,code:"too_big",maximum:t.maximum,inclusive:!0,input:n,inst:r,continue:!t.abort})}}),Vg=$("$ZodCheckMinLength",(r,t)=>{var i;s.init(r,t),(i=r._zod.def).when??(i.when=(o)=>{let n=o.value;return!wt(n)&&n.length!==void 0}),r._zod.onattach.push((o)=>{let n=o._zod.bag.minimum??Number.NEGATIVE_INFINITY;if(t.minimum>n)o._zod.bag.minimum=t.minimum}),r._zod.check=(o)=>{let n=o.value;if(n.length>=t.minimum)return;let l=Cn(n);o.issues.push({origin:l,code:"too_small",minimum:t.minimum,inclusive:!0,input:n,inst:r,continue:!t.abort})}}),Lg=$("$ZodCheckLengthEquals",(r,t)=>{var i;s.init(r,t),(i=r._zod.def).when??(i.when=(o)=>{let n=o.value;return!wt(n)&&n.length!==void 0}),r._zod.onattach.push((o)=>{let n=o._zod.bag;n.minimum=t.length,n.maximum=t.length,n.length=t.length}),r._zod.check=(o)=>{let n=o.value,e=n.length;if(e===t.length)return;let l=Cn(n),u=e>t.length;o.issues.push({origin:l,...u?{code:"too_big",maximum:t.length}:{code:"too_small",minimum:t.length},inclusive:!0,exact:!0,input:o.value,inst:r,continue:!t.abort})}}),bn=$("$ZodCheckStringFormat",(r,t)=>{var i,o;if(s.init(r,t),r._zod.onattach.push((n)=>{let e=n._zod.bag;if(e.format=t.format,t.pattern)e.patterns??(e.patterns=new Set),e.patterns.add(t.pattern)}),t.pattern)(i=r._zod).check??(i.check=(n)=>{if(t.pattern.lastIndex=0,t.pattern.test(n.value))return;n.issues.push({origin:"string",code:"invalid_format",format:t.format,input:n.value,...t.pattern?{pattern:t.pattern.toString()}:{},inst:r,continue:!t.abort})});else(o=r._zod).check??(o.check=()=>{})}),Yg=$("$ZodCheckRegex",(r,t)=>{bn.init(r,t),r._zod.check=(i)=>{if(t.pattern.lastIndex=0,t.pattern.test(i.value))return;i.issues.push({origin:"string",code:"invalid_format",format:"regex",input:i.value,pattern:t.pattern.toString(),inst:r,continue:!t.abort})}}),Eg=$("$ZodCheckLowerCase",(r,t)=>{t.pattern??(t.pattern=ag),bn.init(r,t)}),Qg=$("$ZodCheckUpperCase",(r,t)=>{t.pattern??(t.pattern=Ug),bn.init(r,t)}),Fg=$("$ZodCheckIncludes",(r,t)=>{s.init(r,t);let i=Cr(t.includes),o=new RegExp(typeof t.position==="number"?`^.{${t.position}}${i}`:i);t.pattern=o,r._zod.onattach.push((n)=>{let e=n._zod.bag;e.patterns??(e.patterns=new Set),e.patterns.add(o)}),r._zod.check=(n)=>{if(n.value.includes(t.includes,t.position))return;n.issues.push({origin:"string",code:"invalid_format",format:"includes",includes:t.includes,input:n.value,inst:r,continue:!t.abort})}}),Gg=$("$ZodCheckStartsWith",(r,t)=>{s.init(r,t);let i=new RegExp(`^${Cr(t.prefix)}.*`);t.pattern??(t.pattern=i),r._zod.onattach.push((o)=>{let n=o._zod.bag;n.patterns??(n.patterns=new Set),n.patterns.add(i)}),r._zod.check=(o)=>{if(o.value.startsWith(t.prefix))return;o.issues.push({origin:"string",code:"invalid_format",format:"starts_with",prefix:t.prefix,input:o.value,inst:r,continue:!t.abort})}}),Sg=$("$ZodCheckEndsWith",(r,t)=>{s.init(r,t);let i=new RegExp(`.*${Cr(t.suffix)}$`);t.pattern??(t.pattern=i),r._zod.onattach.push((o)=>{let n=o._zod.bag;n.patterns??(n.patterns=new Set),n.patterns.add(i)}),r._zod.check=(o)=>{if(o.value.endsWith(t.suffix))return;o.issues.push({origin:"string",code:"invalid_format",format:"ends_with",suffix:t.suffix,input:o.value,inst:r,continue:!t.abort})}});function dw(r,t,i){if(r.issues.length)t.issues.push(...qr(i,r.issues))}var Ng=$("$ZodCheckProperty",(r,t)=>{s.init(r,t),r._zod.check=(i)=>{let o=t.schema._zod.run({value:i.value[t.property],issues:[]},{});if(o instanceof Promise)return o.then((n)=>dw(n,i,t.property));dw(o,i,t.property);return}}),Bg=$("$ZodCheckMimeType",(r,t)=>{s.init(r,t);let i=new Set(t.mime);r._zod.onattach.push((o)=>{o._zod.bag.mime=t.mime}),r._zod.check=(o)=>{if(i.has(o.value.type))return;o.issues.push({code:"invalid_value",values:t.mime,input:o.value.type,inst:r,continue:!t.abort})}}),yg=$("$ZodCheckOverwrite",(r,t)=>{s.init(r,t),r._zod.check=(i)=>{i.value=t.tx(i.value)}});class Jo{constructor(r=[]){if(this.content=[],this.indent=0,this)this.args=r}indented(r){this.indent+=1,r(this),this.indent-=1}write(r){if(typeof r==="function"){r(this,{execution:"sync"}),r(this,{execution:"async"});return}let i=r.split(`
`).filter((e)=>e),o=Math.min(...i.map((e)=>e.length-e.trimStart().length)),n=i.map((e)=>e.slice(o)).map((e)=>" ".repeat(this.indent*2)+e);for(let e of n)this.content.push(e)}compile(){let r=Function,t=this?.args,o=[...(this?.content??[""]).map((n)=>`  ${n}`)];return new r(...t,o.join(`
`))}}var Ag={major:4,minor:1,patch:5};var V=$("$ZodType",(r,t)=>{var i;r??(r={}),r._zod.def=t,r._zod.bag=r._zod.bag||{},r._zod.version=Ag;let o=[...r._zod.def.checks??[]];if(r._zod.traits.has("$ZodCheck"))o.unshift(r);for(let n of o)for(let e of n._zod.onattach)e(r);if(o.length===0)(i=r._zod).deferred??(i.deferred=[]),r._zod.deferred?.push(()=>{r._zod.run=r._zod.parse});else{let n=(l,u,g)=>{let c=_t(l),b;for(let v of u){if(v._zod.def.when){if(!v._zod.def.when(l))continue}else if(c)continue;let h=l.issues.length,m=v._zod.check(l);if(m instanceof Promise&&g?.async===!1)throw new Zr;if(b||m instanceof Promise)b=(b??Promise.resolve()).then(async()=>{if(await m,l.issues.length===h)return;if(!c)c=_t(l,h)});else{if(l.issues.length===h)continue;if(!c)c=_t(l,h)}}if(b)return b.then(()=>{return l});return l},e=(l,u,g)=>{if(_t(l))return l.aborted=!0,l;let c=n(u,o,g);if(c instanceof Promise){if(g.async===!1)throw new Zr;return c.then((b)=>r._zod.parse(b,g))}return r._zod.parse(c,g)};r._zod.run=(l,u)=>{if(u.skipChecks)return r._zod.parse(l,u);if(u.direction==="backward"){let c=r._zod.parse({value:l.value,issues:[]},{...u,skipChecks:!0});if(c instanceof Promise)return c.then((b)=>{return e(b,l,u)});return e(c,l,u)}let g=r._zod.parse(l,u);if(g instanceof Promise){if(u.async===!1)throw new Zr;return g.then((c)=>n(c,o,u))}return n(g,o,u)}}r["~standard"]={validate:(n)=>{try{let e=Cu(r,n);return e.success?{value:e.data}:{issues:e.error?.issues}}catch(e){return Tu(r,n).then((l)=>l.success?{value:l.data}:{issues:l.error?.issues})}},vendor:"zod",version:1}}),Wt=$("$ZodString",(r,t)=>{V.init(r,t),r._zod.pattern=[...r?._zod.bag?.patterns??[]].pop()??zg(r._zod.bag),r._zod.parse=(i,o)=>{if(t.coerce)try{i.value=String(i.value)}catch(n){}if(typeof i.value==="string")return i;return i.issues.push({expected:"string",code:"invalid_type",input:i.value,inst:r}),i}}),M=$("$ZodStringFormat",(r,t)=>{bn.init(r,t),Wt.init(r,t)}),Rg=$("$ZodGUID",(r,t)=>{t.pattern??(t.pattern=eg),M.init(r,t)}),Mg=$("$ZodUUID",(r,t)=>{if(t.version){let o={v1:1,v2:2,v3:3,v4:4,v5:5,v6:6,v7:7,v8:8}[t.version];if(o===void 0)throw new Error(`Invalid UUID version: "${t.version}"`);t.pattern??(t.pattern=qt(o))}else t.pattern??(t.pattern=qt());M.init(r,t)}),Zg=$("$ZodEmail",(r,t)=>{t.pattern??(t.pattern=lg),M.init(r,t)}),Cg=$("$ZodURL",(r,t)=>{M.init(r,t),r._zod.check=(i)=>{try{let o=i.value.trim(),n=new URL(o);if(t.hostname){if(t.hostname.lastIndex=0,!t.hostname.test(n.hostname))i.issues.push({code:"invalid_format",format:"url",note:"Invalid hostname",pattern:hg.source,input:i.value,inst:r,continue:!t.abort})}if(t.protocol){if(t.protocol.lastIndex=0,!t.protocol.test(n.protocol.endsWith(":")?n.protocol.slice(0,-1):n.protocol))i.issues.push({code:"invalid_format",format:"url",note:"Invalid protocol",pattern:t.protocol.source,input:i.value,inst:r,continue:!t.abort})}if(t.normalize)i.value=n.href;else i.value=o;return}catch(o){i.issues.push({code:"invalid_format",format:"url",input:i.value,inst:r,continue:!t.abort})}}}),Tg=$("$ZodEmoji",(r,t)=>{t.pattern??(t.pattern=cg()),M.init(r,t)}),dg=$("$ZodNanoID",(r,t)=>{t.pattern??(t.pattern=ig),M.init(r,t)}),sg=$("$ZodCUID",(r,t)=>{t.pattern??(t.pattern=du),M.init(r,t)}),rb=$("$ZodCUID2",(r,t)=>{t.pattern??(t.pattern=su),M.init(r,t)}),tb=$("$ZodULID",(r,t)=>{t.pattern??(t.pattern=rg),M.init(r,t)}),nb=$("$ZodXID",(r,t)=>{t.pattern??(t.pattern=tg),M.init(r,t)}),ib=$("$ZodKSUID",(r,t)=>{t.pattern??(t.pattern=ng),M.init(r,t)}),ob=$("$ZodISODateTime",(r,t)=>{t.pattern??(t.pattern=fg(t)),M.init(r,t)}),eb=$("$ZodISODate",(r,t)=>{t.pattern??(t.pattern=xg),M.init(r,t)}),lb=$("$ZodISOTime",(r,t)=>{t.pattern??(t.pattern=wg(t)),M.init(r,t)}),cb=$("$ZodISODuration",(r,t)=>{t.pattern??(t.pattern=og),M.init(r,t)}),ub=$("$ZodIPv4",(r,t)=>{t.pattern??(t.pattern=ug),M.init(r,t),r._zod.onattach.push((i)=>{let o=i._zod.bag;o.format="ipv4"})}),gb=$("$ZodIPv6",(r,t)=>{t.pattern??(t.pattern=gg),M.init(r,t),r._zod.onattach.push((i)=>{let o=i._zod.bag;o.format="ipv6"}),r._zod.check=(i)=>{try{new URL(`http://[${i.value}]`)}catch{i.issues.push({code:"invalid_format",format:"ipv6",input:i.value,inst:r,continue:!t.abort})}}}),bb=$("$ZodCIDRv4",(r,t)=>{t.pattern??(t.pattern=bg),M.init(r,t)}),mb=$("$ZodCIDRv6",(r,t)=>{t.pattern??(t.pattern=mg),M.init(r,t),r._zod.check=(i)=>{let[o,n]=i.value.split("/");try{if(!n)throw new Error;let e=Number(n);if(`${e}`!==n)throw new Error;if(e<0||e>128)throw new Error;new URL(`http://[${o}]`)}catch{i.issues.push({code:"invalid_format",format:"cidrv6",input:i.value,inst:r,continue:!t.abort})}}});function vb(r){if(r==="")return!0;if(r.length%4!==0)return!1;try{return atob(r),!0}catch{return!1}}var hb=$("$ZodBase64",(r,t)=>{t.pattern??(t.pattern=vg),M.init(r,t),r._zod.onattach.push((i)=>{i._zod.bag.contentEncoding="base64"}),r._zod.check=(i)=>{if(vb(i.value))return;i.issues.push({code:"invalid_format",format:"base64",input:i.value,inst:r,continue:!t.abort})}});function vf(r){if(!ao.test(r))return!1;let t=r.replace(/[-_]/g,(o)=>o==="-"?"+":"/"),i=t.padEnd(Math.ceil(t.length/4)*4,"=");return vb(i)}var $b=$("$ZodBase64URL",(r,t)=>{t.pattern??(t.pattern=ao),M.init(r,t),r._zod.onattach.push((i)=>{i._zod.bag.contentEncoding="base64url"}),r._zod.check=(i)=>{if(vf(i.value))return;i.issues.push({code:"invalid_format",format:"base64url",input:i.value,inst:r,continue:!t.abort})}}),xb=$("$ZodE164",(r,t)=>{t.pattern??(t.pattern=$g),M.init(r,t)});function hf(r,t=null){try{let i=r.split(".");if(i.length!==3)return!1;let[o]=i;if(!o)return!1;let n=JSON.parse(atob(o));if("typ"in n&&n?.typ!=="JWT")return!1;if(!n.alg)return!1;if(t&&(!("alg"in n)||n.alg!==t))return!1;return!0}catch{return!1}}var wb=$("$ZodJWT",(r,t)=>{M.init(r,t),r._zod.check=(i)=>{if(hf(i.value,t.alg))return;i.issues.push({code:"invalid_format",format:"jwt",input:i.value,inst:r,continue:!t.abort})}}),fb=$("$ZodCustomStringFormat",(r,t)=>{M.init(r,t),r._zod.check=(i)=>{if(t.fn(i.value))return;i.issues.push({code:"invalid_format",format:t.format,input:i.value,inst:r,continue:!t.abort})}}),Vo=$("$ZodNumber",(r,t)=>{V.init(r,t),r._zod.pattern=r._zod.bag.pattern??jg,r._zod.parse=(i,o)=>{if(t.coerce)try{i.value=Number(i.value)}catch(l){}let n=i.value;if(typeof n==="number"&&!Number.isNaN(n)&&Number.isFinite(n))return i;let e=typeof n==="number"?Number.isNaN(n)?"NaN":!Number.isFinite(n)?"Infinity":void 0:void 0;return i.issues.push({expected:"number",code:"invalid_type",input:n,inst:r,...e?{received:e}:{}}),i}}),zb=$("$ZodNumber",(r,t)=>{Jg.init(r,t),Vo.init(r,t)}),ni=$("$ZodBoolean",(r,t)=>{V.init(r,t),r._zod.pattern=Og,r._zod.parse=(i,o)=>{if(t.coerce)try{i.value=Boolean(i.value)}catch(e){}let n=i.value;if(typeof n==="boolean")return i;return i.issues.push({expected:"boolean",code:"invalid_type",input:n,inst:r}),i}}),Lo=$("$ZodBigInt",(r,t)=>{V.init(r,t),r._zod.pattern=Dg,r._zod.parse=(i,o)=>{if(t.coerce)try{i.value=BigInt(i.value)}catch(n){}if(typeof i.value==="bigint")return i;return i.issues.push({expected:"bigint",code:"invalid_type",input:i.value,inst:r}),i}}),Db=$("$ZodBigInt",(r,t)=>{Xg.init(r,t),Lo.init(r,t)}),_b=$("$ZodSymbol",(r,t)=>{V.init(r,t),r._zod.parse=(i,o)=>{let n=i.value;if(typeof n==="symbol")return i;return i.issues.push({expected:"symbol",code:"invalid_type",input:n,inst:r}),i}}),jb=$("$ZodUndefined",(r,t)=>{V.init(r,t),r._zod.pattern=Ig,r._zod.values=new Set([void 0]),r._zod.optin="optional",r._zod.optout="optional",r._zod.parse=(i,o)=>{let n=i.value;if(typeof n==="undefined")return i;return i.issues.push({expected:"undefined",code:"invalid_type",input:n,inst:r}),i}}),Ob=$("$ZodNull",(r,t)=>{V.init(r,t),r._zod.pattern=pg,r._zod.values=new Set([null]),r._zod.parse=(i,o)=>{let n=i.value;if(n===null)return i;return i.issues.push({expected:"null",code:"invalid_type",input:n,inst:r}),i}}),pb=$("$ZodAny",(r,t)=>{V.init(r,t),r._zod.parse=(i)=>i}),Ib=$("$ZodUnknown",(r,t)=>{V.init(r,t),r._zod.parse=(i)=>i}),ab=$("$ZodNever",(r,t)=>{V.init(r,t),r._zod.parse=(i,o)=>{return i.issues.push({expected:"never",code:"invalid_type",input:i.value,inst:r}),i}}),Ub=$("$ZodVoid",(r,t)=>{V.init(r,t),r._zod.parse=(i,o)=>{let n=i.value;if(typeof n==="undefined")return i;return i.issues.push({expected:"void",code:"invalid_type",input:n,inst:r}),i}}),kb=$("$ZodDate",(r,t)=>{V.init(r,t),r._zod.parse=(i,o)=>{if(t.coerce)try{i.value=new Date(i.value)}catch(u){}let n=i.value,e=n instanceof Date;if(e&&!Number.isNaN(n.getTime()))return i;return i.issues.push({expected:"date",code:"invalid_type",input:n,...e?{received:"Invalid Date"}:{},inst:r}),i}});function tf(r,t,i){if(r.issues.length)t.issues.push(...qr(i,r.issues));t.value[i]=r.value}var Jb=$("$ZodArray",(r,t)=>{V.init(r,t),r._zod.parse=(i,o)=>{let n=i.value;if(!Array.isArray(n))return i.issues.push({expected:"array",code:"invalid_type",input:n,inst:r}),i;i.value=Array(n.length);let e=[];for(let l=0;l<n.length;l++){let u=n[l],g=t.element._zod.run({value:u,issues:[]},o);if(g instanceof Promise)e.push(g.then((c)=>tf(c,i,l)));else tf(g,i,l)}if(e.length)return Promise.all(e).then(()=>i);return i}});function Ko(r,t,i,o){if(r.issues.length)t.issues.push(...qr(i,r.issues));if(r.value===void 0){if(i in o)t.value[i]=void 0}else t.value[i]=r.value}function $f(r){let t=Object.keys(r.shape);for(let o of t)if(!r.shape[o]._zod.traits.has("$ZodType"))throw new Error(`Invalid element at key "${o}": expected a Zod schema`);let i=Au(r.shape);return{...r,keys:t,keySet:new Set(t),numKeys:t.length,optionalKeys:new Set(i)}}function xf(r,t,i,o,n,e){let l=[],u=n.keySet,g=n.catchall._zod,c=g.def.type;for(let b of Object.keys(t)){if(u.has(b))continue;if(c==="never"){l.push(b);continue}let v=g.run({value:t[b],issues:[]},o);if(v instanceof Promise)r.push(v.then((h)=>Ko(h,i,b,t)));else Ko(v,i,b,t)}if(l.length)i.issues.push({code:"unrecognized_keys",keys:l,input:t,inst:e});if(!r.length)return i;return Promise.all(r).then(()=>{return i})}var wf=$("$ZodObject",(r,t)=>{V.init(r,t);let i=on(()=>$f(t));F(r._zod,"propValues",()=>{let l=t.shape,u={};for(let g in l){let c=l[g]._zod;if(c.values){u[g]??(u[g]=new Set);for(let b of c.values)u[g].add(b)}}return u});let o=Pt,n=t.catchall,e;r._zod.parse=(l,u)=>{e??(e=i.value);let g=l.value;if(!o(g))return l.issues.push({expected:"object",code:"invalid_type",input:g,inst:r}),l;l.value={};let c=[],b=e.shape;for(let v of e.keys){let m=b[v]._zod.run({value:g[v],issues:[]},u);if(m instanceof Promise)c.push(m.then((w)=>Ko(w,l,v,g)));else Ko(m,l,v,g)}if(!n)return c.length?Promise.all(c).then(()=>l):l;return xf(c,g,l,u,i.value,r)}}),Xb=$("$ZodObjectJIT",(r,t)=>{wf.init(r,t);let i=r._zod.parse,o=on(()=>$f(t)),n=(h)=>{let m=new Jo(["shape","payload","ctx"]),w=o.value,O=(L)=>{let P=ho(L);return`shape[${P}]._zod.run({ value: input[${P}], issues: [] }, ctx)`};m.write("const input = payload.value;");let I=Object.create(null),_=0;for(let L of w.keys)I[L]=`key_${_++}`;m.write("const newResult = {}");for(let L of w.keys){let P=I[L],W=ho(L);m.write(`const ${P} = ${O(L)};`),m.write(`
        if (${P}.issues.length) {
          payload.issues = payload.issues.concat(${P}.issues.map(iss => ({
            ...iss,
            path: iss.path ? [${W}, ...iss.path] : [${W}]
          })));
        }
        
        if (${P}.value === undefined) {
          if (${W} in input) {
            newResult[${W}] = undefined;
          }
        } else {
          newResult[${W}] = ${P}.value;
        }
      `)}m.write("payload.value = newResult;"),m.write("return payload;");let J=m.compile();return(L,P)=>J(h,L,P)},e,l=Pt,u=!yn.jitless,c=u&&Nu.value,b=t.catchall,v;r._zod.parse=(h,m)=>{v??(v=o.value);let w=h.value;if(!l(w))return h.issues.push({expected:"object",code:"invalid_type",input:w,inst:r}),h;if(u&&c&&m?.async===!1&&m.jitless!==!0){if(!e)e=n(t.shape);if(h=e(h,m),!b)return h;return xf([],w,h,m,v,r)}return i(h,m)}});function nf(r,t,i,o){for(let e of r)if(e.issues.length===0)return t.value=e.value,t;let n=r.filter((e)=>!_t(e));if(n.length===1)return t.value=n[0].value,n[0];return t.issues.push({code:"invalid_union",input:t.value,inst:i,errors:r.map((e)=>e.issues.map((l)=>Wr(l,o,mr())))}),t}var Yo=$("$ZodUnion",(r,t)=>{V.init(r,t),F(r._zod,"optin",()=>t.options.some((n)=>n._zod.optin==="optional")?"optional":void 0),F(r._zod,"optout",()=>t.options.some((n)=>n._zod.optout==="optional")?"optional":void 0),F(r._zod,"values",()=>{if(t.options.every((n)=>n._zod.values))return new Set(t.options.flatMap((n)=>Array.from(n._zod.values)));return}),F(r._zod,"pattern",()=>{if(t.options.every((n)=>n._zod.pattern)){let n=t.options.map((e)=>e._zod.pattern);return new RegExp(`^(${n.map((e)=>Rn(e.source)).join("|")})$`)}return});let i=t.options.length===1,o=t.options[0]._zod.run;r._zod.parse=(n,e)=>{if(i)return o(n,e);let l=!1,u=[];for(let g of t.options){let c=g._zod.run({value:n.value,issues:[]},e);if(c instanceof Promise)u.push(c),l=!0;else{if(c.issues.length===0)return c;u.push(c)}}if(!l)return nf(u,n,r,e);return Promise.all(u).then((g)=>{return nf(g,n,r,e)})}}),Pb=$("$ZodDiscriminatedUnion",(r,t)=>{Yo.init(r,t);let i=r._zod.parse;F(r._zod,"propValues",()=>{let n={};for(let e of t.options){let l=e._zod.propValues;if(!l||Object.keys(l).length===0)throw new Error(`Invalid discriminated union option at index "${t.options.indexOf(e)}"`);for(let[u,g]of Object.entries(l)){if(!n[u])n[u]=new Set;for(let c of g)n[u].add(c)}}return n});let o=on(()=>{let n=t.options,e=new Map;for(let l of n){let u=l._zod.propValues?.[t.discriminator];if(!u||u.size===0)throw new Error(`Invalid discriminated union option at index "${t.options.indexOf(l)}"`);for(let g of u){if(e.has(g))throw new Error(`Duplicate discriminator value "${String(g)}"`);e.set(g,l)}}return e});r._zod.parse=(n,e)=>{let l=n.value;if(!Pt(l))return n.issues.push({code:"invalid_type",expected:"object",input:l,inst:r}),n;let u=o.value.get(l?.[t.discriminator]);if(u)return u._zod.run(n,e);if(t.unionFallback)return i(n,e);return n.issues.push({code:"invalid_union",errors:[],note:"No matching discriminator",discriminator:t.discriminator,input:l,path:[t.discriminator],inst:r}),n}}),qb=$("$ZodIntersection",(r,t)=>{V.init(r,t),r._zod.parse=(i,o)=>{let n=i.value,e=t.left._zod.run({value:n,issues:[]},o),l=t.right._zod.run({value:n,issues:[]},o);if(e instanceof Promise||l instanceof Promise)return Promise.all([e,l]).then(([g,c])=>{return of(i,g,c)});return of(i,e,l)}});function Hg(r,t){if(r===t)return{valid:!0,data:r};if(r instanceof Date&&t instanceof Date&&+r===+t)return{valid:!0,data:r};if(Dt(r)&&Dt(t)){let i=Object.keys(t),o=Object.keys(r).filter((e)=>i.indexOf(e)!==-1),n={...r,...t};for(let e of o){let l=Hg(r[e],t[e]);if(!l.valid)return{valid:!1,mergeErrorPath:[e,...l.mergeErrorPath]};n[e]=l.data}return{valid:!0,data:n}}if(Array.isArray(r)&&Array.isArray(t)){if(r.length!==t.length)return{valid:!1,mergeErrorPath:[]};let i=[];for(let o=0;o<r.length;o++){let n=r[o],e=t[o],l=Hg(n,e);if(!l.valid)return{valid:!1,mergeErrorPath:[o,...l.mergeErrorPath]};i.push(l.data)}return{valid:!0,data:i}}return{valid:!1,mergeErrorPath:[]}}function of(r,t,i){if(t.issues.length)r.issues.push(...t.issues);if(i.issues.length)r.issues.push(...i.issues);if(_t(r))return r;let o=Hg(t.value,i.value);if(!o.valid)throw new Error(`Unmergable intersection. Error path: ${JSON.stringify(o.mergeErrorPath)}`);return r.value=o.data,r}var Eo=$("$ZodTuple",(r,t)=>{V.init(r,t);let i=t.items,o=i.length-[...i].reverse().findIndex((n)=>n._zod.optin!=="optional");r._zod.parse=(n,e)=>{let l=n.value;if(!Array.isArray(l))return n.issues.push({input:l,inst:r,expected:"tuple",code:"invalid_type"}),n;n.value=[];let u=[];if(!t.rest){let c=l.length>i.length,b=l.length<o-1;if(c||b)return n.issues.push({...c?{code:"too_big",maximum:i.length}:{code:"too_small",minimum:i.length},input:l,inst:r,origin:"array"}),n}let g=-1;for(let c of i){if(g++,g>=l.length){if(g>=o)continue}let b=c._zod.run({value:l[g],issues:[]},e);if(b instanceof Promise)u.push(b.then((v)=>Xo(v,n,g)));else Xo(b,n,g)}if(t.rest){let c=l.slice(i.length);for(let b of c){g++;let v=t.rest._zod.run({value:b,issues:[]},e);if(v instanceof Promise)u.push(v.then((h)=>Xo(h,n,g)));else Xo(v,n,g)}}if(u.length)return Promise.all(u).then(()=>n);return n}});function Xo(r,t,i){if(r.issues.length)t.issues.push(...qr(i,r.issues));t.value[i]=r.value}var Wb=$("$ZodRecord",(r,t)=>{V.init(r,t),r._zod.parse=(i,o)=>{let n=i.value;if(!Dt(n))return i.issues.push({expected:"record",code:"invalid_type",input:n,inst:r}),i;let e=[];if(t.keyType._zod.values){let l=t.keyType._zod.values;i.value={};for(let g of l)if(typeof g==="string"||typeof g==="number"||typeof g==="symbol"){let c=t.valueType._zod.run({value:n[g],issues:[]},o);if(c instanceof Promise)e.push(c.then((b)=>{if(b.issues.length)i.issues.push(...qr(g,b.issues));i.value[g]=b.value}));else{if(c.issues.length)i.issues.push(...qr(g,c.issues));i.value[g]=c.value}}let u;for(let g in n)if(!l.has(g))u=u??[],u.push(g);if(u&&u.length>0)i.issues.push({code:"unrecognized_keys",input:n,inst:r,keys:u})}else{i.value={};for(let l of Reflect.ownKeys(n)){if(l==="__proto__")continue;let u=t.keyType._zod.run({value:l,issues:[]},o);if(u instanceof Promise)throw new Error("Async schemas not supported in object keys currently");if(u.issues.length){i.issues.push({code:"invalid_key",origin:"record",issues:u.issues.map((c)=>Wr(c,o,mr())),input:l,path:[l],inst:r}),i.value[u.value]=u.value;continue}let g=t.valueType._zod.run({value:n[l],issues:[]},o);if(g instanceof Promise)e.push(g.then((c)=>{if(c.issues.length)i.issues.push(...qr(l,c.issues));i.value[u.value]=c.value}));else{if(g.issues.length)i.issues.push(...qr(l,g.issues));i.value[u.value]=g.value}}}if(e.length)return Promise.all(e).then(()=>i);return i}}),Kb=$("$ZodMap",(r,t)=>{V.init(r,t),r._zod.parse=(i,o)=>{let n=i.value;if(!(n instanceof Map))return i.issues.push({expected:"map",code:"invalid_type",input:n,inst:r}),i;let e=[];i.value=new Map;for(let[l,u]of n){let g=t.keyType._zod.run({value:l,issues:[]},o),c=t.valueType._zod.run({value:u,issues:[]},o);if(g instanceof Promise||c instanceof Promise)e.push(Promise.all([g,c]).then(([b,v])=>{ef(b,v,i,l,n,r,o)}));else ef(g,c,i,l,n,r,o)}if(e.length)return Promise.all(e).then(()=>i);return i}});function ef(r,t,i,o,n,e,l){if(r.issues.length)if(Mn.has(typeof o))i.issues.push(...qr(o,r.issues));else i.issues.push({code:"invalid_key",origin:"map",input:n,inst:e,issues:r.issues.map((u)=>Wr(u,l,mr()))});if(t.issues.length)if(Mn.has(typeof o))i.issues.push(...qr(o,t.issues));else i.issues.push({origin:"map",code:"invalid_element",input:n,inst:e,key:o,issues:t.issues.map((u)=>Wr(u,l,mr()))});i.value.set(r.value,t.value)}var Vb=$("$ZodSet",(r,t)=>{V.init(r,t),r._zod.parse=(i,o)=>{let n=i.value;if(!(n instanceof Set))return i.issues.push({input:n,inst:r,expected:"set",code:"invalid_type"}),i;let e=[];i.value=new Set;for(let l of n){let u=t.valueType._zod.run({value:l,issues:[]},o);if(u instanceof Promise)e.push(u.then((g)=>lf(g,i)));else lf(u,i)}if(e.length)return Promise.all(e).then(()=>i);return i}});function lf(r,t){if(r.issues.length)t.issues.push(...r.issues);t.value.add(r.value)}var Lb=$("$ZodEnum",(r,t)=>{V.init(r,t);let i=Hn(t.entries),o=new Set(i);r._zod.values=o,r._zod.pattern=new RegExp(`^(${i.filter((n)=>Mn.has(typeof n)).map((n)=>typeof n==="string"?Cr(n):n.toString()).join("|")})$`),r._zod.parse=(n,e)=>{let l=n.value;if(o.has(l))return n;return n.issues.push({code:"invalid_value",values:i,input:l,inst:r}),n}}),Yb=$("$ZodLiteral",(r,t)=>{if(V.init(r,t),t.values.length===0)throw new Error("Cannot create literal schema with no valid values");r._zod.values=new Set(t.values),r._zod.pattern=new RegExp(`^(${t.values.map((i)=>typeof i==="string"?Cr(i):i?Cr(i.toString()):String(i)).join("|")})$`),r._zod.parse=(i,o)=>{let n=i.value;if(r._zod.values.has(n))return i;return i.issues.push({code:"invalid_value",values:t.values,input:n,inst:r}),i}}),Eb=$("$ZodFile",(r,t)=>{V.init(r,t),r._zod.parse=(i,o)=>{let n=i.value;if(n instanceof File)return i;return i.issues.push({expected:"file",code:"invalid_type",input:n,inst:r}),i}}),Qb=$("$ZodTransform",(r,t)=>{V.init(r,t),r._zod.parse=(i,o)=>{if(o.direction==="backward")throw new Xt(r.constructor.name);let n=t.transform(i.value,i);if(o.async)return(n instanceof Promise?n:Promise.resolve(n)).then((l)=>{return i.value=l,i});if(n instanceof Promise)throw new Zr;return i.value=n,i}});function cf(r,t){if(r.issues.length&&t===void 0)return{issues:[],value:void 0};return r}var Fb=$("$ZodOptional",(r,t)=>{V.init(r,t),r._zod.optin="optional",r._zod.optout="optional",F(r._zod,"values",()=>{return t.innerType._zod.values?new Set([...t.innerType._zod.values,void 0]):void 0}),F(r._zod,"pattern",()=>{let i=t.innerType._zod.pattern;return i?new RegExp(`^(${Rn(i.source)})?$`):void 0}),r._zod.parse=(i,o)=>{if(t.innerType._zod.optin==="optional"){let n=t.innerType._zod.run(i,o);if(n instanceof Promise)return n.then((e)=>cf(e,i.value));return cf(n,i.value)}if(i.value===void 0)return i;return t.innerType._zod.run(i,o)}}),Gb=$("$ZodNullable",(r,t)=>{V.init(r,t),F(r._zod,"optin",()=>t.innerType._zod.optin),F(r._zod,"optout",()=>t.innerType._zod.optout),F(r._zod,"pattern",()=>{let i=t.innerType._zod.pattern;return i?new RegExp(`^(${Rn(i.source)}|null)$`):void 0}),F(r._zod,"values",()=>{return t.innerType._zod.values?new Set([...t.innerType._zod.values,null]):void 0}),r._zod.parse=(i,o)=>{if(i.value===null)return i;return t.innerType._zod.run(i,o)}}),Sb=$("$ZodDefault",(r,t)=>{V.init(r,t),r._zod.optin="optional",F(r._zod,"values",()=>t.innerType._zod.values),r._zod.parse=(i,o)=>{if(o.direction==="backward")return t.innerType._zod.run(i,o);if(i.value===void 0)return i.value=t.defaultValue,i;let n=t.innerType._zod.run(i,o);if(n instanceof Promise)return n.then((e)=>uf(e,t));return uf(n,t)}});function uf(r,t){if(r.value===void 0)r.value=t.defaultValue;return r}var Nb=$("$ZodPrefault",(r,t)=>{V.init(r,t),r._zod.optin="optional",F(r._zod,"values",()=>t.innerType._zod.values),r._zod.parse=(i,o)=>{if(o.direction==="backward")return t.innerType._zod.run(i,o);if(i.value===void 0)i.value=t.defaultValue;return t.innerType._zod.run(i,o)}}),Bb=$("$ZodNonOptional",(r,t)=>{V.init(r,t),F(r._zod,"values",()=>{let i=t.innerType._zod.values;return i?new Set([...i].filter((o)=>o!==void 0)):void 0}),r._zod.parse=(i,o)=>{let n=t.innerType._zod.run(i,o);if(n instanceof Promise)return n.then((e)=>gf(e,r));return gf(n,r)}});function gf(r,t){if(!r.issues.length&&r.value===void 0)r.issues.push({code:"invalid_type",expected:"nonoptional",input:r.value,inst:t});return r}var yb=$("$ZodSuccess",(r,t)=>{V.init(r,t),r._zod.parse=(i,o)=>{if(o.direction==="backward")throw new Xt("ZodSuccess");let n=t.innerType._zod.run(i,o);if(n instanceof Promise)return n.then((e)=>{return i.value=e.issues.length===0,i});return i.value=n.issues.length===0,i}}),Ab=$("$ZodCatch",(r,t)=>{V.init(r,t),F(r._zod,"optin",()=>t.innerType._zod.optin),F(r._zod,"optout",()=>t.innerType._zod.optout),F(r._zod,"values",()=>t.innerType._zod.values),r._zod.parse=(i,o)=>{if(o.direction==="backward")return t.innerType._zod.run(i,o);let n=t.innerType._zod.run(i,o);if(n instanceof Promise)return n.then((e)=>{if(i.value=e.value,e.issues.length)i.value=t.catchValue({...i,error:{issues:e.issues.map((l)=>Wr(l,o,mr()))},input:i.value}),i.issues=[];return i});if(i.value=n.value,n.issues.length)i.value=t.catchValue({...i,error:{issues:n.issues.map((e)=>Wr(e,o,mr()))},input:i.value}),i.issues=[];return i}}),Hb=$("$ZodNaN",(r,t)=>{V.init(r,t),r._zod.parse=(i,o)=>{if(typeof i.value!=="number"||!Number.isNaN(i.value))return i.issues.push({input:i.value,inst:r,expected:"nan",code:"invalid_type"}),i;return i}}),Rb=$("$ZodPipe",(r,t)=>{V.init(r,t),F(r._zod,"values",()=>t.in._zod.values),F(r._zod,"optin",()=>t.in._zod.optin),F(r._zod,"optout",()=>t.out._zod.optout),F(r._zod,"propValues",()=>t.in._zod.propValues),r._zod.parse=(i,o)=>{if(o.direction==="backward"){let e=t.out._zod.run(i,o);if(e instanceof Promise)return e.then((l)=>Po(l,t.in,o));return Po(e,t.in,o)}let n=t.in._zod.run(i,o);if(n instanceof Promise)return n.then((e)=>Po(e,t.out,o));return Po(n,t.out,o)}});function Po(r,t,i){if(r.issues.length)return r.aborted=!0,r;return t._zod.run({value:r.value,issues:r.issues},i)}var ii=$("$ZodCodec",(r,t)=>{V.init(r,t),F(r._zod,"values",()=>t.in._zod.values),F(r._zod,"optin",()=>t.in._zod.optin),F(r._zod,"optout",()=>t.out._zod.optout),F(r._zod,"propValues",()=>t.in._zod.propValues),r._zod.parse=(i,o)=>{if((o.direction||"forward")==="forward"){let e=t.in._zod.run(i,o);if(e instanceof Promise)return e.then((l)=>qo(l,t,o));return qo(e,t,o)}else{let e=t.out._zod.run(i,o);if(e instanceof Promise)return e.then((l)=>qo(l,t,o));return qo(e,t,o)}}});function qo(r,t,i){if(r.issues.length)return r.aborted=!0,r;if((i.direction||"forward")==="forward"){let n=t.transform(r.value,r);if(n instanceof Promise)return n.then((e)=>Wo(r,e,t.out,i));return Wo(r,n,t.out,i)}else{let n=t.reverseTransform(r.value,r);if(n instanceof Promise)return n.then((e)=>Wo(r,e,t.in,i));return Wo(r,n,t.in,i)}}function Wo(r,t,i,o){if(r.issues.length)return r.aborted=!0,r;return i._zod.run({value:t,issues:r.issues},o)}var Mb=$("$ZodReadonly",(r,t)=>{V.init(r,t),F(r._zod,"propValues",()=>t.innerType._zod.propValues),F(r._zod,"values",()=>t.innerType._zod.values),F(r._zod,"optin",()=>t.innerType._zod.optin),F(r._zod,"optout",()=>t.innerType._zod.optout),r._zod.parse=(i,o)=>{if(o.direction==="backward")return t.innerType._zod.run(i,o);let n=t.innerType._zod.run(i,o);if(n instanceof Promise)return n.then(bf);return bf(n)}});function bf(r){return r.value=Object.freeze(r.value),r}var Zb=$("$ZodTemplateLiteral",(r,t)=>{V.init(r,t);let i=[];for(let o of t.parts)if(typeof o==="object"&&o!==null){if(!o._zod.pattern)throw new Error(`Invalid template literal part, no pattern found: ${[...o._zod.traits].shift()}`);let n=o._zod.pattern instanceof RegExp?o._zod.pattern.source:o._zod.pattern;if(!n)throw new Error(`Invalid template literal part: ${o._zod.traits}`);let e=n.startsWith("^")?1:0,l=n.endsWith("$")?n.length-1:n.length;i.push(n.slice(e,l))}else if(o===null||yu.has(typeof o))i.push(Cr(`${o}`));else throw new Error(`Invalid template literal part: ${o}`);r._zod.pattern=new RegExp(`^${i.join("")}$`),r._zod.parse=(o,n)=>{if(typeof o.value!=="string")return o.issues.push({input:o.value,inst:r,expected:"template_literal",code:"invalid_type"}),o;if(r._zod.pattern.lastIndex=0,!r._zod.pattern.test(o.value))return o.issues.push({input:o.value,inst:r,code:"invalid_format",format:t.format??"template_literal",pattern:r._zod.pattern.source}),o;return o}}),Cb=$("$ZodFunction",(r,t)=>{return V.init(r,t),r._def=t,r._zod.def=t,r.implement=(i)=>{if(typeof i!=="function")throw new Error("implement() must be called with a function");return function(...o){let n=r._def.input?xo(r._def.input,o):o,e=Reflect.apply(i,this,n);if(r._def.output)return xo(r._def.output,e);return e}},r.implementAsync=(i)=>{if(typeof i!=="function")throw new Error("implementAsync() must be called with a function");return async function(...o){let n=r._def.input?await wo(r._def.input,o):o,e=await Reflect.apply(i,this,n);if(r._def.output)return await wo(r._def.output,e);return e}},r._zod.parse=(i,o)=>{if(typeof i.value!=="function")return i.issues.push({code:"invalid_type",expected:"function",input:i.value,inst:r}),i;if(r._def.output&&r._def.output._zod.def.type==="promise")i.value=r.implementAsync(i.value);else i.value=r.implement(i.value);return i},r.input=(...i)=>{let o=r.constructor;if(Array.isArray(i[0]))return new o({type:"function",input:new Eo({type:"tuple",items:i[0],rest:i[1]}),output:r._def.output});return new o({type:"function",input:i[0],output:r._def.output})},r.output=(i)=>{return new r.constructor({type:"function",input:r._def.input,output:i})},r}),Tb=$("$ZodPromise",(r,t)=>{V.init(r,t),r._zod.parse=(i,o)=>{return Promise.resolve(i.value).then((n)=>t.innerType._zod.run({value:n,issues:[]},o))}}),db=$("$ZodLazy",(r,t)=>{V.init(r,t),F(r._zod,"innerType",()=>t.getter()),F(r._zod,"pattern",()=>r._zod.innerType._zod.pattern),F(r._zod,"propValues",()=>r._zod.innerType._zod.propValues),F(r._zod,"optin",()=>r._zod.innerType._zod.optin??void 0),F(r._zod,"optout",()=>r._zod.innerType._zod.optout??void 0),r._zod.parse=(i,o)=>{return r._zod.innerType._zod.run(i,o)}}),sb=$("$ZodCustom",(r,t)=>{s.init(r,t),V.init(r,t),r._zod.parse=(i,o)=>{return i},r._zod.check=(i)=>{let o=i.value,n=t.fn(o);if(n instanceof Promise)return n.then((e)=>mf(e,i,o,r));mf(n,i,o,r);return}});function mf(r,t,i,o){if(!r){let n={code:"custom",input:i,inst:o,path:[...o._zod.def.path??[]],continue:!o._zod.def.abort};if(o._zod.def.params)n.params=o._zod.def.params;t.issues.push(en(n))}}var ei={};a(ei,{zhTW:()=>Fm,zhCN:()=>Qm,yo:()=>Gm,vi:()=>Em,ur:()=>Ym,ua:()=>Lm,tr:()=>Vm,th:()=>Km,ta:()=>Wm,sv:()=>qm,sl:()=>Pm,ru:()=>Xm,pt:()=>Jm,ps:()=>Um,pl:()=>km,ota:()=>am,no:()=>Im,nl:()=>pm,ms:()=>Om,mk:()=>jm,ko:()=>_m,kh:()=>Dm,ja:()=>zm,it:()=>fm,is:()=>wm,id:()=>xm,hu:()=>$m,he:()=>hm,frCA:()=>vm,fr:()=>mm,fi:()=>bm,fa:()=>gm,es:()=>um,eo:()=>cm,en:()=>oi,de:()=>lm,da:()=>em,cs:()=>om,ca:()=>im,be:()=>nm,az:()=>tm,ar:()=>rm});var sp=()=>{let r={string:{unit:"حرف",verb:"أن يحوي"},file:{unit:"بايت",verb:"أن يحوي"},array:{unit:"عنصر",verb:"أن يحوي"},set:{unit:"عنصر",verb:"أن يحوي"}};function t(n){return r[n]??null}let i=(n)=>{let e=typeof n;switch(e){case"number":return Number.isNaN(n)?"NaN":"number";case"object":{if(Array.isArray(n))return"array";if(n===null)return"null";if(Object.getPrototypeOf(n)!==Object.prototype&&n.constructor)return n.constructor.name}}return e},o={regex:"مدخل",email:"بريد إلكتروني",url:"رابط",emoji:"إيموجي",uuid:"UUID",uuidv4:"UUIDv4",uuidv6:"UUIDv6",nanoid:"nanoid",guid:"GUID",cuid:"cuid",cuid2:"cuid2",ulid:"ULID",xid:"XID",ksuid:"KSUID",datetime:"تاريخ ووقت بمعيار ISO",date:"تاريخ بمعيار ISO",time:"وقت بمعيار ISO",duration:"مدة بمعيار ISO",ipv4:"عنوان IPv4",ipv6:"عنوان IPv6",cidrv4:"مدى عناوين بصيغة IPv4",cidrv6:"مدى عناوين بصيغة IPv6",base64:"نَص بترميز base64-encoded",base64url:"نَص بترميز base64url-encoded",json_string:"نَص على هيئة JSON",e164:"رقم هاتف بمعيار E.164",jwt:"JWT",template_literal:"مدخل"};return(n)=>{switch(n.code){case"invalid_type":return`مدخلات غير مقبولة: يفترض إدخال ${n.expected}، ولكن تم إدخال ${i(n.input)}`;case"invalid_value":if(n.values.length===1)return`مدخلات غير مقبولة: يفترض إدخال ${j(n.values[0])}`;return`اختيار غير مقبول: يتوقع انتقاء أحد هذه الخيارات: ${x(n.values,"|")}`;case"too_big":{let e=n.inclusive?"<=":"<",l=t(n.origin);if(l)return` أكبر من اللازم: يفترض أن تكون ${n.origin??"القيمة"} ${e} ${n.maximum.toString()} ${l.unit??"عنصر"}`;return`أكبر من اللازم: يفترض أن تكون ${n.origin??"القيمة"} ${e} ${n.maximum.toString()}`}case"too_small":{let e=n.inclusive?">=":">",l=t(n.origin);if(l)return`أصغر من اللازم: يفترض لـ ${n.origin} أن يكون ${e} ${n.minimum.toString()} ${l.unit}`;return`أصغر من اللازم: يفترض لـ ${n.origin} أن يكون ${e} ${n.minimum.toString()}`}case"invalid_format":{let e=n;if(e.format==="starts_with")return`نَص غير مقبول: يجب أن يبدأ بـ "${n.prefix}"`;if(e.format==="ends_with")return`نَص غير مقبول: يجب أن ينتهي بـ "${e.suffix}"`;if(e.format==="includes")return`نَص غير مقبول: يجب أن يتضمَّن "${e.includes}"`;if(e.format==="regex")return`نَص غير مقبول: يجب أن يطابق النمط ${e.pattern}`;return`${o[e.format]??n.format} غير مقبول`}case"not_multiple_of":return`رقم غير مقبول: يجب أن يكون من مضاعفات ${n.divisor}`;case"unrecognized_keys":return`معرف${n.keys.length>1?"ات":""} غريب${n.keys.length>1?"ة":""}: ${x(n.keys,"، ")}`;case"invalid_key":return`معرف غير مقبول في ${n.origin}`;case"invalid_union":return"مدخل غير مقبول";case"invalid_element":return`مدخل غير مقبول في ${n.origin}`;default:return"مدخل غير مقبول"}}};function rm(){return{localeError:sp()}}var rI=()=>{let r={string:{unit:"simvol",verb:"olmalıdır"},file:{unit:"bayt",verb:"olmalıdır"},array:{unit:"element",verb:"olmalıdır"},set:{unit:"element",verb:"olmalıdır"}};function t(n){return r[n]??null}let i=(n)=>{let e=typeof n;switch(e){case"number":return Number.isNaN(n)?"NaN":"number";case"object":{if(Array.isArray(n))return"array";if(n===null)return"null";if(Object.getPrototypeOf(n)!==Object.prototype&&n.constructor)return n.constructor.name}}return e},o={regex:"input",email:"email address",url:"URL",emoji:"emoji",uuid:"UUID",uuidv4:"UUIDv4",uuidv6:"UUIDv6",nanoid:"nanoid",guid:"GUID",cuid:"cuid",cuid2:"cuid2",ulid:"ULID",xid:"XID",ksuid:"KSUID",datetime:"ISO datetime",date:"ISO date",time:"ISO time",duration:"ISO duration",ipv4:"IPv4 address",ipv6:"IPv6 address",cidrv4:"IPv4 range",cidrv6:"IPv6 range",base64:"base64-encoded string",base64url:"base64url-encoded string",json_string:"JSON string",e164:"E.164 number",jwt:"JWT",template_literal:"input"};return(n)=>{switch(n.code){case"invalid_type":return`Yanlış dəyər: gözlənilən ${n.expected}, daxil olan ${i(n.input)}`;case"invalid_value":if(n.values.length===1)return`Yanlış dəyər: gözlənilən ${j(n.values[0])}`;return`Yanlış seçim: aşağıdakılardan biri olmalıdır: ${x(n.values,"|")}`;case"too_big":{let e=n.inclusive?"<=":"<",l=t(n.origin);if(l)return`Çox böyük: gözlənilən ${n.origin??"dəyər"} ${e}${n.maximum.toString()} ${l.unit??"element"}`;return`Çox böyük: gözlənilən ${n.origin??"dəyər"} ${e}${n.maximum.toString()}`}case"too_small":{let e=n.inclusive?">=":">",l=t(n.origin);if(l)return`Çox kiçik: gözlənilən ${n.origin} ${e}${n.minimum.toString()} ${l.unit}`;return`Çox kiçik: gözlənilən ${n.origin} ${e}${n.minimum.toString()}`}case"invalid_format":{let e=n;if(e.format==="starts_with")return`Yanlış mətn: "${e.prefix}" ilə başlamalıdır`;if(e.format==="ends_with")return`Yanlış mətn: "${e.suffix}" ilə bitməlidir`;if(e.format==="includes")return`Yanlış mətn: "${e.includes}" daxil olmalıdır`;if(e.format==="regex")return`Yanlış mətn: ${e.pattern} şablonuna uyğun olmalıdır`;return`Yanlış ${o[e.format]??n.format}`}case"not_multiple_of":return`Yanlış ədəd: ${n.divisor} ilə bölünə bilən olmalıdır`;case"unrecognized_keys":return`Tanınmayan açar${n.keys.length>1?"lar":""}: ${x(n.keys,", ")}`;case"invalid_key":return`${n.origin} daxilində yanlış açar`;case"invalid_union":return"Yanlış dəyər";case"invalid_element":return`${n.origin} daxilində yanlış dəyər`;default:return"Yanlış dəyər"}}};function tm(){return{localeError:rI()}}function ff(r,t,i,o){let n=Math.abs(r),e=n%10,l=n%100;if(l>=11&&l<=19)return o;if(e===1)return t;if(e>=2&&e<=4)return i;return o}var tI=()=>{let r={string:{unit:{one:"сімвал",few:"сімвалы",many:"сімвалаў"},verb:"мець"},array:{unit:{one:"элемент",few:"элементы",many:"элементаў"},verb:"мець"},set:{unit:{one:"элемент",few:"элементы",many:"элементаў"},verb:"мець"},file:{unit:{one:"байт",few:"байты",many:"байтаў"},verb:"мець"}};function t(n){return r[n]??null}let i=(n)=>{let e=typeof n;switch(e){case"number":return Number.isNaN(n)?"NaN":"лік";case"object":{if(Array.isArray(n))return"масіў";if(n===null)return"null";if(Object.getPrototypeOf(n)!==Object.prototype&&n.constructor)return n.constructor.name}}return e},o={regex:"увод",email:"email адрас",url:"URL",emoji:"эмодзі",uuid:"UUID",uuidv4:"UUIDv4",uuidv6:"UUIDv6",nanoid:"nanoid",guid:"GUID",cuid:"cuid",cuid2:"cuid2",ulid:"ULID",xid:"XID",ksuid:"KSUID",datetime:"ISO дата і час",date:"ISO дата",time:"ISO час",duration:"ISO працягласць",ipv4:"IPv4 адрас",ipv6:"IPv6 адрас",cidrv4:"IPv4 дыяпазон",cidrv6:"IPv6 дыяпазон",base64:"радок у фармаце base64",base64url:"радок у фармаце base64url",json_string:"JSON радок",e164:"нумар E.164",jwt:"JWT",template_literal:"увод"};return(n)=>{switch(n.code){case"invalid_type":return`Няправільны ўвод: чакаўся ${n.expected}, атрымана ${i(n.input)}`;case"invalid_value":if(n.values.length===1)return`Няправільны ўвод: чакалася ${j(n.values[0])}`;return`Няправільны варыянт: чакаўся адзін з ${x(n.values,"|")}`;case"too_big":{let e=n.inclusive?"<=":"<",l=t(n.origin);if(l){let u=Number(n.maximum),g=ff(u,l.unit.one,l.unit.few,l.unit.many);return`Занадта вялікі: чакалася, што ${n.origin??"значэнне"} павінна ${l.verb} ${e}${n.maximum.toString()} ${g}`}return`Занадта вялікі: чакалася, што ${n.origin??"значэнне"} павінна быць ${e}${n.maximum.toString()}`}case"too_small":{let e=n.inclusive?">=":">",l=t(n.origin);if(l){let u=Number(n.minimum),g=ff(u,l.unit.one,l.unit.few,l.unit.many);return`Занадта малы: чакалася, што ${n.origin} павінна ${l.verb} ${e}${n.minimum.toString()} ${g}`}return`Занадта малы: чакалася, што ${n.origin} павінна быць ${e}${n.minimum.toString()}`}case"invalid_format":{let e=n;if(e.format==="starts_with")return`Няправільны радок: павінен пачынацца з "${e.prefix}"`;if(e.format==="ends_with")return`Няправільны радок: павінен заканчвацца на "${e.suffix}"`;if(e.format==="includes")return`Няправільны радок: павінен змяшчаць "${e.includes}"`;if(e.format==="regex")return`Няправільны радок: павінен адпавядаць шаблону ${e.pattern}`;return`Няправільны ${o[e.format]??n.format}`}case"not_multiple_of":return`Няправільны лік: павінен быць кратным ${n.divisor}`;case"unrecognized_keys":return`Нераспазнаны ${n.keys.length>1?"ключы":"ключ"}: ${x(n.keys,", ")}`;case"invalid_key":return`Няправільны ключ у ${n.origin}`;case"invalid_union":return"Няправільны ўвод";case"invalid_element":return`Няправільнае значэнне ў ${n.origin}`;default:return"Няправільны ўвод"}}};function nm(){return{localeError:tI()}}var nI=()=>{let r={string:{unit:"caràcters",verb:"contenir"},file:{unit:"bytes",verb:"contenir"},array:{unit:"elements",verb:"contenir"},set:{unit:"elements",verb:"contenir"}};function t(n){return r[n]??null}let i=(n)=>{let e=typeof n;switch(e){case"number":return Number.isNaN(n)?"NaN":"number";case"object":{if(Array.isArray(n))return"array";if(n===null)return"null";if(Object.getPrototypeOf(n)!==Object.prototype&&n.constructor)return n.constructor.name}}return e},o={regex:"entrada",email:"adreça electrònica",url:"URL",emoji:"emoji",uuid:"UUID",uuidv4:"UUIDv4",uuidv6:"UUIDv6",nanoid:"nanoid",guid:"GUID",cuid:"cuid",cuid2:"cuid2",ulid:"ULID",xid:"XID",ksuid:"KSUID",datetime:"data i hora ISO",date:"data ISO",time:"hora ISO",duration:"durada ISO",ipv4:"adreça IPv4",ipv6:"adreça IPv6",cidrv4:"rang IPv4",cidrv6:"rang IPv6",base64:"cadena codificada en base64",base64url:"cadena codificada en base64url",json_string:"cadena JSON",e164:"número E.164",jwt:"JWT",template_literal:"entrada"};return(n)=>{switch(n.code){case"invalid_type":return`Tipus invàlid: s'esperava ${n.expected}, s'ha rebut ${i(n.input)}`;case"invalid_value":if(n.values.length===1)return`Valor invàlid: s'esperava ${j(n.values[0])}`;return`Opció invàlida: s'esperava una de ${x(n.values," o ")}`;case"too_big":{let e=n.inclusive?"com a màxim":"menys de",l=t(n.origin);if(l)return`Massa gran: s'esperava que ${n.origin??"el valor"} contingués ${e} ${n.maximum.toString()} ${l.unit??"elements"}`;return`Massa gran: s'esperava que ${n.origin??"el valor"} fos ${e} ${n.maximum.toString()}`}case"too_small":{let e=n.inclusive?"com a mínim":"més de",l=t(n.origin);if(l)return`Massa petit: s'esperava que ${n.origin} contingués ${e} ${n.minimum.toString()} ${l.unit}`;return`Massa petit: s'esperava que ${n.origin} fos ${e} ${n.minimum.toString()}`}case"invalid_format":{let e=n;if(e.format==="starts_with")return`Format invàlid: ha de començar amb "${e.prefix}"`;if(e.format==="ends_with")return`Format invàlid: ha d'acabar amb "${e.suffix}"`;if(e.format==="includes")return`Format invàlid: ha d'incloure "${e.includes}"`;if(e.format==="regex")return`Format invàlid: ha de coincidir amb el patró ${e.pattern}`;return`Format invàlid per a ${o[e.format]??n.format}`}case"not_multiple_of":return`Número invàlid: ha de ser múltiple de ${n.divisor}`;case"unrecognized_keys":return`Clau${n.keys.length>1?"s":""} no reconeguda${n.keys.length>1?"s":""}: ${x(n.keys,", ")}`;case"invalid_key":return`Clau invàlida a ${n.origin}`;case"invalid_union":return"Entrada invàlida";case"invalid_element":return`Element invàlid a ${n.origin}`;default:return"Entrada invàlida"}}};function im(){return{localeError:nI()}}var iI=()=>{let r={string:{unit:"znaků",verb:"mít"},file:{unit:"bajtů",verb:"mít"},array:{unit:"prvků",verb:"mít"},set:{unit:"prvků",verb:"mít"}};function t(n){return r[n]??null}let i=(n)=>{let e=typeof n;switch(e){case"number":return Number.isNaN(n)?"NaN":"číslo";case"string":return"řetězec";case"boolean":return"boolean";case"bigint":return"bigint";case"function":return"funkce";case"symbol":return"symbol";case"undefined":return"undefined";case"object":{if(Array.isArray(n))return"pole";if(n===null)return"null";if(Object.getPrototypeOf(n)!==Object.prototype&&n.constructor)return n.constructor.name}}return e},o={regex:"regulární výraz",email:"e-mailová adresa",url:"URL",emoji:"emoji",uuid:"UUID",uuidv4:"UUIDv4",uuidv6:"UUIDv6",nanoid:"nanoid",guid:"GUID",cuid:"cuid",cuid2:"cuid2",ulid:"ULID",xid:"XID",ksuid:"KSUID",datetime:"datum a čas ve formátu ISO",date:"datum ve formátu ISO",time:"čas ve formátu ISO",duration:"doba trvání ISO",ipv4:"IPv4 adresa",ipv6:"IPv6 adresa",cidrv4:"rozsah IPv4",cidrv6:"rozsah IPv6",base64:"řetězec zakódovaný ve formátu base64",base64url:"řetězec zakódovaný ve formátu base64url",json_string:"řetězec ve formátu JSON",e164:"číslo E.164",jwt:"JWT",template_literal:"vstup"};return(n)=>{switch(n.code){case"invalid_type":return`Neplatný vstup: očekáváno ${n.expected}, obdrženo ${i(n.input)}`;case"invalid_value":if(n.values.length===1)return`Neplatný vstup: očekáváno ${j(n.values[0])}`;return`Neplatná možnost: očekávána jedna z hodnot ${x(n.values,"|")}`;case"too_big":{let e=n.inclusive?"<=":"<",l=t(n.origin);if(l)return`Hodnota je příliš velká: ${n.origin??"hodnota"} musí mít ${e}${n.maximum.toString()} ${l.unit??"prvků"}`;return`Hodnota je příliš velká: ${n.origin??"hodnota"} musí být ${e}${n.maximum.toString()}`}case"too_small":{let e=n.inclusive?">=":">",l=t(n.origin);if(l)return`Hodnota je příliš malá: ${n.origin??"hodnota"} musí mít ${e}${n.minimum.toString()} ${l.unit??"prvků"}`;return`Hodnota je příliš malá: ${n.origin??"hodnota"} musí být ${e}${n.minimum.toString()}`}case"invalid_format":{let e=n;if(e.format==="starts_with")return`Neplatný řetězec: musí začínat na "${e.prefix}"`;if(e.format==="ends_with")return`Neplatný řetězec: musí končit na "${e.suffix}"`;if(e.format==="includes")return`Neplatný řetězec: musí obsahovat "${e.includes}"`;if(e.format==="regex")return`Neplatný řetězec: musí odpovídat vzoru ${e.pattern}`;return`Neplatný formát ${o[e.format]??n.format}`}case"not_multiple_of":return`Neplatné číslo: musí být násobkem ${n.divisor}`;case"unrecognized_keys":return`Neznámé klíče: ${x(n.keys,", ")}`;case"invalid_key":return`Neplatný klíč v ${n.origin}`;case"invalid_union":return"Neplatný vstup";case"invalid_element":return`Neplatná hodnota v ${n.origin}`;default:return"Neplatný vstup"}}};function om(){return{localeError:iI()}}var oI=()=>{let r={string:{unit:"tegn",verb:"havde"},file:{unit:"bytes",verb:"havde"},array:{unit:"elementer",verb:"indeholdt"},set:{unit:"elementer",verb:"indeholdt"}},t={string:"streng",number:"tal",boolean:"boolean",array:"liste",object:"objekt",set:"sæt",file:"fil"};function i(l){return r[l]??null}function o(l){return t[l]??l}let n=(l)=>{let u=typeof l;switch(u){case"number":return Number.isNaN(l)?"NaN":"tal";case"object":{if(Array.isArray(l))return"liste";if(l===null)return"null";if(Object.getPrototypeOf(l)!==Object.prototype&&l.constructor)return l.constructor.name;return"objekt"}}return u},e={regex:"input",email:"e-mailadresse",url:"URL",emoji:"emoji",uuid:"UUID",uuidv4:"UUIDv4",uuidv6:"UUIDv6",nanoid:"nanoid",guid:"GUID",cuid:"cuid",cuid2:"cuid2",ulid:"ULID",xid:"XID",ksuid:"KSUID",datetime:"ISO dato- og klokkeslæt",date:"ISO-dato",time:"ISO-klokkeslæt",duration:"ISO-varighed",ipv4:"IPv4-område",ipv6:"IPv6-område",cidrv4:"IPv4-spektrum",cidrv6:"IPv6-spektrum",base64:"base64-kodet streng",base64url:"base64url-kodet streng",json_string:"JSON-streng",e164:"E.164-nummer",jwt:"JWT",template_literal:"input"};return(l)=>{switch(l.code){case"invalid_type":return`Ugyldigt input: forventede ${o(l.expected)}, fik ${o(n(l.input))}`;case"invalid_value":if(l.values.length===1)return`Ugyldig værdi: forventede ${j(l.values[0])}`;return`Ugyldigt valg: forventede en af følgende ${x(l.values,"|")}`;case"too_big":{let u=l.inclusive?"<=":"<",g=i(l.origin),c=o(l.origin);if(g)return`For stor: forventede ${c??"value"} ${g.verb} ${u} ${l.maximum.toString()} ${g.unit??"elementer"}`;return`For stor: forventede ${c??"value"} havde ${u} ${l.maximum.toString()}`}case"too_small":{let u=l.inclusive?">=":">",g=i(l.origin),c=o(l.origin);if(g)return`For lille: forventede ${c} ${g.verb} ${u} ${l.minimum.toString()} ${g.unit}`;return`For lille: forventede ${c} havde ${u} ${l.minimum.toString()}`}case"invalid_format":{let u=l;if(u.format==="starts_with")return`Ugyldig streng: skal starte med "${u.prefix}"`;if(u.format==="ends_with")return`Ugyldig streng: skal ende med "${u.suffix}"`;if(u.format==="includes")return`Ugyldig streng: skal indeholde "${u.includes}"`;if(u.format==="regex")return`Ugyldig streng: skal matche mønsteret ${u.pattern}`;return`Ugyldig ${e[u.format]??l.format}`}case"not_multiple_of":return`Ugyldigt tal: skal være deleligt med ${l.divisor}`;case"unrecognized_keys":return`${l.keys.length>1?"Ukendte nøgler":"Ukendt nøgle"}: ${x(l.keys,", ")}`;case"invalid_key":return`Ugyldig nøgle i ${l.origin}`;case"invalid_union":return"Ugyldigt input: matcher ingen af de tilladte typer";case"invalid_element":return`Ugyldig værdi i ${l.origin}`;default:return"Ugyldigt input"}}};function em(){return{localeError:oI()}}var eI=()=>{let r={string:{unit:"Zeichen",verb:"zu haben"},file:{unit:"Bytes",verb:"zu haben"},array:{unit:"Elemente",verb:"zu haben"},set:{unit:"Elemente",verb:"zu haben"}};function t(n){return r[n]??null}let i=(n)=>{let e=typeof n;switch(e){case"number":return Number.isNaN(n)?"NaN":"Zahl";case"object":{if(Array.isArray(n))return"Array";if(n===null)return"null";if(Object.getPrototypeOf(n)!==Object.prototype&&n.constructor)return n.constructor.name}}return e},o={regex:"Eingabe",email:"E-Mail-Adresse",url:"URL",emoji:"Emoji",uuid:"UUID",uuidv4:"UUIDv4",uuidv6:"UUIDv6",nanoid:"nanoid",guid:"GUID",cuid:"cuid",cuid2:"cuid2",ulid:"ULID",xid:"XID",ksuid:"KSUID",datetime:"ISO-Datum und -Uhrzeit",date:"ISO-Datum",time:"ISO-Uhrzeit",duration:"ISO-Dauer",ipv4:"IPv4-Adresse",ipv6:"IPv6-Adresse",cidrv4:"IPv4-Bereich",cidrv6:"IPv6-Bereich",base64:"Base64-codierter String",base64url:"Base64-URL-codierter String",json_string:"JSON-String",e164:"E.164-Nummer",jwt:"JWT",template_literal:"Eingabe"};return(n)=>{switch(n.code){case"invalid_type":return`Ungültige Eingabe: erwartet ${n.expected}, erhalten ${i(n.input)}`;case"invalid_value":if(n.values.length===1)return`Ungültige Eingabe: erwartet ${j(n.values[0])}`;return`Ungültige Option: erwartet eine von ${x(n.values,"|")}`;case"too_big":{let e=n.inclusive?"<=":"<",l=t(n.origin);if(l)return`Zu groß: erwartet, dass ${n.origin??"Wert"} ${e}${n.maximum.toString()} ${l.unit??"Elemente"} hat`;return`Zu groß: erwartet, dass ${n.origin??"Wert"} ${e}${n.maximum.toString()} ist`}case"too_small":{let e=n.inclusive?">=":">",l=t(n.origin);if(l)return`Zu klein: erwartet, dass ${n.origin} ${e}${n.minimum.toString()} ${l.unit} hat`;return`Zu klein: erwartet, dass ${n.origin} ${e}${n.minimum.toString()} ist`}case"invalid_format":{let e=n;if(e.format==="starts_with")return`Ungültiger String: muss mit "${e.prefix}" beginnen`;if(e.format==="ends_with")return`Ungültiger String: muss mit "${e.suffix}" enden`;if(e.format==="includes")return`Ungültiger String: muss "${e.includes}" enthalten`;if(e.format==="regex")return`Ungültiger String: muss dem Muster ${e.pattern} entsprechen`;return`Ungültig: ${o[e.format]??n.format}`}case"not_multiple_of":return`Ungültige Zahl: muss ein Vielfaches von ${n.divisor} sein`;case"unrecognized_keys":return`${n.keys.length>1?"Unbekannte Schlüssel":"Unbekannter Schlüssel"}: ${x(n.keys,", ")}`;case"invalid_key":return`Ungültiger Schlüssel in ${n.origin}`;case"invalid_union":return"Ungültige Eingabe";case"invalid_element":return`Ungültiger Wert in ${n.origin}`;default:return"Ungültige Eingabe"}}};function lm(){return{localeError:eI()}}var lI=(r)=>{let t=typeof r;switch(t){case"number":return Number.isNaN(r)?"NaN":"number";case"object":{if(Array.isArray(r))return"array";if(r===null)return"null";if(Object.getPrototypeOf(r)!==Object.prototype&&r.constructor)return r.constructor.name}}return t},cI=()=>{let r={string:{unit:"characters",verb:"to have"},file:{unit:"bytes",verb:"to have"},array:{unit:"items",verb:"to have"},set:{unit:"items",verb:"to have"}};function t(o){return r[o]??null}let i={regex:"input",email:"email address",url:"URL",emoji:"emoji",uuid:"UUID",uuidv4:"UUIDv4",uuidv6:"UUIDv6",nanoid:"nanoid",guid:"GUID",cuid:"cuid",cuid2:"cuid2",ulid:"ULID",xid:"XID",ksuid:"KSUID",datetime:"ISO datetime",date:"ISO date",time:"ISO time",duration:"ISO duration",ipv4:"IPv4 address",ipv6:"IPv6 address",cidrv4:"IPv4 range",cidrv6:"IPv6 range",base64:"base64-encoded string",base64url:"base64url-encoded string",json_string:"JSON string",e164:"E.164 number",jwt:"JWT",template_literal:"input"};return(o)=>{switch(o.code){case"invalid_type":return`Invalid input: expected ${o.expected}, received ${lI(o.input)}`;case"invalid_value":if(o.values.length===1)return`Invalid input: expected ${j(o.values[0])}`;return`Invalid option: expected one of ${x(o.values,"|")}`;case"too_big":{let n=o.inclusive?"<=":"<",e=t(o.origin);if(e)return`Too big: expected ${o.origin??"value"} to have ${n}${o.maximum.toString()} ${e.unit??"elements"}`;return`Too big: expected ${o.origin??"value"} to be ${n}${o.maximum.toString()}`}case"too_small":{let n=o.inclusive?">=":">",e=t(o.origin);if(e)return`Too small: expected ${o.origin} to have ${n}${o.minimum.toString()} ${e.unit}`;return`Too small: expected ${o.origin} to be ${n}${o.minimum.toString()}`}case"invalid_format":{let n=o;if(n.format==="starts_with")return`Invalid string: must start with "${n.prefix}"`;if(n.format==="ends_with")return`Invalid string: must end with "${n.suffix}"`;if(n.format==="includes")return`Invalid string: must include "${n.includes}"`;if(n.format==="regex")return`Invalid string: must match pattern ${n.pattern}`;return`Invalid ${i[n.format]??o.format}`}case"not_multiple_of":return`Invalid number: must be a multiple of ${o.divisor}`;case"unrecognized_keys":return`Unrecognized key${o.keys.length>1?"s":""}: ${x(o.keys,", ")}`;case"invalid_key":return`Invalid key in ${o.origin}`;case"invalid_union":return"Invalid input";case"invalid_element":return`Invalid value in ${o.origin}`;default:return"Invalid input"}}};function oi(){return{localeError:cI()}}var uI=(r)=>{let t=typeof r;switch(t){case"number":return Number.isNaN(r)?"NaN":"nombro";case"object":{if(Array.isArray(r))return"tabelo";if(r===null)return"senvalora";if(Object.getPrototypeOf(r)!==Object.prototype&&r.constructor)return r.constructor.name}}return t},gI=()=>{let r={string:{unit:"karaktrojn",verb:"havi"},file:{unit:"bajtojn",verb:"havi"},array:{unit:"elementojn",verb:"havi"},set:{unit:"elementojn",verb:"havi"}};function t(o){return r[o]??null}let i={regex:"enigo",email:"retadreso",url:"URL",emoji:"emoĝio",uuid:"UUID",uuidv4:"UUIDv4",uuidv6:"UUIDv6",nanoid:"nanoid",guid:"GUID",cuid:"cuid",cuid2:"cuid2",ulid:"ULID",xid:"XID",ksuid:"KSUID",datetime:"ISO-datotempo",date:"ISO-dato",time:"ISO-tempo",duration:"ISO-daŭro",ipv4:"IPv4-adreso",ipv6:"IPv6-adreso",cidrv4:"IPv4-rango",cidrv6:"IPv6-rango",base64:"64-ume kodita karaktraro",base64url:"URL-64-ume kodita karaktraro",json_string:"JSON-karaktraro",e164:"E.164-nombro",jwt:"JWT",template_literal:"enigo"};return(o)=>{switch(o.code){case"invalid_type":return`Nevalida enigo: atendiĝis ${o.expected}, riceviĝis ${uI(o.input)}`;case"invalid_value":if(o.values.length===1)return`Nevalida enigo: atendiĝis ${j(o.values[0])}`;return`Nevalida opcio: atendiĝis unu el ${x(o.values,"|")}`;case"too_big":{let n=o.inclusive?"<=":"<",e=t(o.origin);if(e)return`Tro granda: atendiĝis ke ${o.origin??"valoro"} havu ${n}${o.maximum.toString()} ${e.unit??"elementojn"}`;return`Tro granda: atendiĝis ke ${o.origin??"valoro"} havu ${n}${o.maximum.toString()}`}case"too_small":{let n=o.inclusive?">=":">",e=t(o.origin);if(e)return`Tro malgranda: atendiĝis ke ${o.origin} havu ${n}${o.minimum.toString()} ${e.unit}`;return`Tro malgranda: atendiĝis ke ${o.origin} estu ${n}${o.minimum.toString()}`}case"invalid_format":{let n=o;if(n.format==="starts_with")return`Nevalida karaktraro: devas komenciĝi per "${n.prefix}"`;if(n.format==="ends_with")return`Nevalida karaktraro: devas finiĝi per "${n.suffix}"`;if(n.format==="includes")return`Nevalida karaktraro: devas inkluzivi "${n.includes}"`;if(n.format==="regex")return`Nevalida karaktraro: devas kongrui kun la modelo ${n.pattern}`;return`Nevalida ${i[n.format]??o.format}`}case"not_multiple_of":return`Nevalida nombro: devas esti oblo de ${o.divisor}`;case"unrecognized_keys":return`Nekonata${o.keys.length>1?"j":""} ŝlosilo${o.keys.length>1?"j":""}: ${x(o.keys,", ")}`;case"invalid_key":return`Nevalida ŝlosilo en ${o.origin}`;case"invalid_union":return"Nevalida enigo";case"invalid_element":return`Nevalida valoro en ${o.origin}`;default:return"Nevalida enigo"}}};function cm(){return{localeError:gI()}}var bI=()=>{let r={string:{unit:"caracteres",verb:"tener"},file:{unit:"bytes",verb:"tener"},array:{unit:"elementos",verb:"tener"},set:{unit:"elementos",verb:"tener"}};function t(n){return r[n]??null}let i=(n)=>{let e=typeof n;switch(e){case"number":return Number.isNaN(n)?"NaN":"número";case"object":{if(Array.isArray(n))return"arreglo";if(n===null)return"nulo";if(Object.getPrototypeOf(n)!==Object.prototype)return n.constructor.name}}return e},o={regex:"entrada",email:"dirección de correo electrónico",url:"URL",emoji:"emoji",uuid:"UUID",uuidv4:"UUIDv4",uuidv6:"UUIDv6",nanoid:"nanoid",guid:"GUID",cuid:"cuid",cuid2:"cuid2",ulid:"ULID",xid:"XID",ksuid:"KSUID",datetime:"fecha y hora ISO",date:"fecha ISO",time:"hora ISO",duration:"duración ISO",ipv4:"dirección IPv4",ipv6:"dirección IPv6",cidrv4:"rango IPv4",cidrv6:"rango IPv6",base64:"cadena codificada en base64",base64url:"URL codificada en base64",json_string:"cadena JSON",e164:"número E.164",jwt:"JWT",template_literal:"entrada"};return(n)=>{switch(n.code){case"invalid_type":return`Entrada inválida: se esperaba ${n.expected}, recibido ${i(n.input)}`;case"invalid_value":if(n.values.length===1)return`Entrada inválida: se esperaba ${j(n.values[0])}`;return`Opción inválida: se esperaba una de ${x(n.values,"|")}`;case"too_big":{let e=n.inclusive?"<=":"<",l=t(n.origin);if(l)return`Demasiado grande: se esperaba que ${n.origin??"valor"} tuviera ${e}${n.maximum.toString()} ${l.unit??"elementos"}`;return`Demasiado grande: se esperaba que ${n.origin??"valor"} fuera ${e}${n.maximum.toString()}`}case"too_small":{let e=n.inclusive?">=":">",l=t(n.origin);if(l)return`Demasiado pequeño: se esperaba que ${n.origin} tuviera ${e}${n.minimum.toString()} ${l.unit}`;return`Demasiado pequeño: se esperaba que ${n.origin} fuera ${e}${n.minimum.toString()}`}case"invalid_format":{let e=n;if(e.format==="starts_with")return`Cadena inválida: debe comenzar con "${e.prefix}"`;if(e.format==="ends_with")return`Cadena inválida: debe terminar en "${e.suffix}"`;if(e.format==="includes")return`Cadena inválida: debe incluir "${e.includes}"`;if(e.format==="regex")return`Cadena inválida: debe coincidir con el patrón ${e.pattern}`;return`Inválido ${o[e.format]??n.format}`}case"not_multiple_of":return`Número inválido: debe ser múltiplo de ${n.divisor}`;case"unrecognized_keys":return`Llave${n.keys.length>1?"s":""} desconocida${n.keys.length>1?"s":""}: ${x(n.keys,", ")}`;case"invalid_key":return`Llave inválida en ${n.origin}`;case"invalid_union":return"Entrada inválida";case"invalid_element":return`Valor inválido en ${n.origin}`;default:return"Entrada inválida"}}};function um(){return{localeError:bI()}}var mI=()=>{let r={string:{unit:"کاراکتر",verb:"داشته باشد"},file:{unit:"بایت",verb:"داشته باشد"},array:{unit:"آیتم",verb:"داشته باشد"},set:{unit:"آیتم",verb:"داشته باشد"}};function t(n){return r[n]??null}let i=(n)=>{let e=typeof n;switch(e){case"number":return Number.isNaN(n)?"NaN":"عدد";case"object":{if(Array.isArray(n))return"آرایه";if(n===null)return"null";if(Object.getPrototypeOf(n)!==Object.prototype&&n.constructor)return n.constructor.name}}return e},o={regex:"ورودی",email:"آدرس ایمیل",url:"URL",emoji:"ایموجی",uuid:"UUID",uuidv4:"UUIDv4",uuidv6:"UUIDv6",nanoid:"nanoid",guid:"GUID",cuid:"cuid",cuid2:"cuid2",ulid:"ULID",xid:"XID",ksuid:"KSUID",datetime:"تاریخ و زمان ایزو",date:"تاریخ ایزو",time:"زمان ایزو",duration:"مدت زمان ایزو",ipv4:"IPv4 آدرس",ipv6:"IPv6 آدرس",cidrv4:"IPv4 دامنه",cidrv6:"IPv6 دامنه",base64:"base64-encoded رشته",base64url:"base64url-encoded رشته",json_string:"JSON رشته",e164:"E.164 عدد",jwt:"JWT",template_literal:"ورودی"};return(n)=>{switch(n.code){case"invalid_type":return`ورودی نامعتبر: می‌بایست ${n.expected} می‌بود، ${i(n.input)} دریافت شد`;case"invalid_value":if(n.values.length===1)return`ورودی نامعتبر: می‌بایست ${j(n.values[0])} می‌بود`;return`گزینه نامعتبر: می‌بایست یکی از ${x(n.values,"|")} می‌بود`;case"too_big":{let e=n.inclusive?"<=":"<",l=t(n.origin);if(l)return`خیلی بزرگ: ${n.origin??"مقدار"} باید ${e}${n.maximum.toString()} ${l.unit??"عنصر"} باشد`;return`خیلی بزرگ: ${n.origin??"مقدار"} باید ${e}${n.maximum.toString()} باشد`}case"too_small":{let e=n.inclusive?">=":">",l=t(n.origin);if(l)return`خیلی کوچک: ${n.origin} باید ${e}${n.minimum.toString()} ${l.unit} باشد`;return`خیلی کوچک: ${n.origin} باید ${e}${n.minimum.toString()} باشد`}case"invalid_format":{let e=n;if(e.format==="starts_with")return`رشته نامعتبر: باید با "${e.prefix}" شروع شود`;if(e.format==="ends_with")return`رشته نامعتبر: باید با "${e.suffix}" تمام شود`;if(e.format==="includes")return`رشته نامعتبر: باید شامل "${e.includes}" باشد`;if(e.format==="regex")return`رشته نامعتبر: باید با الگوی ${e.pattern} مطابقت داشته باشد`;return`${o[e.format]??n.format} نامعتبر`}case"not_multiple_of":return`عدد نامعتبر: باید مضرب ${n.divisor} باشد`;case"unrecognized_keys":return`کلید${n.keys.length>1?"های":""} ناشناس: ${x(n.keys,", ")}`;case"invalid_key":return`کلید ناشناس در ${n.origin}`;case"invalid_union":return"ورودی نامعتبر";case"invalid_element":return`مقدار نامعتبر در ${n.origin}`;default:return"ورودی نامعتبر"}}};function gm(){return{localeError:mI()}}var vI=()=>{let r={string:{unit:"merkkiä",subject:"merkkijonon"},file:{unit:"tavua",subject:"tiedoston"},array:{unit:"alkiota",subject:"listan"},set:{unit:"alkiota",subject:"joukon"},number:{unit:"",subject:"luvun"},bigint:{unit:"",subject:"suuren kokonaisluvun"},int:{unit:"",subject:"kokonaisluvun"},date:{unit:"",subject:"päivämäärän"}};function t(n){return r[n]??null}let i=(n)=>{let e=typeof n;switch(e){case"number":return Number.isNaN(n)?"NaN":"number";case"object":{if(Array.isArray(n))return"array";if(n===null)return"null";if(Object.getPrototypeOf(n)!==Object.prototype&&n.constructor)return n.constructor.name}}return e},o={regex:"säännöllinen lauseke",email:"sähköpostiosoite",url:"URL-osoite",emoji:"emoji",uuid:"UUID",uuidv4:"UUIDv4",uuidv6:"UUIDv6",nanoid:"nanoid",guid:"GUID",cuid:"cuid",cuid2:"cuid2",ulid:"ULID",xid:"XID",ksuid:"KSUID",datetime:"ISO-aikaleima",date:"ISO-päivämäärä",time:"ISO-aika",duration:"ISO-kesto",ipv4:"IPv4-osoite",ipv6:"IPv6-osoite",cidrv4:"IPv4-alue",cidrv6:"IPv6-alue",base64:"base64-koodattu merkkijono",base64url:"base64url-koodattu merkkijono",json_string:"JSON-merkkijono",e164:"E.164-luku",jwt:"JWT",template_literal:"templaattimerkkijono"};return(n)=>{switch(n.code){case"invalid_type":return`Virheellinen tyyppi: odotettiin ${n.expected}, oli ${i(n.input)}`;case"invalid_value":if(n.values.length===1)return`Virheellinen syöte: täytyy olla ${j(n.values[0])}`;return`Virheellinen valinta: täytyy olla yksi seuraavista: ${x(n.values,"|")}`;case"too_big":{let e=n.inclusive?"<=":"<",l=t(n.origin);if(l)return`Liian suuri: ${l.subject} täytyy olla ${e}${n.maximum.toString()} ${l.unit}`.trim();return`Liian suuri: arvon täytyy olla ${e}${n.maximum.toString()}`}case"too_small":{let e=n.inclusive?">=":">",l=t(n.origin);if(l)return`Liian pieni: ${l.subject} täytyy olla ${e}${n.minimum.toString()} ${l.unit}`.trim();return`Liian pieni: arvon täytyy olla ${e}${n.minimum.toString()}`}case"invalid_format":{let e=n;if(e.format==="starts_with")return`Virheellinen syöte: täytyy alkaa "${e.prefix}"`;if(e.format==="ends_with")return`Virheellinen syöte: täytyy loppua "${e.suffix}"`;if(e.format==="includes")return`Virheellinen syöte: täytyy sisältää "${e.includes}"`;if(e.format==="regex")return`Virheellinen syöte: täytyy vastata säännöllistä lauseketta ${e.pattern}`;return`Virheellinen ${o[e.format]??n.format}`}case"not_multiple_of":return`Virheellinen luku: täytyy olla luvun ${n.divisor} monikerta`;case"unrecognized_keys":return`${n.keys.length>1?"Tuntemattomat avaimet":"Tuntematon avain"}: ${x(n.keys,", ")}`;case"invalid_key":return"Virheellinen avain tietueessa";case"invalid_union":return"Virheellinen unioni";case"invalid_element":return"Virheellinen arvo joukossa";default:return"Virheellinen syöte"}}};function bm(){return{localeError:vI()}}var hI=()=>{let r={string:{unit:"caractères",verb:"avoir"},file:{unit:"octets",verb:"avoir"},array:{unit:"éléments",verb:"avoir"},set:{unit:"éléments",verb:"avoir"}};function t(n){return r[n]??null}let i=(n)=>{let e=typeof n;switch(e){case"number":return Number.isNaN(n)?"NaN":"nombre";case"object":{if(Array.isArray(n))return"tableau";if(n===null)return"null";if(Object.getPrototypeOf(n)!==Object.prototype&&n.constructor)return n.constructor.name}}return e},o={regex:"entrée",email:"adresse e-mail",url:"URL",emoji:"emoji",uuid:"UUID",uuidv4:"UUIDv4",uuidv6:"UUIDv6",nanoid:"nanoid",guid:"GUID",cuid:"cuid",cuid2:"cuid2",ulid:"ULID",xid:"XID",ksuid:"KSUID",datetime:"date et heure ISO",date:"date ISO",time:"heure ISO",duration:"durée ISO",ipv4:"adresse IPv4",ipv6:"adresse IPv6",cidrv4:"plage IPv4",cidrv6:"plage IPv6",base64:"chaîne encodée en base64",base64url:"chaîne encodée en base64url",json_string:"chaîne JSON",e164:"numéro E.164",jwt:"JWT",template_literal:"entrée"};return(n)=>{switch(n.code){case"invalid_type":return`Entrée invalide : ${n.expected} attendu, ${i(n.input)} reçu`;case"invalid_value":if(n.values.length===1)return`Entrée invalide : ${j(n.values[0])} attendu`;return`Option invalide : une valeur parmi ${x(n.values,"|")} attendue`;case"too_big":{let e=n.inclusive?"<=":"<",l=t(n.origin);if(l)return`Trop grand : ${n.origin??"valeur"} doit ${l.verb} ${e}${n.maximum.toString()} ${l.unit??"élément(s)"}`;return`Trop grand : ${n.origin??"valeur"} doit être ${e}${n.maximum.toString()}`}case"too_small":{let e=n.inclusive?">=":">",l=t(n.origin);if(l)return`Trop petit : ${n.origin} doit ${l.verb} ${e}${n.minimum.toString()} ${l.unit}`;return`Trop petit : ${n.origin} doit être ${e}${n.minimum.toString()}`}case"invalid_format":{let e=n;if(e.format==="starts_with")return`Chaîne invalide : doit commencer par "${e.prefix}"`;if(e.format==="ends_with")return`Chaîne invalide : doit se terminer par "${e.suffix}"`;if(e.format==="includes")return`Chaîne invalide : doit inclure "${e.includes}"`;if(e.format==="regex")return`Chaîne invalide : doit correspondre au modèle ${e.pattern}`;return`${o[e.format]??n.format} invalide`}case"not_multiple_of":return`Nombre invalide : doit être un multiple de ${n.divisor}`;case"unrecognized_keys":return`Clé${n.keys.length>1?"s":""} non reconnue${n.keys.length>1?"s":""} : ${x(n.keys,", ")}`;case"invalid_key":return`Clé invalide dans ${n.origin}`;case"invalid_union":return"Entrée invalide";case"invalid_element":return`Valeur invalide dans ${n.origin}`;default:return"Entrée invalide"}}};function mm(){return{localeError:hI()}}var $I=()=>{let r={string:{unit:"caractères",verb:"avoir"},file:{unit:"octets",verb:"avoir"},array:{unit:"éléments",verb:"avoir"},set:{unit:"éléments",verb:"avoir"}};function t(n){return r[n]??null}let i=(n)=>{let e=typeof n;switch(e){case"number":return Number.isNaN(n)?"NaN":"number";case"object":{if(Array.isArray(n))return"array";if(n===null)return"null";if(Object.getPrototypeOf(n)!==Object.prototype&&n.constructor)return n.constructor.name}}return e},o={regex:"entrée",email:"adresse courriel",url:"URL",emoji:"emoji",uuid:"UUID",uuidv4:"UUIDv4",uuidv6:"UUIDv6",nanoid:"nanoid",guid:"GUID",cuid:"cuid",cuid2:"cuid2",ulid:"ULID",xid:"XID",ksuid:"KSUID",datetime:"date-heure ISO",date:"date ISO",time:"heure ISO",duration:"durée ISO",ipv4:"adresse IPv4",ipv6:"adresse IPv6",cidrv4:"plage IPv4",cidrv6:"plage IPv6",base64:"chaîne encodée en base64",base64url:"chaîne encodée en base64url",json_string:"chaîne JSON",e164:"numéro E.164",jwt:"JWT",template_literal:"entrée"};return(n)=>{switch(n.code){case"invalid_type":return`Entrée invalide : attendu ${n.expected}, reçu ${i(n.input)}`;case"invalid_value":if(n.values.length===1)return`Entrée invalide : attendu ${j(n.values[0])}`;return`Option invalide : attendu l'une des valeurs suivantes ${x(n.values,"|")}`;case"too_big":{let e=n.inclusive?"≤":"<",l=t(n.origin);if(l)return`Trop grand : attendu que ${n.origin??"la valeur"} ait ${e}${n.maximum.toString()} ${l.unit}`;return`Trop grand : attendu que ${n.origin??"la valeur"} soit ${e}${n.maximum.toString()}`}case"too_small":{let e=n.inclusive?"≥":">",l=t(n.origin);if(l)return`Trop petit : attendu que ${n.origin} ait ${e}${n.minimum.toString()} ${l.unit}`;return`Trop petit : attendu que ${n.origin} soit ${e}${n.minimum.toString()}`}case"invalid_format":{let e=n;if(e.format==="starts_with")return`Chaîne invalide : doit commencer par "${e.prefix}"`;if(e.format==="ends_with")return`Chaîne invalide : doit se terminer par "${e.suffix}"`;if(e.format==="includes")return`Chaîne invalide : doit inclure "${e.includes}"`;if(e.format==="regex")return`Chaîne invalide : doit correspondre au motif ${e.pattern}`;return`${o[e.format]??n.format} invalide`}case"not_multiple_of":return`Nombre invalide : doit être un multiple de ${n.divisor}`;case"unrecognized_keys":return`Clé${n.keys.length>1?"s":""} non reconnue${n.keys.length>1?"s":""} : ${x(n.keys,", ")}`;case"invalid_key":return`Clé invalide dans ${n.origin}`;case"invalid_union":return"Entrée invalide";case"invalid_element":return`Valeur invalide dans ${n.origin}`;default:return"Entrée invalide"}}};function vm(){return{localeError:$I()}}var xI=()=>{let r={string:{unit:"אותיות",verb:"לכלול"},file:{unit:"בייטים",verb:"לכלול"},array:{unit:"פריטים",verb:"לכלול"},set:{unit:"פריטים",verb:"לכלול"}};function t(n){return r[n]??null}let i=(n)=>{let e=typeof n;switch(e){case"number":return Number.isNaN(n)?"NaN":"number";case"object":{if(Array.isArray(n))return"array";if(n===null)return"null";if(Object.getPrototypeOf(n)!==Object.prototype&&n.constructor)return n.constructor.name}}return e},o={regex:"קלט",email:"כתובת אימייל",url:"כתובת רשת",emoji:"אימוג'י",uuid:"UUID",uuidv4:"UUIDv4",uuidv6:"UUIDv6",nanoid:"nanoid",guid:"GUID",cuid:"cuid",cuid2:"cuid2",ulid:"ULID",xid:"XID",ksuid:"KSUID",datetime:"תאריך וזמן ISO",date:"תאריך ISO",time:"זמן ISO",duration:"משך זמן ISO",ipv4:"כתובת IPv4",ipv6:"כתובת IPv6",cidrv4:"טווח IPv4",cidrv6:"טווח IPv6",base64:"מחרוזת בבסיס 64",base64url:"מחרוזת בבסיס 64 לכתובות רשת",json_string:"מחרוזת JSON",e164:"מספר E.164",jwt:"JWT",template_literal:"קלט"};return(n)=>{switch(n.code){case"invalid_type":return`קלט לא תקין: צריך ${n.expected}, התקבל ${i(n.input)}`;case"invalid_value":if(n.values.length===1)return`קלט לא תקין: צריך ${j(n.values[0])}`;return`קלט לא תקין: צריך אחת מהאפשרויות  ${x(n.values,"|")}`;case"too_big":{let e=n.inclusive?"<=":"<",l=t(n.origin);if(l)return`גדול מדי: ${n.origin??"value"} צריך להיות ${e}${n.maximum.toString()} ${l.unit??"elements"}`;return`גדול מדי: ${n.origin??"value"} צריך להיות ${e}${n.maximum.toString()}`}case"too_small":{let e=n.inclusive?">=":">",l=t(n.origin);if(l)return`קטן מדי: ${n.origin} צריך להיות ${e}${n.minimum.toString()} ${l.unit}`;return`קטן מדי: ${n.origin} צריך להיות ${e}${n.minimum.toString()}`}case"invalid_format":{let e=n;if(e.format==="starts_with")return`מחרוזת לא תקינה: חייבת להתחיל ב"${e.prefix}"`;if(e.format==="ends_with")return`מחרוזת לא תקינה: חייבת להסתיים ב "${e.suffix}"`;if(e.format==="includes")return`מחרוזת לא תקינה: חייבת לכלול "${e.includes}"`;if(e.format==="regex")return`מחרוזת לא תקינה: חייבת להתאים לתבנית ${e.pattern}`;return`${o[e.format]??n.format} לא תקין`}case"not_multiple_of":return`מספר לא תקין: חייב להיות מכפלה של ${n.divisor}`;case"unrecognized_keys":return`מפתח${n.keys.length>1?"ות":""} לא מזוה${n.keys.length>1?"ים":"ה"}: ${x(n.keys,", ")}`;case"invalid_key":return`מפתח לא תקין ב${n.origin}`;case"invalid_union":return"קלט לא תקין";case"invalid_element":return`ערך לא תקין ב${n.origin}`;default:return"קלט לא תקין"}}};function hm(){return{localeError:xI()}}var wI=()=>{let r={string:{unit:"karakter",verb:"legyen"},file:{unit:"byte",verb:"legyen"},array:{unit:"elem",verb:"legyen"},set:{unit:"elem",verb:"legyen"}};function t(n){return r[n]??null}let i=(n)=>{let e=typeof n;switch(e){case"number":return Number.isNaN(n)?"NaN":"szám";case"object":{if(Array.isArray(n))return"tömb";if(n===null)return"null";if(Object.getPrototypeOf(n)!==Object.prototype&&n.constructor)return n.constructor.name}}return e},o={regex:"bemenet",email:"email cím",url:"URL",emoji:"emoji",uuid:"UUID",uuidv4:"UUIDv4",uuidv6:"UUIDv6",nanoid:"nanoid",guid:"GUID",cuid:"cuid",cuid2:"cuid2",ulid:"ULID",xid:"XID",ksuid:"KSUID",datetime:"ISO időbélyeg",date:"ISO dátum",time:"ISO idő",duration:"ISO időintervallum",ipv4:"IPv4 cím",ipv6:"IPv6 cím",cidrv4:"IPv4 tartomány",cidrv6:"IPv6 tartomány",base64:"base64-kódolt string",base64url:"base64url-kódolt string",json_string:"JSON string",e164:"E.164 szám",jwt:"JWT",template_literal:"bemenet"};return(n)=>{switch(n.code){case"invalid_type":return`Érvénytelen bemenet: a várt érték ${n.expected}, a kapott érték ${i(n.input)}`;case"invalid_value":if(n.values.length===1)return`Érvénytelen bemenet: a várt érték ${j(n.values[0])}`;return`Érvénytelen opció: valamelyik érték várt ${x(n.values,"|")}`;case"too_big":{let e=n.inclusive?"<=":"<",l=t(n.origin);if(l)return`Túl nagy: ${n.origin??"érték"} mérete túl nagy ${e}${n.maximum.toString()} ${l.unit??"elem"}`;return`Túl nagy: a bemeneti érték ${n.origin??"érték"} túl nagy: ${e}${n.maximum.toString()}`}case"too_small":{let e=n.inclusive?">=":">",l=t(n.origin);if(l)return`Túl kicsi: a bemeneti érték ${n.origin} mérete túl kicsi ${e}${n.minimum.toString()} ${l.unit}`;return`Túl kicsi: a bemeneti érték ${n.origin} túl kicsi ${e}${n.minimum.toString()}`}case"invalid_format":{let e=n;if(e.format==="starts_with")return`Érvénytelen string: "${e.prefix}" értékkel kell kezdődnie`;if(e.format==="ends_with")return`Érvénytelen string: "${e.suffix}" értékkel kell végződnie`;if(e.format==="includes")return`Érvénytelen string: "${e.includes}" értéket kell tartalmaznia`;if(e.format==="regex")return`Érvénytelen string: ${e.pattern} mintának kell megfelelnie`;return`Érvénytelen ${o[e.format]??n.format}`}case"not_multiple_of":return`Érvénytelen szám: ${n.divisor} többszörösének kell lennie`;case"unrecognized_keys":return`Ismeretlen kulcs${n.keys.length>1?"s":""}: ${x(n.keys,", ")}`;case"invalid_key":return`Érvénytelen kulcs ${n.origin}`;case"invalid_union":return"Érvénytelen bemenet";case"invalid_element":return`Érvénytelen érték: ${n.origin}`;default:return"Érvénytelen bemenet"}}};function $m(){return{localeError:wI()}}var fI=()=>{let r={string:{unit:"karakter",verb:"memiliki"},file:{unit:"byte",verb:"memiliki"},array:{unit:"item",verb:"memiliki"},set:{unit:"item",verb:"memiliki"}};function t(n){return r[n]??null}let i=(n)=>{let e=typeof n;switch(e){case"number":return Number.isNaN(n)?"NaN":"number";case"object":{if(Array.isArray(n))return"array";if(n===null)return"null";if(Object.getPrototypeOf(n)!==Object.prototype&&n.constructor)return n.constructor.name}}return e},o={regex:"input",email:"alamat email",url:"URL",emoji:"emoji",uuid:"UUID",uuidv4:"UUIDv4",uuidv6:"UUIDv6",nanoid:"nanoid",guid:"GUID",cuid:"cuid",cuid2:"cuid2",ulid:"ULID",xid:"XID",ksuid:"KSUID",datetime:"tanggal dan waktu format ISO",date:"tanggal format ISO",time:"jam format ISO",duration:"durasi format ISO",ipv4:"alamat IPv4",ipv6:"alamat IPv6",cidrv4:"rentang alamat IPv4",cidrv6:"rentang alamat IPv6",base64:"string dengan enkode base64",base64url:"string dengan enkode base64url",json_string:"string JSON",e164:"angka E.164",jwt:"JWT",template_literal:"input"};return(n)=>{switch(n.code){case"invalid_type":return`Input tidak valid: diharapkan ${n.expected}, diterima ${i(n.input)}`;case"invalid_value":if(n.values.length===1)return`Input tidak valid: diharapkan ${j(n.values[0])}`;return`Pilihan tidak valid: diharapkan salah satu dari ${x(n.values,"|")}`;case"too_big":{let e=n.inclusive?"<=":"<",l=t(n.origin);if(l)return`Terlalu besar: diharapkan ${n.origin??"value"} memiliki ${e}${n.maximum.toString()} ${l.unit??"elemen"}`;return`Terlalu besar: diharapkan ${n.origin??"value"} menjadi ${e}${n.maximum.toString()}`}case"too_small":{let e=n.inclusive?">=":">",l=t(n.origin);if(l)return`Terlalu kecil: diharapkan ${n.origin} memiliki ${e}${n.minimum.toString()} ${l.unit}`;return`Terlalu kecil: diharapkan ${n.origin} menjadi ${e}${n.minimum.toString()}`}case"invalid_format":{let e=n;if(e.format==="starts_with")return`String tidak valid: harus dimulai dengan "${e.prefix}"`;if(e.format==="ends_with")return`String tidak valid: harus berakhir dengan "${e.suffix}"`;if(e.format==="includes")return`String tidak valid: harus menyertakan "${e.includes}"`;if(e.format==="regex")return`String tidak valid: harus sesuai pola ${e.pattern}`;return`${o[e.format]??n.format} tidak valid`}case"not_multiple_of":return`Angka tidak valid: harus kelipatan dari ${n.divisor}`;case"unrecognized_keys":return`Kunci tidak dikenali ${n.keys.length>1?"s":""}: ${x(n.keys,", ")}`;case"invalid_key":return`Kunci tidak valid di ${n.origin}`;case"invalid_union":return"Input tidak valid";case"invalid_element":return`Nilai tidak valid di ${n.origin}`;default:return"Input tidak valid"}}};function xm(){return{localeError:fI()}}var zI=(r)=>{let t=typeof r;switch(t){case"number":return Number.isNaN(r)?"NaN":"númer";case"object":{if(Array.isArray(r))return"fylki";if(r===null)return"null";if(Object.getPrototypeOf(r)!==Object.prototype&&r.constructor)return r.constructor.name}}return t},DI=()=>{let r={string:{unit:"stafi",verb:"að hafa"},file:{unit:"bæti",verb:"að hafa"},array:{unit:"hluti",verb:"að hafa"},set:{unit:"hluti",verb:"að hafa"}};function t(o){return r[o]??null}let i={regex:"gildi",email:"netfang",url:"vefslóð",emoji:"emoji",uuid:"UUID",uuidv4:"UUIDv4",uuidv6:"UUIDv6",nanoid:"nanoid",guid:"GUID",cuid:"cuid",cuid2:"cuid2",ulid:"ULID",xid:"XID",ksuid:"KSUID",datetime:"ISO dagsetning og tími",date:"ISO dagsetning",time:"ISO tími",duration:"ISO tímalengd",ipv4:"IPv4 address",ipv6:"IPv6 address",cidrv4:"IPv4 range",cidrv6:"IPv6 range",base64:"base64-encoded strengur",base64url:"base64url-encoded strengur",json_string:"JSON strengur",e164:"E.164 tölugildi",jwt:"JWT",template_literal:"gildi"};return(o)=>{switch(o.code){case"invalid_type":return`Rangt gildi: Þú slóst inn ${zI(o.input)} þar sem á að vera ${o.expected}`;case"invalid_value":if(o.values.length===1)return`Rangt gildi: gert ráð fyrir ${j(o.values[0])}`;return`Ógilt val: má vera eitt af eftirfarandi ${x(o.values,"|")}`;case"too_big":{let n=o.inclusive?"<=":"<",e=t(o.origin);if(e)return`Of stórt: gert er ráð fyrir að ${o.origin??"gildi"} hafi ${n}${o.maximum.toString()} ${e.unit??"hluti"}`;return`Of stórt: gert er ráð fyrir að ${o.origin??"gildi"} sé ${n}${o.maximum.toString()}`}case"too_small":{let n=o.inclusive?">=":">",e=t(o.origin);if(e)return`Of lítið: gert er ráð fyrir að ${o.origin} hafi ${n}${o.minimum.toString()} ${e.unit}`;return`Of lítið: gert er ráð fyrir að ${o.origin} sé ${n}${o.minimum.toString()}`}case"invalid_format":{let n=o;if(n.format==="starts_with")return`Ógildur strengur: verður að byrja á "${n.prefix}"`;if(n.format==="ends_with")return`Ógildur strengur: verður að enda á "${n.suffix}"`;if(n.format==="includes")return`Ógildur strengur: verður að innihalda "${n.includes}"`;if(n.format==="regex")return`Ógildur strengur: verður að fylgja mynstri ${n.pattern}`;return`Rangt ${i[n.format]??o.format}`}case"not_multiple_of":return`Röng tala: verður að vera margfeldi af ${o.divisor}`;case"unrecognized_keys":return`Óþekkt ${o.keys.length>1?"ir lyklar":"ur lykill"}: ${x(o.keys,", ")}`;case"invalid_key":return`Rangur lykill í ${o.origin}`;case"invalid_union":return"Rangt gildi";case"invalid_element":return`Rangt gildi í ${o.origin}`;default:return"Rangt gildi"}}};function wm(){return{localeError:DI()}}var _I=()=>{let r={string:{unit:"caratteri",verb:"avere"},file:{unit:"byte",verb:"avere"},array:{unit:"elementi",verb:"avere"},set:{unit:"elementi",verb:"avere"}};function t(n){return r[n]??null}let i=(n)=>{let e=typeof n;switch(e){case"number":return Number.isNaN(n)?"NaN":"numero";case"object":{if(Array.isArray(n))return"vettore";if(n===null)return"null";if(Object.getPrototypeOf(n)!==Object.prototype&&n.constructor)return n.constructor.name}}return e},o={regex:"input",email:"indirizzo email",url:"URL",emoji:"emoji",uuid:"UUID",uuidv4:"UUIDv4",uuidv6:"UUIDv6",nanoid:"nanoid",guid:"GUID",cuid:"cuid",cuid2:"cuid2",ulid:"ULID",xid:"XID",ksuid:"KSUID",datetime:"data e ora ISO",date:"data ISO",time:"ora ISO",duration:"durata ISO",ipv4:"indirizzo IPv4",ipv6:"indirizzo IPv6",cidrv4:"intervallo IPv4",cidrv6:"intervallo IPv6",base64:"stringa codificata in base64",base64url:"URL codificata in base64",json_string:"stringa JSON",e164:"numero E.164",jwt:"JWT",template_literal:"input"};return(n)=>{switch(n.code){case"invalid_type":return`Input non valido: atteso ${n.expected}, ricevuto ${i(n.input)}`;case"invalid_value":if(n.values.length===1)return`Input non valido: atteso ${j(n.values[0])}`;return`Opzione non valida: atteso uno tra ${x(n.values,"|")}`;case"too_big":{let e=n.inclusive?"<=":"<",l=t(n.origin);if(l)return`Troppo grande: ${n.origin??"valore"} deve avere ${e}${n.maximum.toString()} ${l.unit??"elementi"}`;return`Troppo grande: ${n.origin??"valore"} deve essere ${e}${n.maximum.toString()}`}case"too_small":{let e=n.inclusive?">=":">",l=t(n.origin);if(l)return`Troppo piccolo: ${n.origin} deve avere ${e}${n.minimum.toString()} ${l.unit}`;return`Troppo piccolo: ${n.origin} deve essere ${e}${n.minimum.toString()}`}case"invalid_format":{let e=n;if(e.format==="starts_with")return`Stringa non valida: deve iniziare con "${e.prefix}"`;if(e.format==="ends_with")return`Stringa non valida: deve terminare con "${e.suffix}"`;if(e.format==="includes")return`Stringa non valida: deve includere "${e.includes}"`;if(e.format==="regex")return`Stringa non valida: deve corrispondere al pattern ${e.pattern}`;return`Invalid ${o[e.format]??n.format}`}case"not_multiple_of":return`Numero non valido: deve essere un multiplo di ${n.divisor}`;case"unrecognized_keys":return`Chiav${n.keys.length>1?"i":"e"} non riconosciut${n.keys.length>1?"e":"a"}: ${x(n.keys,", ")}`;case"invalid_key":return`Chiave non valida in ${n.origin}`;case"invalid_union":return"Input non valido";case"invalid_element":return`Valore non valido in ${n.origin}`;default:return"Input non valido"}}};function fm(){return{localeError:_I()}}var jI=()=>{let r={string:{unit:"文字",verb:"である"},file:{unit:"バイト",verb:"である"},array:{unit:"要素",verb:"である"},set:{unit:"要素",verb:"である"}};function t(n){return r[n]??null}let i=(n)=>{let e=typeof n;switch(e){case"number":return Number.isNaN(n)?"NaN":"数値";case"object":{if(Array.isArray(n))return"配列";if(n===null)return"null";if(Object.getPrototypeOf(n)!==Object.prototype&&n.constructor)return n.constructor.name}}return e},o={regex:"入力値",email:"メールアドレス",url:"URL",emoji:"絵文字",uuid:"UUID",uuidv4:"UUIDv4",uuidv6:"UUIDv6",nanoid:"nanoid",guid:"GUID",cuid:"cuid",cuid2:"cuid2",ulid:"ULID",xid:"XID",ksuid:"KSUID",datetime:"ISO日時",date:"ISO日付",time:"ISO時刻",duration:"ISO期間",ipv4:"IPv4アドレス",ipv6:"IPv6アドレス",cidrv4:"IPv4範囲",cidrv6:"IPv6範囲",base64:"base64エンコード文字列",base64url:"base64urlエンコード文字列",json_string:"JSON文字列",e164:"E.164番号",jwt:"JWT",template_literal:"入力値"};return(n)=>{switch(n.code){case"invalid_type":return`無効な入力: ${n.expected}が期待されましたが、${i(n.input)}が入力されました`;case"invalid_value":if(n.values.length===1)return`無効な入力: ${j(n.values[0])}が期待されました`;return`無効な選択: ${x(n.values,"、")}のいずれかである必要があります`;case"too_big":{let e=n.inclusive?"以下である":"より小さい",l=t(n.origin);if(l)return`大きすぎる値: ${n.origin??"値"}は${n.maximum.toString()}${l.unit??"要素"}${e}必要があります`;return`大きすぎる値: ${n.origin??"値"}は${n.maximum.toString()}${e}必要があります`}case"too_small":{let e=n.inclusive?"以上である":"より大きい",l=t(n.origin);if(l)return`小さすぎる値: ${n.origin}は${n.minimum.toString()}${l.unit}${e}必要があります`;return`小さすぎる値: ${n.origin}は${n.minimum.toString()}${e}必要があります`}case"invalid_format":{let e=n;if(e.format==="starts_with")return`無効な文字列: "${e.prefix}"で始まる必要があります`;if(e.format==="ends_with")return`無効な文字列: "${e.suffix}"で終わる必要があります`;if(e.format==="includes")return`無効な文字列: "${e.includes}"を含む必要があります`;if(e.format==="regex")return`無効な文字列: パターン${e.pattern}に一致する必要があります`;return`無効な${o[e.format]??n.format}`}case"not_multiple_of":return`無効な数値: ${n.divisor}の倍数である必要があります`;case"unrecognized_keys":return`認識されていないキー${n.keys.length>1?"群":""}: ${x(n.keys,"、")}`;case"invalid_key":return`${n.origin}内の無効なキー`;case"invalid_union":return"無効な入力";case"invalid_element":return`${n.origin}内の無効な値`;default:return"無効な入力"}}};function zm(){return{localeError:jI()}}var OI=()=>{let r={string:{unit:"តួអក្សរ",verb:"គួរមាន"},file:{unit:"បៃ",verb:"គួរមាន"},array:{unit:"ធាតុ",verb:"គួរមាន"},set:{unit:"ធាតុ",verb:"គួរមាន"}};function t(n){return r[n]??null}let i=(n)=>{let e=typeof n;switch(e){case"number":return Number.isNaN(n)?"មិនមែនជាលេខ (NaN)":"លេខ";case"object":{if(Array.isArray(n))return"អារេ (Array)";if(n===null)return"គ្មានតម្លៃ (null)";if(Object.getPrototypeOf(n)!==Object.prototype&&n.constructor)return n.constructor.name}}return e},o={regex:"ទិន្នន័យបញ្ចូល",email:"អាសយដ្ឋានអ៊ីមែល",url:"URL",emoji:"សញ្ញាអារម្មណ៍",uuid:"UUID",uuidv4:"UUIDv4",uuidv6:"UUIDv6",nanoid:"nanoid",guid:"GUID",cuid:"cuid",cuid2:"cuid2",ulid:"ULID",xid:"XID",ksuid:"KSUID",datetime:"កាលបរិច្ឆេទ និងម៉ោង ISO",date:"កាលបរិច្ឆេទ ISO",time:"ម៉ោង ISO",duration:"រយៈពេល ISO",ipv4:"អាសយដ្ឋាន IPv4",ipv6:"អាសយដ្ឋាន IPv6",cidrv4:"ដែនអាសយដ្ឋាន IPv4",cidrv6:"ដែនអាសយដ្ឋាន IPv6",base64:"ខ្សែអក្សរអ៊ិកូដ base64",base64url:"ខ្សែអក្សរអ៊ិកូដ base64url",json_string:"ខ្សែអក្សរ JSON",e164:"លេខ E.164",jwt:"JWT",template_literal:"ទិន្នន័យបញ្ចូល"};return(n)=>{switch(n.code){case"invalid_type":return`ទិន្នន័យបញ្ចូលមិនត្រឹមត្រូវ៖ ត្រូវការ ${n.expected} ប៉ុន្តែទទួលបាន ${i(n.input)}`;case"invalid_value":if(n.values.length===1)return`ទិន្នន័យបញ្ចូលមិនត្រឹមត្រូវ៖ ត្រូវការ ${j(n.values[0])}`;return`ជម្រើសមិនត្រឹមត្រូវ៖ ត្រូវជាមួយក្នុងចំណោម ${x(n.values,"|")}`;case"too_big":{let e=n.inclusive?"<=":"<",l=t(n.origin);if(l)return`ធំពេក៖ ត្រូវការ ${n.origin??"តម្លៃ"} ${e} ${n.maximum.toString()} ${l.unit??"ធាតុ"}`;return`ធំពេក៖ ត្រូវការ ${n.origin??"តម្លៃ"} ${e} ${n.maximum.toString()}`}case"too_small":{let e=n.inclusive?">=":">",l=t(n.origin);if(l)return`តូចពេក៖ ត្រូវការ ${n.origin} ${e} ${n.minimum.toString()} ${l.unit}`;return`តូចពេក៖ ត្រូវការ ${n.origin} ${e} ${n.minimum.toString()}`}case"invalid_format":{let e=n;if(e.format==="starts_with")return`ខ្សែអក្សរមិនត្រឹមត្រូវ៖ ត្រូវចាប់ផ្តើមដោយ "${e.prefix}"`;if(e.format==="ends_with")return`ខ្សែអក្សរមិនត្រឹមត្រូវ៖ ត្រូវបញ្ចប់ដោយ "${e.suffix}"`;if(e.format==="includes")return`ខ្សែអក្សរមិនត្រឹមត្រូវ៖ ត្រូវមាន "${e.includes}"`;if(e.format==="regex")return`ខ្សែអក្សរមិនត្រឹមត្រូវ៖ ត្រូវតែផ្គូផ្គងនឹងទម្រង់ដែលបានកំណត់ ${e.pattern}`;return`មិនត្រឹមត្រូវ៖ ${o[e.format]??n.format}`}case"not_multiple_of":return`លេខមិនត្រឹមត្រូវ៖ ត្រូវតែជាពហុគុណនៃ ${n.divisor}`;case"unrecognized_keys":return`រកឃើញសោមិនស្គាល់៖ ${x(n.keys,", ")}`;case"invalid_key":return`សោមិនត្រឹមត្រូវនៅក្នុង ${n.origin}`;case"invalid_union":return"ទិន្នន័យមិនត្រឹមត្រូវ";case"invalid_element":return`ទិន្នន័យមិនត្រឹមត្រូវនៅក្នុង ${n.origin}`;default:return"ទិន្នន័យមិនត្រឹមត្រូវ"}}};function Dm(){return{localeError:OI()}}var pI=()=>{let r={string:{unit:"문자",verb:"to have"},file:{unit:"바이트",verb:"to have"},array:{unit:"개",verb:"to have"},set:{unit:"개",verb:"to have"}};function t(n){return r[n]??null}let i=(n)=>{let e=typeof n;switch(e){case"number":return Number.isNaN(n)?"NaN":"number";case"object":{if(Array.isArray(n))return"array";if(n===null)return"null";if(Object.getPrototypeOf(n)!==Object.prototype&&n.constructor)return n.constructor.name}}return e},o={regex:"입력",email:"이메일 주소",url:"URL",emoji:"이모지",uuid:"UUID",uuidv4:"UUIDv4",uuidv6:"UUIDv6",nanoid:"nanoid",guid:"GUID",cuid:"cuid",cuid2:"cuid2",ulid:"ULID",xid:"XID",ksuid:"KSUID",datetime:"ISO 날짜시간",date:"ISO 날짜",time:"ISO 시간",duration:"ISO 기간",ipv4:"IPv4 주소",ipv6:"IPv6 주소",cidrv4:"IPv4 범위",cidrv6:"IPv6 범위",base64:"base64 인코딩 문자열",base64url:"base64url 인코딩 문자열",json_string:"JSON 문자열",e164:"E.164 번호",jwt:"JWT",template_literal:"입력"};return(n)=>{switch(n.code){case"invalid_type":return`잘못된 입력: 예상 타입은 ${n.expected}, 받은 타입은 ${i(n.input)}입니다`;case"invalid_value":if(n.values.length===1)return`잘못된 입력: 값은 ${j(n.values[0])} 이어야 합니다`;return`잘못된 옵션: ${x(n.values,"또는 ")} 중 하나여야 합니다`;case"too_big":{let e=n.inclusive?"이하":"미만",l=e==="미만"?"이어야 합니다":"여야 합니다",u=t(n.origin),g=u?.unit??"요소";if(u)return`${n.origin??"값"}이 너무 큽니다: ${n.maximum.toString()}${g} ${e}${l}`;return`${n.origin??"값"}이 너무 큽니다: ${n.maximum.toString()} ${e}${l}`}case"too_small":{let e=n.inclusive?"이상":"초과",l=e==="이상"?"이어야 합니다":"여야 합니다",u=t(n.origin),g=u?.unit??"요소";if(u)return`${n.origin??"값"}이 너무 작습니다: ${n.minimum.toString()}${g} ${e}${l}`;return`${n.origin??"값"}이 너무 작습니다: ${n.minimum.toString()} ${e}${l}`}case"invalid_format":{let e=n;if(e.format==="starts_with")return`잘못된 문자열: "${e.prefix}"(으)로 시작해야 합니다`;if(e.format==="ends_with")return`잘못된 문자열: "${e.suffix}"(으)로 끝나야 합니다`;if(e.format==="includes")return`잘못된 문자열: "${e.includes}"을(를) 포함해야 합니다`;if(e.format==="regex")return`잘못된 문자열: 정규식 ${e.pattern} 패턴과 일치해야 합니다`;return`잘못된 ${o[e.format]??n.format}`}case"not_multiple_of":return`잘못된 숫자: ${n.divisor}의 배수여야 합니다`;case"unrecognized_keys":return`인식할 수 없는 키: ${x(n.keys,", ")}`;case"invalid_key":return`잘못된 키: ${n.origin}`;case"invalid_union":return"잘못된 입력";case"invalid_element":return`잘못된 값: ${n.origin}`;default:return"잘못된 입력"}}};function _m(){return{localeError:pI()}}var II=()=>{let r={string:{unit:"знаци",verb:"да имаат"},file:{unit:"бајти",verb:"да имаат"},array:{unit:"ставки",verb:"да имаат"},set:{unit:"ставки",verb:"да имаат"}};function t(n){return r[n]??null}let i=(n)=>{let e=typeof n;switch(e){case"number":return Number.isNaN(n)?"NaN":"број";case"object":{if(Array.isArray(n))return"низа";if(n===null)return"null";if(Object.getPrototypeOf(n)!==Object.prototype&&n.constructor)return n.constructor.name}}return e},o={regex:"внес",email:"адреса на е-пошта",url:"URL",emoji:"емоџи",uuid:"UUID",uuidv4:"UUIDv4",uuidv6:"UUIDv6",nanoid:"nanoid",guid:"GUID",cuid:"cuid",cuid2:"cuid2",ulid:"ULID",xid:"XID",ksuid:"KSUID",datetime:"ISO датум и време",date:"ISO датум",time:"ISO време",duration:"ISO времетраење",ipv4:"IPv4 адреса",ipv6:"IPv6 адреса",cidrv4:"IPv4 опсег",cidrv6:"IPv6 опсег",base64:"base64-енкодирана низа",base64url:"base64url-енкодирана низа",json_string:"JSON низа",e164:"E.164 број",jwt:"JWT",template_literal:"внес"};return(n)=>{switch(n.code){case"invalid_type":return`Грешен внес: се очекува ${n.expected}, примено ${i(n.input)}`;case"invalid_value":if(n.values.length===1)return`Invalid input: expected ${j(n.values[0])}`;return`Грешана опција: се очекува една ${x(n.values,"|")}`;case"too_big":{let e=n.inclusive?"<=":"<",l=t(n.origin);if(l)return`Премногу голем: се очекува ${n.origin??"вредноста"} да има ${e}${n.maximum.toString()} ${l.unit??"елементи"}`;return`Премногу голем: се очекува ${n.origin??"вредноста"} да биде ${e}${n.maximum.toString()}`}case"too_small":{let e=n.inclusive?">=":">",l=t(n.origin);if(l)return`Премногу мал: се очекува ${n.origin} да има ${e}${n.minimum.toString()} ${l.unit}`;return`Премногу мал: се очекува ${n.origin} да биде ${e}${n.minimum.toString()}`}case"invalid_format":{let e=n;if(e.format==="starts_with")return`Неважечка низа: мора да започнува со "${e.prefix}"`;if(e.format==="ends_with")return`Неважечка низа: мора да завршува со "${e.suffix}"`;if(e.format==="includes")return`Неважечка низа: мора да вклучува "${e.includes}"`;if(e.format==="regex")return`Неважечка низа: мора да одгоара на патернот ${e.pattern}`;return`Invalid ${o[e.format]??n.format}`}case"not_multiple_of":return`Грешен број: мора да биде делив со ${n.divisor}`;case"unrecognized_keys":return`${n.keys.length>1?"Непрепознаени клучеви":"Непрепознаен клуч"}: ${x(n.keys,", ")}`;case"invalid_key":return`Грешен клуч во ${n.origin}`;case"invalid_union":return"Грешен внес";case"invalid_element":return`Грешна вредност во ${n.origin}`;default:return"Грешен внес"}}};function jm(){return{localeError:II()}}var aI=()=>{let r={string:{unit:"aksara",verb:"mempunyai"},file:{unit:"bait",verb:"mempunyai"},array:{unit:"elemen",verb:"mempunyai"},set:{unit:"elemen",verb:"mempunyai"}};function t(n){return r[n]??null}let i=(n)=>{let e=typeof n;switch(e){case"number":return Number.isNaN(n)?"NaN":"nombor";case"object":{if(Array.isArray(n))return"array";if(n===null)return"null";if(Object.getPrototypeOf(n)!==Object.prototype&&n.constructor)return n.constructor.name}}return e},o={regex:"input",email:"alamat e-mel",url:"URL",emoji:"emoji",uuid:"UUID",uuidv4:"UUIDv4",uuidv6:"UUIDv6",nanoid:"nanoid",guid:"GUID",cuid:"cuid",cuid2:"cuid2",ulid:"ULID",xid:"XID",ksuid:"KSUID",datetime:"tarikh masa ISO",date:"tarikh ISO",time:"masa ISO",duration:"tempoh ISO",ipv4:"alamat IPv4",ipv6:"alamat IPv6",cidrv4:"julat IPv4",cidrv6:"julat IPv6",base64:"string dikodkan base64",base64url:"string dikodkan base64url",json_string:"string JSON",e164:"nombor E.164",jwt:"JWT",template_literal:"input"};return(n)=>{switch(n.code){case"invalid_type":return`Input tidak sah: dijangka ${n.expected}, diterima ${i(n.input)}`;case"invalid_value":if(n.values.length===1)return`Input tidak sah: dijangka ${j(n.values[0])}`;return`Pilihan tidak sah: dijangka salah satu daripada ${x(n.values,"|")}`;case"too_big":{let e=n.inclusive?"<=":"<",l=t(n.origin);if(l)return`Terlalu besar: dijangka ${n.origin??"nilai"} ${l.verb} ${e}${n.maximum.toString()} ${l.unit??"elemen"}`;return`Terlalu besar: dijangka ${n.origin??"nilai"} adalah ${e}${n.maximum.toString()}`}case"too_small":{let e=n.inclusive?">=":">",l=t(n.origin);if(l)return`Terlalu kecil: dijangka ${n.origin} ${l.verb} ${e}${n.minimum.toString()} ${l.unit}`;return`Terlalu kecil: dijangka ${n.origin} adalah ${e}${n.minimum.toString()}`}case"invalid_format":{let e=n;if(e.format==="starts_with")return`String tidak sah: mesti bermula dengan "${e.prefix}"`;if(e.format==="ends_with")return`String tidak sah: mesti berakhir dengan "${e.suffix}"`;if(e.format==="includes")return`String tidak sah: mesti mengandungi "${e.includes}"`;if(e.format==="regex")return`String tidak sah: mesti sepadan dengan corak ${e.pattern}`;return`${o[e.format]??n.format} tidak sah`}case"not_multiple_of":return`Nombor tidak sah: perlu gandaan ${n.divisor}`;case"unrecognized_keys":return`Kunci tidak dikenali: ${x(n.keys,", ")}`;case"invalid_key":return`Kunci tidak sah dalam ${n.origin}`;case"invalid_union":return"Input tidak sah";case"invalid_element":return`Nilai tidak sah dalam ${n.origin}`;default:return"Input tidak sah"}}};function Om(){return{localeError:aI()}}var UI=()=>{let r={string:{unit:"tekens"},file:{unit:"bytes"},array:{unit:"elementen"},set:{unit:"elementen"}};function t(n){return r[n]??null}let i=(n)=>{let e=typeof n;switch(e){case"number":return Number.isNaN(n)?"NaN":"getal";case"object":{if(Array.isArray(n))return"array";if(n===null)return"null";if(Object.getPrototypeOf(n)!==Object.prototype&&n.constructor)return n.constructor.name}}return e},o={regex:"invoer",email:"emailadres",url:"URL",emoji:"emoji",uuid:"UUID",uuidv4:"UUIDv4",uuidv6:"UUIDv6",nanoid:"nanoid",guid:"GUID",cuid:"cuid",cuid2:"cuid2",ulid:"ULID",xid:"XID",ksuid:"KSUID",datetime:"ISO datum en tijd",date:"ISO datum",time:"ISO tijd",duration:"ISO duur",ipv4:"IPv4-adres",ipv6:"IPv6-adres",cidrv4:"IPv4-bereik",cidrv6:"IPv6-bereik",base64:"base64-gecodeerde tekst",base64url:"base64 URL-gecodeerde tekst",json_string:"JSON string",e164:"E.164-nummer",jwt:"JWT",template_literal:"invoer"};return(n)=>{switch(n.code){case"invalid_type":return`Ongeldige invoer: verwacht ${n.expected}, ontving ${i(n.input)}`;case"invalid_value":if(n.values.length===1)return`Ongeldige invoer: verwacht ${j(n.values[0])}`;return`Ongeldige optie: verwacht één van ${x(n.values,"|")}`;case"too_big":{let e=n.inclusive?"<=":"<",l=t(n.origin);if(l)return`Te lang: verwacht dat ${n.origin??"waarde"} ${e}${n.maximum.toString()} ${l.unit??"elementen"} bevat`;return`Te lang: verwacht dat ${n.origin??"waarde"} ${e}${n.maximum.toString()} is`}case"too_small":{let e=n.inclusive?">=":">",l=t(n.origin);if(l)return`Te kort: verwacht dat ${n.origin} ${e}${n.minimum.toString()} ${l.unit} bevat`;return`Te kort: verwacht dat ${n.origin} ${e}${n.minimum.toString()} is`}case"invalid_format":{let e=n;if(e.format==="starts_with")return`Ongeldige tekst: moet met "${e.prefix}" beginnen`;if(e.format==="ends_with")return`Ongeldige tekst: moet op "${e.suffix}" eindigen`;if(e.format==="includes")return`Ongeldige tekst: moet "${e.includes}" bevatten`;if(e.format==="regex")return`Ongeldige tekst: moet overeenkomen met patroon ${e.pattern}`;return`Ongeldig: ${o[e.format]??n.format}`}case"not_multiple_of":return`Ongeldig getal: moet een veelvoud van ${n.divisor} zijn`;case"unrecognized_keys":return`Onbekende key${n.keys.length>1?"s":""}: ${x(n.keys,", ")}`;case"invalid_key":return`Ongeldige key in ${n.origin}`;case"invalid_union":return"Ongeldige invoer";case"invalid_element":return`Ongeldige waarde in ${n.origin}`;default:return"Ongeldige invoer"}}};function pm(){return{localeError:UI()}}var kI=()=>{let r={string:{unit:"tegn",verb:"å ha"},file:{unit:"bytes",verb:"å ha"},array:{unit:"elementer",verb:"å inneholde"},set:{unit:"elementer",verb:"å inneholde"}};function t(n){return r[n]??null}let i=(n)=>{let e=typeof n;switch(e){case"number":return Number.isNaN(n)?"NaN":"tall";case"object":{if(Array.isArray(n))return"liste";if(n===null)return"null";if(Object.getPrototypeOf(n)!==Object.prototype&&n.constructor)return n.constructor.name}}return e},o={regex:"input",email:"e-postadresse",url:"URL",emoji:"emoji",uuid:"UUID",uuidv4:"UUIDv4",uuidv6:"UUIDv6",nanoid:"nanoid",guid:"GUID",cuid:"cuid",cuid2:"cuid2",ulid:"ULID",xid:"XID",ksuid:"KSUID",datetime:"ISO dato- og klokkeslett",date:"ISO-dato",time:"ISO-klokkeslett",duration:"ISO-varighet",ipv4:"IPv4-område",ipv6:"IPv6-område",cidrv4:"IPv4-spekter",cidrv6:"IPv6-spekter",base64:"base64-enkodet streng",base64url:"base64url-enkodet streng",json_string:"JSON-streng",e164:"E.164-nummer",jwt:"JWT",template_literal:"input"};return(n)=>{switch(n.code){case"invalid_type":return`Ugyldig input: forventet ${n.expected}, fikk ${i(n.input)}`;case"invalid_value":if(n.values.length===1)return`Ugyldig verdi: forventet ${j(n.values[0])}`;return`Ugyldig valg: forventet en av ${x(n.values,"|")}`;case"too_big":{let e=n.inclusive?"<=":"<",l=t(n.origin);if(l)return`For stor(t): forventet ${n.origin??"value"} til å ha ${e}${n.maximum.toString()} ${l.unit??"elementer"}`;return`For stor(t): forventet ${n.origin??"value"} til å ha ${e}${n.maximum.toString()}`}case"too_small":{let e=n.inclusive?">=":">",l=t(n.origin);if(l)return`For lite(n): forventet ${n.origin} til å ha ${e}${n.minimum.toString()} ${l.unit}`;return`For lite(n): forventet ${n.origin} til å ha ${e}${n.minimum.toString()}`}case"invalid_format":{let e=n;if(e.format==="starts_with")return`Ugyldig streng: må starte med "${e.prefix}"`;if(e.format==="ends_with")return`Ugyldig streng: må ende med "${e.suffix}"`;if(e.format==="includes")return`Ugyldig streng: må inneholde "${e.includes}"`;if(e.format==="regex")return`Ugyldig streng: må matche mønsteret ${e.pattern}`;return`Ugyldig ${o[e.format]??n.format}`}case"not_multiple_of":return`Ugyldig tall: må være et multiplum av ${n.divisor}`;case"unrecognized_keys":return`${n.keys.length>1?"Ukjente nøkler":"Ukjent nøkkel"}: ${x(n.keys,", ")}`;case"invalid_key":return`Ugyldig nøkkel i ${n.origin}`;case"invalid_union":return"Ugyldig input";case"invalid_element":return`Ugyldig verdi i ${n.origin}`;default:return"Ugyldig input"}}};function Im(){return{localeError:kI()}}var JI=()=>{let r={string:{unit:"harf",verb:"olmalıdır"},file:{unit:"bayt",verb:"olmalıdır"},array:{unit:"unsur",verb:"olmalıdır"},set:{unit:"unsur",verb:"olmalıdır"}};function t(n){return r[n]??null}let i=(n)=>{let e=typeof n;switch(e){case"number":return Number.isNaN(n)?"NaN":"numara";case"object":{if(Array.isArray(n))return"saf";if(n===null)return"gayb";if(Object.getPrototypeOf(n)!==Object.prototype&&n.constructor)return n.constructor.name}}return e},o={regex:"giren",email:"epostagâh",url:"URL",emoji:"emoji",uuid:"UUID",uuidv4:"UUIDv4",uuidv6:"UUIDv6",nanoid:"nanoid",guid:"GUID",cuid:"cuid",cuid2:"cuid2",ulid:"ULID",xid:"XID",ksuid:"KSUID",datetime:"ISO hengâmı",date:"ISO tarihi",time:"ISO zamanı",duration:"ISO müddeti",ipv4:"IPv4 nişânı",ipv6:"IPv6 nişânı",cidrv4:"IPv4 menzili",cidrv6:"IPv6 menzili",base64:"base64-şifreli metin",base64url:"base64url-şifreli metin",json_string:"JSON metin",e164:"E.164 sayısı",jwt:"JWT",template_literal:"giren"};return(n)=>{switch(n.code){case"invalid_type":return`Fâsit giren: umulan ${n.expected}, alınan ${i(n.input)}`;case"invalid_value":if(n.values.length===1)return`Fâsit giren: umulan ${j(n.values[0])}`;return`Fâsit tercih: mûteberler ${x(n.values,"|")}`;case"too_big":{let e=n.inclusive?"<=":"<",l=t(n.origin);if(l)return`Fazla büyük: ${n.origin??"value"}, ${e}${n.maximum.toString()} ${l.unit??"elements"} sahip olmalıydı.`;return`Fazla büyük: ${n.origin??"value"}, ${e}${n.maximum.toString()} olmalıydı.`}case"too_small":{let e=n.inclusive?">=":">",l=t(n.origin);if(l)return`Fazla küçük: ${n.origin}, ${e}${n.minimum.toString()} ${l.unit} sahip olmalıydı.`;return`Fazla küçük: ${n.origin}, ${e}${n.minimum.toString()} olmalıydı.`}case"invalid_format":{let e=n;if(e.format==="starts_with")return`Fâsit metin: "${e.prefix}" ile başlamalı.`;if(e.format==="ends_with")return`Fâsit metin: "${e.suffix}" ile bitmeli.`;if(e.format==="includes")return`Fâsit metin: "${e.includes}" ihtivâ etmeli.`;if(e.format==="regex")return`Fâsit metin: ${e.pattern} nakşına uymalı.`;return`Fâsit ${o[e.format]??n.format}`}case"not_multiple_of":return`Fâsit sayı: ${n.divisor} katı olmalıydı.`;case"unrecognized_keys":return`Tanınmayan anahtar ${n.keys.length>1?"s":""}: ${x(n.keys,", ")}`;case"invalid_key":return`${n.origin} için tanınmayan anahtar var.`;case"invalid_union":return"Giren tanınamadı.";case"invalid_element":return`${n.origin} için tanınmayan kıymet var.`;default:return"Kıymet tanınamadı."}}};function am(){return{localeError:JI()}}var XI=()=>{let r={string:{unit:"توکي",verb:"ولري"},file:{unit:"بایټس",verb:"ولري"},array:{unit:"توکي",verb:"ولري"},set:{unit:"توکي",verb:"ولري"}};function t(n){return r[n]??null}let i=(n)=>{let e=typeof n;switch(e){case"number":return Number.isNaN(n)?"NaN":"عدد";case"object":{if(Array.isArray(n))return"ارې";if(n===null)return"null";if(Object.getPrototypeOf(n)!==Object.prototype&&n.constructor)return n.constructor.name}}return e},o={regex:"ورودي",email:"بریښنالیک",url:"یو آر ال",emoji:"ایموجي",uuid:"UUID",uuidv4:"UUIDv4",uuidv6:"UUIDv6",nanoid:"nanoid",guid:"GUID",cuid:"cuid",cuid2:"cuid2",ulid:"ULID",xid:"XID",ksuid:"KSUID",datetime:"نیټه او وخت",date:"نېټه",time:"وخت",duration:"موده",ipv4:"د IPv4 پته",ipv6:"د IPv6 پته",cidrv4:"د IPv4 ساحه",cidrv6:"د IPv6 ساحه",base64:"base64-encoded متن",base64url:"base64url-encoded متن",json_string:"JSON متن",e164:"د E.164 شمېره",jwt:"JWT",template_literal:"ورودي"};return(n)=>{switch(n.code){case"invalid_type":return`ناسم ورودي: باید ${n.expected} وای, مګر ${i(n.input)} ترلاسه شو`;case"invalid_value":if(n.values.length===1)return`ناسم ورودي: باید ${j(n.values[0])} وای`;return`ناسم انتخاب: باید یو له ${x(n.values,"|")} څخه وای`;case"too_big":{let e=n.inclusive?"<=":"<",l=t(n.origin);if(l)return`ډیر لوی: ${n.origin??"ارزښت"} باید ${e}${n.maximum.toString()} ${l.unit??"عنصرونه"} ولري`;return`ډیر لوی: ${n.origin??"ارزښت"} باید ${e}${n.maximum.toString()} وي`}case"too_small":{let e=n.inclusive?">=":">",l=t(n.origin);if(l)return`ډیر کوچنی: ${n.origin} باید ${e}${n.minimum.toString()} ${l.unit} ولري`;return`ډیر کوچنی: ${n.origin} باید ${e}${n.minimum.toString()} وي`}case"invalid_format":{let e=n;if(e.format==="starts_with")return`ناسم متن: باید د "${e.prefix}" سره پیل شي`;if(e.format==="ends_with")return`ناسم متن: باید د "${e.suffix}" سره پای ته ورسيږي`;if(e.format==="includes")return`ناسم متن: باید "${e.includes}" ولري`;if(e.format==="regex")return`ناسم متن: باید د ${e.pattern} سره مطابقت ولري`;return`${o[e.format]??n.format} ناسم دی`}case"not_multiple_of":return`ناسم عدد: باید د ${n.divisor} مضرب وي`;case"unrecognized_keys":return`ناسم ${n.keys.length>1?"کلیډونه":"کلیډ"}: ${x(n.keys,", ")}`;case"invalid_key":return`ناسم کلیډ په ${n.origin} کې`;case"invalid_union":return"ناسمه ورودي";case"invalid_element":return`ناسم عنصر په ${n.origin} کې`;default:return"ناسمه ورودي"}}};function Um(){return{localeError:XI()}}var PI=()=>{let r={string:{unit:"znaków",verb:"mieć"},file:{unit:"bajtów",verb:"mieć"},array:{unit:"elementów",verb:"mieć"},set:{unit:"elementów",verb:"mieć"}};function t(n){return r[n]??null}let i=(n)=>{let e=typeof n;switch(e){case"number":return Number.isNaN(n)?"NaN":"liczba";case"object":{if(Array.isArray(n))return"tablica";if(n===null)return"null";if(Object.getPrototypeOf(n)!==Object.prototype&&n.constructor)return n.constructor.name}}return e},o={regex:"wyrażenie",email:"adres email",url:"URL",emoji:"emoji",uuid:"UUID",uuidv4:"UUIDv4",uuidv6:"UUIDv6",nanoid:"nanoid",guid:"GUID",cuid:"cuid",cuid2:"cuid2",ulid:"ULID",xid:"XID",ksuid:"KSUID",datetime:"data i godzina w formacie ISO",date:"data w formacie ISO",time:"godzina w formacie ISO",duration:"czas trwania ISO",ipv4:"adres IPv4",ipv6:"adres IPv6",cidrv4:"zakres IPv4",cidrv6:"zakres IPv6",base64:"ciąg znaków zakodowany w formacie base64",base64url:"ciąg znaków zakodowany w formacie base64url",json_string:"ciąg znaków w formacie JSON",e164:"liczba E.164",jwt:"JWT",template_literal:"wejście"};return(n)=>{switch(n.code){case"invalid_type":return`Nieprawidłowe dane wejściowe: oczekiwano ${n.expected}, otrzymano ${i(n.input)}`;case"invalid_value":if(n.values.length===1)return`Nieprawidłowe dane wejściowe: oczekiwano ${j(n.values[0])}`;return`Nieprawidłowa opcja: oczekiwano jednej z wartości ${x(n.values,"|")}`;case"too_big":{let e=n.inclusive?"<=":"<",l=t(n.origin);if(l)return`Za duża wartość: oczekiwano, że ${n.origin??"wartość"} będzie mieć ${e}${n.maximum.toString()} ${l.unit??"elementów"}`;return`Zbyt duż(y/a/e): oczekiwano, że ${n.origin??"wartość"} będzie wynosić ${e}${n.maximum.toString()}`}case"too_small":{let e=n.inclusive?">=":">",l=t(n.origin);if(l)return`Za mała wartość: oczekiwano, że ${n.origin??"wartość"} będzie mieć ${e}${n.minimum.toString()} ${l.unit??"elementów"}`;return`Zbyt mał(y/a/e): oczekiwano, że ${n.origin??"wartość"} będzie wynosić ${e}${n.minimum.toString()}`}case"invalid_format":{let e=n;if(e.format==="starts_with")return`Nieprawidłowy ciąg znaków: musi zaczynać się od "${e.prefix}"`;if(e.format==="ends_with")return`Nieprawidłowy ciąg znaków: musi kończyć się na "${e.suffix}"`;if(e.format==="includes")return`Nieprawidłowy ciąg znaków: musi zawierać "${e.includes}"`;if(e.format==="regex")return`Nieprawidłowy ciąg znaków: musi odpowiadać wzorcowi ${e.pattern}`;return`Nieprawidłow(y/a/e) ${o[e.format]??n.format}`}case"not_multiple_of":return`Nieprawidłowa liczba: musi być wielokrotnością ${n.divisor}`;case"unrecognized_keys":return`Nierozpoznane klucze${n.keys.length>1?"s":""}: ${x(n.keys,", ")}`;case"invalid_key":return`Nieprawidłowy klucz w ${n.origin}`;case"invalid_union":return"Nieprawidłowe dane wejściowe";case"invalid_element":return`Nieprawidłowa wartość w ${n.origin}`;default:return"Nieprawidłowe dane wejściowe"}}};function km(){return{localeError:PI()}}var qI=()=>{let r={string:{unit:"caracteres",verb:"ter"},file:{unit:"bytes",verb:"ter"},array:{unit:"itens",verb:"ter"},set:{unit:"itens",verb:"ter"}};function t(n){return r[n]??null}let i=(n)=>{let e=typeof n;switch(e){case"number":return Number.isNaN(n)?"NaN":"número";case"object":{if(Array.isArray(n))return"array";if(n===null)return"nulo";if(Object.getPrototypeOf(n)!==Object.prototype&&n.constructor)return n.constructor.name}}return e},o={regex:"padrão",email:"endereço de e-mail",url:"URL",emoji:"emoji",uuid:"UUID",uuidv4:"UUIDv4",uuidv6:"UUIDv6",nanoid:"nanoid",guid:"GUID",cuid:"cuid",cuid2:"cuid2",ulid:"ULID",xid:"XID",ksuid:"KSUID",datetime:"data e hora ISO",date:"data ISO",time:"hora ISO",duration:"duração ISO",ipv4:"endereço IPv4",ipv6:"endereço IPv6",cidrv4:"faixa de IPv4",cidrv6:"faixa de IPv6",base64:"texto codificado em base64",base64url:"URL codificada em base64",json_string:"texto JSON",e164:"número E.164",jwt:"JWT",template_literal:"entrada"};return(n)=>{switch(n.code){case"invalid_type":return`Tipo inválido: esperado ${n.expected}, recebido ${i(n.input)}`;case"invalid_value":if(n.values.length===1)return`Entrada inválida: esperado ${j(n.values[0])}`;return`Opção inválida: esperada uma das ${x(n.values,"|")}`;case"too_big":{let e=n.inclusive?"<=":"<",l=t(n.origin);if(l)return`Muito grande: esperado que ${n.origin??"valor"} tivesse ${e}${n.maximum.toString()} ${l.unit??"elementos"}`;return`Muito grande: esperado que ${n.origin??"valor"} fosse ${e}${n.maximum.toString()}`}case"too_small":{let e=n.inclusive?">=":">",l=t(n.origin);if(l)return`Muito pequeno: esperado que ${n.origin} tivesse ${e}${n.minimum.toString()} ${l.unit}`;return`Muito pequeno: esperado que ${n.origin} fosse ${e}${n.minimum.toString()}`}case"invalid_format":{let e=n;if(e.format==="starts_with")return`Texto inválido: deve começar com "${e.prefix}"`;if(e.format==="ends_with")return`Texto inválido: deve terminar com "${e.suffix}"`;if(e.format==="includes")return`Texto inválido: deve incluir "${e.includes}"`;if(e.format==="regex")return`Texto inválido: deve corresponder ao padrão ${e.pattern}`;return`${o[e.format]??n.format} inválido`}case"not_multiple_of":return`Número inválido: deve ser múltiplo de ${n.divisor}`;case"unrecognized_keys":return`Chave${n.keys.length>1?"s":""} desconhecida${n.keys.length>1?"s":""}: ${x(n.keys,", ")}`;case"invalid_key":return`Chave inválida em ${n.origin}`;case"invalid_union":return"Entrada inválida";case"invalid_element":return`Valor inválido em ${n.origin}`;default:return"Campo inválido"}}};function Jm(){return{localeError:qI()}}function zf(r,t,i,o){let n=Math.abs(r),e=n%10,l=n%100;if(l>=11&&l<=19)return o;if(e===1)return t;if(e>=2&&e<=4)return i;return o}var WI=()=>{let r={string:{unit:{one:"символ",few:"символа",many:"символов"},verb:"иметь"},file:{unit:{one:"байт",few:"байта",many:"байт"},verb:"иметь"},array:{unit:{one:"элемент",few:"элемента",many:"элементов"},verb:"иметь"},set:{unit:{one:"элемент",few:"элемента",many:"элементов"},verb:"иметь"}};function t(n){return r[n]??null}let i=(n)=>{let e=typeof n;switch(e){case"number":return Number.isNaN(n)?"NaN":"число";case"object":{if(Array.isArray(n))return"массив";if(n===null)return"null";if(Object.getPrototypeOf(n)!==Object.prototype&&n.constructor)return n.constructor.name}}return e},o={regex:"ввод",email:"email адрес",url:"URL",emoji:"эмодзи",uuid:"UUID",uuidv4:"UUIDv4",uuidv6:"UUIDv6",nanoid:"nanoid",guid:"GUID",cuid:"cuid",cuid2:"cuid2",ulid:"ULID",xid:"XID",ksuid:"KSUID",datetime:"ISO дата и время",date:"ISO дата",time:"ISO время",duration:"ISO длительность",ipv4:"IPv4 адрес",ipv6:"IPv6 адрес",cidrv4:"IPv4 диапазон",cidrv6:"IPv6 диапазон",base64:"строка в формате base64",base64url:"строка в формате base64url",json_string:"JSON строка",e164:"номер E.164",jwt:"JWT",template_literal:"ввод"};return(n)=>{switch(n.code){case"invalid_type":return`Неверный ввод: ожидалось ${n.expected}, получено ${i(n.input)}`;case"invalid_value":if(n.values.length===1)return`Неверный ввод: ожидалось ${j(n.values[0])}`;return`Неверный вариант: ожидалось одно из ${x(n.values,"|")}`;case"too_big":{let e=n.inclusive?"<=":"<",l=t(n.origin);if(l){let u=Number(n.maximum),g=zf(u,l.unit.one,l.unit.few,l.unit.many);return`Слишком большое значение: ожидалось, что ${n.origin??"значение"} будет иметь ${e}${n.maximum.toString()} ${g}`}return`Слишком большое значение: ожидалось, что ${n.origin??"значение"} будет ${e}${n.maximum.toString()}`}case"too_small":{let e=n.inclusive?">=":">",l=t(n.origin);if(l){let u=Number(n.minimum),g=zf(u,l.unit.one,l.unit.few,l.unit.many);return`Слишком маленькое значение: ожидалось, что ${n.origin} будет иметь ${e}${n.minimum.toString()} ${g}`}return`Слишком маленькое значение: ожидалось, что ${n.origin} будет ${e}${n.minimum.toString()}`}case"invalid_format":{let e=n;if(e.format==="starts_with")return`Неверная строка: должна начинаться с "${e.prefix}"`;if(e.format==="ends_with")return`Неверная строка: должна заканчиваться на "${e.suffix}"`;if(e.format==="includes")return`Неверная строка: должна содержать "${e.includes}"`;if(e.format==="regex")return`Неверная строка: должна соответствовать шаблону ${e.pattern}`;return`Неверный ${o[e.format]??n.format}`}case"not_multiple_of":return`Неверное число: должно быть кратным ${n.divisor}`;case"unrecognized_keys":return`Нераспознанн${n.keys.length>1?"ые":"ый"} ключ${n.keys.length>1?"и":""}: ${x(n.keys,", ")}`;case"invalid_key":return`Неверный ключ в ${n.origin}`;case"invalid_union":return"Неверные входные данные";case"invalid_element":return`Неверное значение в ${n.origin}`;default:return"Неверные входные данные"}}};function Xm(){return{localeError:WI()}}var KI=()=>{let r={string:{unit:"znakov",verb:"imeti"},file:{unit:"bajtov",verb:"imeti"},array:{unit:"elementov",verb:"imeti"},set:{unit:"elementov",verb:"imeti"}};function t(n){return r[n]??null}let i=(n)=>{let e=typeof n;switch(e){case"number":return Number.isNaN(n)?"NaN":"število";case"object":{if(Array.isArray(n))return"tabela";if(n===null)return"null";if(Object.getPrototypeOf(n)!==Object.prototype&&n.constructor)return n.constructor.name}}return e},o={regex:"vnos",email:"e-poštni naslov",url:"URL",emoji:"emoji",uuid:"UUID",uuidv4:"UUIDv4",uuidv6:"UUIDv6",nanoid:"nanoid",guid:"GUID",cuid:"cuid",cuid2:"cuid2",ulid:"ULID",xid:"XID",ksuid:"KSUID",datetime:"ISO datum in čas",date:"ISO datum",time:"ISO čas",duration:"ISO trajanje",ipv4:"IPv4 naslov",ipv6:"IPv6 naslov",cidrv4:"obseg IPv4",cidrv6:"obseg IPv6",base64:"base64 kodiran niz",base64url:"base64url kodiran niz",json_string:"JSON niz",e164:"E.164 številka",jwt:"JWT",template_literal:"vnos"};return(n)=>{switch(n.code){case"invalid_type":return`Neveljaven vnos: pričakovano ${n.expected}, prejeto ${i(n.input)}`;case"invalid_value":if(n.values.length===1)return`Neveljaven vnos: pričakovano ${j(n.values[0])}`;return`Neveljavna možnost: pričakovano eno izmed ${x(n.values,"|")}`;case"too_big":{let e=n.inclusive?"<=":"<",l=t(n.origin);if(l)return`Preveliko: pričakovano, da bo ${n.origin??"vrednost"} imelo ${e}${n.maximum.toString()} ${l.unit??"elementov"}`;return`Preveliko: pričakovano, da bo ${n.origin??"vrednost"} ${e}${n.maximum.toString()}`}case"too_small":{let e=n.inclusive?">=":">",l=t(n.origin);if(l)return`Premajhno: pričakovano, da bo ${n.origin} imelo ${e}${n.minimum.toString()} ${l.unit}`;return`Premajhno: pričakovano, da bo ${n.origin} ${e}${n.minimum.toString()}`}case"invalid_format":{let e=n;if(e.format==="starts_with")return`Neveljaven niz: mora se začeti z "${e.prefix}"`;if(e.format==="ends_with")return`Neveljaven niz: mora se končati z "${e.suffix}"`;if(e.format==="includes")return`Neveljaven niz: mora vsebovati "${e.includes}"`;if(e.format==="regex")return`Neveljaven niz: mora ustrezati vzorcu ${e.pattern}`;return`Neveljaven ${o[e.format]??n.format}`}case"not_multiple_of":return`Neveljavno število: mora biti večkratnik ${n.divisor}`;case"unrecognized_keys":return`Neprepoznan${n.keys.length>1?"i ključi":" ključ"}: ${x(n.keys,", ")}`;case"invalid_key":return`Neveljaven ključ v ${n.origin}`;case"invalid_union":return"Neveljaven vnos";case"invalid_element":return`Neveljavna vrednost v ${n.origin}`;default:return"Neveljaven vnos"}}};function Pm(){return{localeError:KI()}}var VI=()=>{let r={string:{unit:"tecken",verb:"att ha"},file:{unit:"bytes",verb:"att ha"},array:{unit:"objekt",verb:"att innehålla"},set:{unit:"objekt",verb:"att innehålla"}};function t(n){return r[n]??null}let i=(n)=>{let e=typeof n;switch(e){case"number":return Number.isNaN(n)?"NaN":"antal";case"object":{if(Array.isArray(n))return"lista";if(n===null)return"null";if(Object.getPrototypeOf(n)!==Object.prototype&&n.constructor)return n.constructor.name}}return e},o={regex:"reguljärt uttryck",email:"e-postadress",url:"URL",emoji:"emoji",uuid:"UUID",uuidv4:"UUIDv4",uuidv6:"UUIDv6",nanoid:"nanoid",guid:"GUID",cuid:"cuid",cuid2:"cuid2",ulid:"ULID",xid:"XID",ksuid:"KSUID",datetime:"ISO-datum och tid",date:"ISO-datum",time:"ISO-tid",duration:"ISO-varaktighet",ipv4:"IPv4-intervall",ipv6:"IPv6-intervall",cidrv4:"IPv4-spektrum",cidrv6:"IPv6-spektrum",base64:"base64-kodad sträng",base64url:"base64url-kodad sträng",json_string:"JSON-sträng",e164:"E.164-nummer",jwt:"JWT",template_literal:"mall-literal"};return(n)=>{switch(n.code){case"invalid_type":return`Ogiltig inmatning: förväntat ${n.expected}, fick ${i(n.input)}`;case"invalid_value":if(n.values.length===1)return`Ogiltig inmatning: förväntat ${j(n.values[0])}`;return`Ogiltigt val: förväntade en av ${x(n.values,"|")}`;case"too_big":{let e=n.inclusive?"<=":"<",l=t(n.origin);if(l)return`För stor(t): förväntade ${n.origin??"värdet"} att ha ${e}${n.maximum.toString()} ${l.unit??"element"}`;return`För stor(t): förväntat ${n.origin??"värdet"} att ha ${e}${n.maximum.toString()}`}case"too_small":{let e=n.inclusive?">=":">",l=t(n.origin);if(l)return`För lite(t): förväntade ${n.origin??"värdet"} att ha ${e}${n.minimum.toString()} ${l.unit}`;return`För lite(t): förväntade ${n.origin??"värdet"} att ha ${e}${n.minimum.toString()}`}case"invalid_format":{let e=n;if(e.format==="starts_with")return`Ogiltig sträng: måste börja med "${e.prefix}"`;if(e.format==="ends_with")return`Ogiltig sträng: måste sluta med "${e.suffix}"`;if(e.format==="includes")return`Ogiltig sträng: måste innehålla "${e.includes}"`;if(e.format==="regex")return`Ogiltig sträng: måste matcha mönstret "${e.pattern}"`;return`Ogiltig(t) ${o[e.format]??n.format}`}case"not_multiple_of":return`Ogiltigt tal: måste vara en multipel av ${n.divisor}`;case"unrecognized_keys":return`${n.keys.length>1?"Okända nycklar":"Okänd nyckel"}: ${x(n.keys,", ")}`;case"invalid_key":return`Ogiltig nyckel i ${n.origin??"värdet"}`;case"invalid_union":return"Ogiltig input";case"invalid_element":return`Ogiltigt värde i ${n.origin??"värdet"}`;default:return"Ogiltig input"}}};function qm(){return{localeError:VI()}}var LI=()=>{let r={string:{unit:"எழுத்துக்கள்",verb:"கொண்டிருக்க வேண்டும்"},file:{unit:"பைட்டுகள்",verb:"கொண்டிருக்க வேண்டும்"},array:{unit:"உறுப்புகள்",verb:"கொண்டிருக்க வேண்டும்"},set:{unit:"உறுப்புகள்",verb:"கொண்டிருக்க வேண்டும்"}};function t(n){return r[n]??null}let i=(n)=>{let e=typeof n;switch(e){case"number":return Number.isNaN(n)?"எண் அல்லாதது":"எண்";case"object":{if(Array.isArray(n))return"அணி";if(n===null)return"வெறுமை";if(Object.getPrototypeOf(n)!==Object.prototype&&n.constructor)return n.constructor.name}}return e},o={regex:"உள்ளீடு",email:"மின்னஞ்சல் முகவரி",url:"URL",emoji:"emoji",uuid:"UUID",uuidv4:"UUIDv4",uuidv6:"UUIDv6",nanoid:"nanoid",guid:"GUID",cuid:"cuid",cuid2:"cuid2",ulid:"ULID",xid:"XID",ksuid:"KSUID",datetime:"ISO தேதி நேரம்",date:"ISO தேதி",time:"ISO நேரம்",duration:"ISO கால அளவு",ipv4:"IPv4 முகவரி",ipv6:"IPv6 முகவரி",cidrv4:"IPv4 வரம்பு",cidrv6:"IPv6 வரம்பு",base64:"base64-encoded சரம்",base64url:"base64url-encoded சரம்",json_string:"JSON சரம்",e164:"E.164 எண்",jwt:"JWT",template_literal:"input"};return(n)=>{switch(n.code){case"invalid_type":return`தவறான உள்ளீடு: எதிர்பார்க்கப்பட்டது ${n.expected}, பெறப்பட்டது ${i(n.input)}`;case"invalid_value":if(n.values.length===1)return`தவறான உள்ளீடு: எதிர்பார்க்கப்பட்டது ${j(n.values[0])}`;return`தவறான விருப்பம்: எதிர்பார்க்கப்பட்டது ${x(n.values,"|")} இல் ஒன்று`;case"too_big":{let e=n.inclusive?"<=":"<",l=t(n.origin);if(l)return`மிக பெரியது: எதிர்பார்க்கப்பட்டது ${n.origin??"மதிப்பு"} ${e}${n.maximum.toString()} ${l.unit??"உறுப்புகள்"} ஆக இருக்க வேண்டும்`;return`மிக பெரியது: எதிர்பார்க்கப்பட்டது ${n.origin??"மதிப்பு"} ${e}${n.maximum.toString()} ஆக இருக்க வேண்டும்`}case"too_small":{let e=n.inclusive?">=":">",l=t(n.origin);if(l)return`மிகச் சிறியது: எதிர்பார்க்கப்பட்டது ${n.origin} ${e}${n.minimum.toString()} ${l.unit} ஆக இருக்க வேண்டும்`;return`மிகச் சிறியது: எதிர்பார்க்கப்பட்டது ${n.origin} ${e}${n.minimum.toString()} ஆக இருக்க வேண்டும்`}case"invalid_format":{let e=n;if(e.format==="starts_with")return`தவறான சரம்: "${e.prefix}" இல் தொடங்க வேண்டும்`;if(e.format==="ends_with")return`தவறான சரம்: "${e.suffix}" இல் முடிவடைய வேண்டும்`;if(e.format==="includes")return`தவறான சரம்: "${e.includes}" ஐ உள்ளடக்க வேண்டும்`;if(e.format==="regex")return`தவறான சரம்: ${e.pattern} முறைபாட்டுடன் பொருந்த வேண்டும்`;return`தவறான ${o[e.format]??n.format}`}case"not_multiple_of":return`தவறான எண்: ${n.divisor} இன் பலமாக இருக்க வேண்டும்`;case"unrecognized_keys":return`அடையாளம் தெரியாத விசை${n.keys.length>1?"கள்":""}: ${x(n.keys,", ")}`;case"invalid_key":return`${n.origin} இல் தவறான விசை`;case"invalid_union":return"தவறான உள்ளீடு";case"invalid_element":return`${n.origin} இல் தவறான மதிப்பு`;default:return"தவறான உள்ளீடு"}}};function Wm(){return{localeError:LI()}}var YI=()=>{let r={string:{unit:"ตัวอักษร",verb:"ควรมี"},file:{unit:"ไบต์",verb:"ควรมี"},array:{unit:"รายการ",verb:"ควรมี"},set:{unit:"รายการ",verb:"ควรมี"}};function t(n){return r[n]??null}let i=(n)=>{let e=typeof n;switch(e){case"number":return Number.isNaN(n)?"ไม่ใช่ตัวเลข (NaN)":"ตัวเลข";case"object":{if(Array.isArray(n))return"อาร์เรย์ (Array)";if(n===null)return"ไม่มีค่า (null)";if(Object.getPrototypeOf(n)!==Object.prototype&&n.constructor)return n.constructor.name}}return e},o={regex:"ข้อมูลที่ป้อน",email:"ที่อยู่อีเมล",url:"URL",emoji:"อิโมจิ",uuid:"UUID",uuidv4:"UUIDv4",uuidv6:"UUIDv6",nanoid:"nanoid",guid:"GUID",cuid:"cuid",cuid2:"cuid2",ulid:"ULID",xid:"XID",ksuid:"KSUID",datetime:"วันที่เวลาแบบ ISO",date:"วันที่แบบ ISO",time:"เวลาแบบ ISO",duration:"ช่วงเวลาแบบ ISO",ipv4:"ที่อยู่ IPv4",ipv6:"ที่อยู่ IPv6",cidrv4:"ช่วง IP แบบ IPv4",cidrv6:"ช่วง IP แบบ IPv6",base64:"ข้อความแบบ Base64",base64url:"ข้อความแบบ Base64 สำหรับ URL",json_string:"ข้อความแบบ JSON",e164:"เบอร์โทรศัพท์ระหว่างประเทศ (E.164)",jwt:"โทเคน JWT",template_literal:"ข้อมูลที่ป้อน"};return(n)=>{switch(n.code){case"invalid_type":return`ประเภทข้อมูลไม่ถูกต้อง: ควรเป็น ${n.expected} แต่ได้รับ ${i(n.input)}`;case"invalid_value":if(n.values.length===1)return`ค่าไม่ถูกต้อง: ควรเป็น ${j(n.values[0])}`;return`ตัวเลือกไม่ถูกต้อง: ควรเป็นหนึ่งใน ${x(n.values,"|")}`;case"too_big":{let e=n.inclusive?"ไม่เกิน":"น้อยกว่า",l=t(n.origin);if(l)return`เกินกำหนด: ${n.origin??"ค่า"} ควรมี${e} ${n.maximum.toString()} ${l.unit??"รายการ"}`;return`เกินกำหนด: ${n.origin??"ค่า"} ควรมี${e} ${n.maximum.toString()}`}case"too_small":{let e=n.inclusive?"อย่างน้อย":"มากกว่า",l=t(n.origin);if(l)return`น้อยกว่ากำหนด: ${n.origin} ควรมี${e} ${n.minimum.toString()} ${l.unit}`;return`น้อยกว่ากำหนด: ${n.origin} ควรมี${e} ${n.minimum.toString()}`}case"invalid_format":{let e=n;if(e.format==="starts_with")return`รูปแบบไม่ถูกต้อง: ข้อความต้องขึ้นต้นด้วย "${e.prefix}"`;if(e.format==="ends_with")return`รูปแบบไม่ถูกต้อง: ข้อความต้องลงท้ายด้วย "${e.suffix}"`;if(e.format==="includes")return`รูปแบบไม่ถูกต้อง: ข้อความต้องมี "${e.includes}" อยู่ในข้อความ`;if(e.format==="regex")return`รูปแบบไม่ถูกต้อง: ต้องตรงกับรูปแบบที่กำหนด ${e.pattern}`;return`รูปแบบไม่ถูกต้อง: ${o[e.format]??n.format}`}case"not_multiple_of":return`ตัวเลขไม่ถูกต้อง: ต้องเป็นจำนวนที่หารด้วย ${n.divisor} ได้ลงตัว`;case"unrecognized_keys":return`พบคีย์ที่ไม่รู้จัก: ${x(n.keys,", ")}`;case"invalid_key":return`คีย์ไม่ถูกต้องใน ${n.origin}`;case"invalid_union":return"ข้อมูลไม่ถูกต้อง: ไม่ตรงกับรูปแบบยูเนียนที่กำหนดไว้";case"invalid_element":return`ข้อมูลไม่ถูกต้องใน ${n.origin}`;default:return"ข้อมูลไม่ถูกต้อง"}}};function Km(){return{localeError:YI()}}var EI=(r)=>{let t=typeof r;switch(t){case"number":return Number.isNaN(r)?"NaN":"number";case"object":{if(Array.isArray(r))return"array";if(r===null)return"null";if(Object.getPrototypeOf(r)!==Object.prototype&&r.constructor)return r.constructor.name}}return t},QI=()=>{let r={string:{unit:"karakter",verb:"olmalı"},file:{unit:"bayt",verb:"olmalı"},array:{unit:"öğe",verb:"olmalı"},set:{unit:"öğe",verb:"olmalı"}};function t(o){return r[o]??null}let i={regex:"girdi",email:"e-posta adresi",url:"URL",emoji:"emoji",uuid:"UUID",uuidv4:"UUIDv4",uuidv6:"UUIDv6",nanoid:"nanoid",guid:"GUID",cuid:"cuid",cuid2:"cuid2",ulid:"ULID",xid:"XID",ksuid:"KSUID",datetime:"ISO tarih ve saat",date:"ISO tarih",time:"ISO saat",duration:"ISO süre",ipv4:"IPv4 adresi",ipv6:"IPv6 adresi",cidrv4:"IPv4 aralığı",cidrv6:"IPv6 aralığı",base64:"base64 ile şifrelenmiş metin",base64url:"base64url ile şifrelenmiş metin",json_string:"JSON dizesi",e164:"E.164 sayısı",jwt:"JWT",template_literal:"Şablon dizesi"};return(o)=>{switch(o.code){case"invalid_type":return`Geçersiz değer: beklenen ${o.expected}, alınan ${EI(o.input)}`;case"invalid_value":if(o.values.length===1)return`Geçersiz değer: beklenen ${j(o.values[0])}`;return`Geçersiz seçenek: aşağıdakilerden biri olmalı: ${x(o.values,"|")}`;case"too_big":{let n=o.inclusive?"<=":"<",e=t(o.origin);if(e)return`Çok büyük: beklenen ${o.origin??"değer"} ${n}${o.maximum.toString()} ${e.unit??"öğe"}`;return`Çok büyük: beklenen ${o.origin??"değer"} ${n}${o.maximum.toString()}`}case"too_small":{let n=o.inclusive?">=":">",e=t(o.origin);if(e)return`Çok küçük: beklenen ${o.origin} ${n}${o.minimum.toString()} ${e.unit}`;return`Çok küçük: beklenen ${o.origin} ${n}${o.minimum.toString()}`}case"invalid_format":{let n=o;if(n.format==="starts_with")return`Geçersiz metin: "${n.prefix}" ile başlamalı`;if(n.format==="ends_with")return`Geçersiz metin: "${n.suffix}" ile bitmeli`;if(n.format==="includes")return`Geçersiz metin: "${n.includes}" içermeli`;if(n.format==="regex")return`Geçersiz metin: ${n.pattern} desenine uymalı`;return`Geçersiz ${i[n.format]??o.format}`}case"not_multiple_of":return`Geçersiz sayı: ${o.divisor} ile tam bölünebilmeli`;case"unrecognized_keys":return`Tanınmayan anahtar${o.keys.length>1?"lar":""}: ${x(o.keys,", ")}`;case"invalid_key":return`${o.origin} içinde geçersiz anahtar`;case"invalid_union":return"Geçersiz değer";case"invalid_element":return`${o.origin} içinde geçersiz değer`;default:return"Geçersiz değer"}}};function Vm(){return{localeError:QI()}}var FI=()=>{let r={string:{unit:"символів",verb:"матиме"},file:{unit:"байтів",verb:"матиме"},array:{unit:"елементів",verb:"матиме"},set:{unit:"елементів",verb:"матиме"}};function t(n){return r[n]??null}let i=(n)=>{let e=typeof n;switch(e){case"number":return Number.isNaN(n)?"NaN":"число";case"object":{if(Array.isArray(n))return"масив";if(n===null)return"null";if(Object.getPrototypeOf(n)!==Object.prototype&&n.constructor)return n.constructor.name}}return e},o={regex:"вхідні дані",email:"адреса електронної пошти",url:"URL",emoji:"емодзі",uuid:"UUID",uuidv4:"UUIDv4",uuidv6:"UUIDv6",nanoid:"nanoid",guid:"GUID",cuid:"cuid",cuid2:"cuid2",ulid:"ULID",xid:"XID",ksuid:"KSUID",datetime:"дата та час ISO",date:"дата ISO",time:"час ISO",duration:"тривалість ISO",ipv4:"адреса IPv4",ipv6:"адреса IPv6",cidrv4:"діапазон IPv4",cidrv6:"діапазон IPv6",base64:"рядок у кодуванні base64",base64url:"рядок у кодуванні base64url",json_string:"рядок JSON",e164:"номер E.164",jwt:"JWT",template_literal:"вхідні дані"};return(n)=>{switch(n.code){case"invalid_type":return`Неправильні вхідні дані: очікується ${n.expected}, отримано ${i(n.input)}`;case"invalid_value":if(n.values.length===1)return`Неправильні вхідні дані: очікується ${j(n.values[0])}`;return`Неправильна опція: очікується одне з ${x(n.values,"|")}`;case"too_big":{let e=n.inclusive?"<=":"<",l=t(n.origin);if(l)return`Занадто велике: очікується, що ${n.origin??"значення"} ${l.verb} ${e}${n.maximum.toString()} ${l.unit??"елементів"}`;return`Занадто велике: очікується, що ${n.origin??"значення"} буде ${e}${n.maximum.toString()}`}case"too_small":{let e=n.inclusive?">=":">",l=t(n.origin);if(l)return`Занадто мале: очікується, що ${n.origin} ${l.verb} ${e}${n.minimum.toString()} ${l.unit}`;return`Занадто мале: очікується, що ${n.origin} буде ${e}${n.minimum.toString()}`}case"invalid_format":{let e=n;if(e.format==="starts_with")return`Неправильний рядок: повинен починатися з "${e.prefix}"`;if(e.format==="ends_with")return`Неправильний рядок: повинен закінчуватися на "${e.suffix}"`;if(e.format==="includes")return`Неправильний рядок: повинен містити "${e.includes}"`;if(e.format==="regex")return`Неправильний рядок: повинен відповідати шаблону ${e.pattern}`;return`Неправильний ${o[e.format]??n.format}`}case"not_multiple_of":return`Неправильне число: повинно бути кратним ${n.divisor}`;case"unrecognized_keys":return`Нерозпізнаний ключ${n.keys.length>1?"і":""}: ${x(n.keys,", ")}`;case"invalid_key":return`Неправильний ключ у ${n.origin}`;case"invalid_union":return"Неправильні вхідні дані";case"invalid_element":return`Неправильне значення у ${n.origin}`;default:return"Неправильні вхідні дані"}}};function Lm(){return{localeError:FI()}}var GI=()=>{let r={string:{unit:"حروف",verb:"ہونا"},file:{unit:"بائٹس",verb:"ہونا"},array:{unit:"آئٹمز",verb:"ہونا"},set:{unit:"آئٹمز",verb:"ہونا"}};function t(n){return r[n]??null}let i=(n)=>{let e=typeof n;switch(e){case"number":return Number.isNaN(n)?"NaN":"نمبر";case"object":{if(Array.isArray(n))return"آرے";if(n===null)return"نل";if(Object.getPrototypeOf(n)!==Object.prototype&&n.constructor)return n.constructor.name}}return e},o={regex:"ان پٹ",email:"ای میل ایڈریس",url:"یو آر ایل",emoji:"ایموجی",uuid:"یو یو آئی ڈی",uuidv4:"یو یو آئی ڈی وی 4",uuidv6:"یو یو آئی ڈی وی 6",nanoid:"نینو آئی ڈی",guid:"جی یو آئی ڈی",cuid:"سی یو آئی ڈی",cuid2:"سی یو آئی ڈی 2",ulid:"یو ایل آئی ڈی",xid:"ایکس آئی ڈی",ksuid:"کے ایس یو آئی ڈی",datetime:"آئی ایس او ڈیٹ ٹائم",date:"آئی ایس او تاریخ",time:"آئی ایس او وقت",duration:"آئی ایس او مدت",ipv4:"آئی پی وی 4 ایڈریس",ipv6:"آئی پی وی 6 ایڈریس",cidrv4:"آئی پی وی 4 رینج",cidrv6:"آئی پی وی 6 رینج",base64:"بیس 64 ان کوڈڈ سٹرنگ",base64url:"بیس 64 یو آر ایل ان کوڈڈ سٹرنگ",json_string:"جے ایس او این سٹرنگ",e164:"ای 164 نمبر",jwt:"جے ڈبلیو ٹی",template_literal:"ان پٹ"};return(n)=>{switch(n.code){case"invalid_type":return`غلط ان پٹ: ${n.expected} متوقع تھا، ${i(n.input)} موصول ہوا`;case"invalid_value":if(n.values.length===1)return`غلط ان پٹ: ${j(n.values[0])} متوقع تھا`;return`غلط آپشن: ${x(n.values,"|")} میں سے ایک متوقع تھا`;case"too_big":{let e=n.inclusive?"<=":"<",l=t(n.origin);if(l)return`بہت بڑا: ${n.origin??"ویلیو"} کے ${e}${n.maximum.toString()} ${l.unit??"عناصر"} ہونے متوقع تھے`;return`بہت بڑا: ${n.origin??"ویلیو"} کا ${e}${n.maximum.toString()} ہونا متوقع تھا`}case"too_small":{let e=n.inclusive?">=":">",l=t(n.origin);if(l)return`بہت چھوٹا: ${n.origin} کے ${e}${n.minimum.toString()} ${l.unit} ہونے متوقع تھے`;return`بہت چھوٹا: ${n.origin} کا ${e}${n.minimum.toString()} ہونا متوقع تھا`}case"invalid_format":{let e=n;if(e.format==="starts_with")return`غلط سٹرنگ: "${e.prefix}" سے شروع ہونا چاہیے`;if(e.format==="ends_with")return`غلط سٹرنگ: "${e.suffix}" پر ختم ہونا چاہیے`;if(e.format==="includes")return`غلط سٹرنگ: "${e.includes}" شامل ہونا چاہیے`;if(e.format==="regex")return`غلط سٹرنگ: پیٹرن ${e.pattern} سے میچ ہونا چاہیے`;return`غلط ${o[e.format]??n.format}`}case"not_multiple_of":return`غلط نمبر: ${n.divisor} کا مضاعف ہونا چاہیے`;case"unrecognized_keys":return`غیر تسلیم شدہ کی${n.keys.length>1?"ز":""}: ${x(n.keys,"، ")}`;case"invalid_key":return`${n.origin} میں غلط کی`;case"invalid_union":return"غلط ان پٹ";case"invalid_element":return`${n.origin} میں غلط ویلیو`;default:return"غلط ان پٹ"}}};function Ym(){return{localeError:GI()}}var SI=()=>{let r={string:{unit:"ký tự",verb:"có"},file:{unit:"byte",verb:"có"},array:{unit:"phần tử",verb:"có"},set:{unit:"phần tử",verb:"có"}};function t(n){return r[n]??null}let i=(n)=>{let e=typeof n;switch(e){case"number":return Number.isNaN(n)?"NaN":"số";case"object":{if(Array.isArray(n))return"mảng";if(n===null)return"null";if(Object.getPrototypeOf(n)!==Object.prototype&&n.constructor)return n.constructor.name}}return e},o={regex:"đầu vào",email:"địa chỉ email",url:"URL",emoji:"emoji",uuid:"UUID",uuidv4:"UUIDv4",uuidv6:"UUIDv6",nanoid:"nanoid",guid:"GUID",cuid:"cuid",cuid2:"cuid2",ulid:"ULID",xid:"XID",ksuid:"KSUID",datetime:"ngày giờ ISO",date:"ngày ISO",time:"giờ ISO",duration:"khoảng thời gian ISO",ipv4:"địa chỉ IPv4",ipv6:"địa chỉ IPv6",cidrv4:"dải IPv4",cidrv6:"dải IPv6",base64:"chuỗi mã hóa base64",base64url:"chuỗi mã hóa base64url",json_string:"chuỗi JSON",e164:"số E.164",jwt:"JWT",template_literal:"đầu vào"};return(n)=>{switch(n.code){case"invalid_type":return`Đầu vào không hợp lệ: mong đợi ${n.expected}, nhận được ${i(n.input)}`;case"invalid_value":if(n.values.length===1)return`Đầu vào không hợp lệ: mong đợi ${j(n.values[0])}`;return`Tùy chọn không hợp lệ: mong đợi một trong các giá trị ${x(n.values,"|")}`;case"too_big":{let e=n.inclusive?"<=":"<",l=t(n.origin);if(l)return`Quá lớn: mong đợi ${n.origin??"giá trị"} ${l.verb} ${e}${n.maximum.toString()} ${l.unit??"phần tử"}`;return`Quá lớn: mong đợi ${n.origin??"giá trị"} ${e}${n.maximum.toString()}`}case"too_small":{let e=n.inclusive?">=":">",l=t(n.origin);if(l)return`Quá nhỏ: mong đợi ${n.origin} ${l.verb} ${e}${n.minimum.toString()} ${l.unit}`;return`Quá nhỏ: mong đợi ${n.origin} ${e}${n.minimum.toString()}`}case"invalid_format":{let e=n;if(e.format==="starts_with")return`Chuỗi không hợp lệ: phải bắt đầu bằng "${e.prefix}"`;if(e.format==="ends_with")return`Chuỗi không hợp lệ: phải kết thúc bằng "${e.suffix}"`;if(e.format==="includes")return`Chuỗi không hợp lệ: phải bao gồm "${e.includes}"`;if(e.format==="regex")return`Chuỗi không hợp lệ: phải khớp với mẫu ${e.pattern}`;return`${o[e.format]??n.format} không hợp lệ`}case"not_multiple_of":return`Số không hợp lệ: phải là bội số của ${n.divisor}`;case"unrecognized_keys":return`Khóa không được nhận dạng: ${x(n.keys,", ")}`;case"invalid_key":return`Khóa không hợp lệ trong ${n.origin}`;case"invalid_union":return"Đầu vào không hợp lệ";case"invalid_element":return`Giá trị không hợp lệ trong ${n.origin}`;default:return"Đầu vào không hợp lệ"}}};function Em(){return{localeError:SI()}}var NI=()=>{let r={string:{unit:"字符",verb:"包含"},file:{unit:"字节",verb:"包含"},array:{unit:"项",verb:"包含"},set:{unit:"项",verb:"包含"}};function t(n){return r[n]??null}let i=(n)=>{let e=typeof n;switch(e){case"number":return Number.isNaN(n)?"非数字(NaN)":"数字";case"object":{if(Array.isArray(n))return"数组";if(n===null)return"空值(null)";if(Object.getPrototypeOf(n)!==Object.prototype&&n.constructor)return n.constructor.name}}return e},o={regex:"输入",email:"电子邮件",url:"URL",emoji:"表情符号",uuid:"UUID",uuidv4:"UUIDv4",uuidv6:"UUIDv6",nanoid:"nanoid",guid:"GUID",cuid:"cuid",cuid2:"cuid2",ulid:"ULID",xid:"XID",ksuid:"KSUID",datetime:"ISO日期时间",date:"ISO日期",time:"ISO时间",duration:"ISO时长",ipv4:"IPv4地址",ipv6:"IPv6地址",cidrv4:"IPv4网段",cidrv6:"IPv6网段",base64:"base64编码字符串",base64url:"base64url编码字符串",json_string:"JSON字符串",e164:"E.164号码",jwt:"JWT",template_literal:"输入"};return(n)=>{switch(n.code){case"invalid_type":return`无效输入：期望 ${n.expected}，实际接收 ${i(n.input)}`;case"invalid_value":if(n.values.length===1)return`无效输入：期望 ${j(n.values[0])}`;return`无效选项：期望以下之一 ${x(n.values,"|")}`;case"too_big":{let e=n.inclusive?"<=":"<",l=t(n.origin);if(l)return`数值过大：期望 ${n.origin??"值"} ${e}${n.maximum.toString()} ${l.unit??"个元素"}`;return`数值过大：期望 ${n.origin??"值"} ${e}${n.maximum.toString()}`}case"too_small":{let e=n.inclusive?">=":">",l=t(n.origin);if(l)return`数值过小：期望 ${n.origin} ${e}${n.minimum.toString()} ${l.unit}`;return`数值过小：期望 ${n.origin} ${e}${n.minimum.toString()}`}case"invalid_format":{let e=n;if(e.format==="starts_with")return`无效字符串：必须以 "${e.prefix}" 开头`;if(e.format==="ends_with")return`无效字符串：必须以 "${e.suffix}" 结尾`;if(e.format==="includes")return`无效字符串：必须包含 "${e.includes}"`;if(e.format==="regex")return`无效字符串：必须满足正则表达式 ${e.pattern}`;return`无效${o[e.format]??n.format}`}case"not_multiple_of":return`无效数字：必须是 ${n.divisor} 的倍数`;case"unrecognized_keys":return`出现未知的键(key): ${x(n.keys,", ")}`;case"invalid_key":return`${n.origin} 中的键(key)无效`;case"invalid_union":return"无效输入";case"invalid_element":return`${n.origin} 中包含无效值(value)`;default:return"无效输入"}}};function Qm(){return{localeError:NI()}}var BI=()=>{let r={string:{unit:"字元",verb:"擁有"},file:{unit:"位元組",verb:"擁有"},array:{unit:"項目",verb:"擁有"},set:{unit:"項目",verb:"擁有"}};function t(n){return r[n]??null}let i=(n)=>{let e=typeof n;switch(e){case"number":return Number.isNaN(n)?"NaN":"number";case"object":{if(Array.isArray(n))return"array";if(n===null)return"null";if(Object.getPrototypeOf(n)!==Object.prototype&&n.constructor)return n.constructor.name}}return e},o={regex:"輸入",email:"郵件地址",url:"URL",emoji:"emoji",uuid:"UUID",uuidv4:"UUIDv4",uuidv6:"UUIDv6",nanoid:"nanoid",guid:"GUID",cuid:"cuid",cuid2:"cuid2",ulid:"ULID",xid:"XID",ksuid:"KSUID",datetime:"ISO 日期時間",date:"ISO 日期",time:"ISO 時間",duration:"ISO 期間",ipv4:"IPv4 位址",ipv6:"IPv6 位址",cidrv4:"IPv4 範圍",cidrv6:"IPv6 範圍",base64:"base64 編碼字串",base64url:"base64url 編碼字串",json_string:"JSON 字串",e164:"E.164 數值",jwt:"JWT",template_literal:"輸入"};return(n)=>{switch(n.code){case"invalid_type":return`無效的輸入值：預期為 ${n.expected}，但收到 ${i(n.input)}`;case"invalid_value":if(n.values.length===1)return`無效的輸入值：預期為 ${j(n.values[0])}`;return`無效的選項：預期為以下其中之一 ${x(n.values,"|")}`;case"too_big":{let e=n.inclusive?"<=":"<",l=t(n.origin);if(l)return`數值過大：預期 ${n.origin??"值"} 應為 ${e}${n.maximum.toString()} ${l.unit??"個元素"}`;return`數值過大：預期 ${n.origin??"值"} 應為 ${e}${n.maximum.toString()}`}case"too_small":{let e=n.inclusive?">=":">",l=t(n.origin);if(l)return`數值過小：預期 ${n.origin} 應為 ${e}${n.minimum.toString()} ${l.unit}`;return`數值過小：預期 ${n.origin} 應為 ${e}${n.minimum.toString()}`}case"invalid_format":{let e=n;if(e.format==="starts_with")return`無效的字串：必須以 "${e.prefix}" 開頭`;if(e.format==="ends_with")return`無效的字串：必須以 "${e.suffix}" 結尾`;if(e.format==="includes")return`無效的字串：必須包含 "${e.includes}"`;if(e.format==="regex")return`無效的字串：必須符合格式 ${e.pattern}`;return`無效的 ${o[e.format]??n.format}`}case"not_multiple_of":return`無效的數字：必須為 ${n.divisor} 的倍數`;case"unrecognized_keys":return`無法識別的鍵值${n.keys.length>1?"們":""}：${x(n.keys,"、")}`;case"invalid_key":return`${n.origin} 中有無效的鍵值`;case"invalid_union":return"無效的輸入值";case"invalid_element":return`${n.origin} 中有無效的值`;default:return"無效的輸入值"}}};function Fm(){return{localeError:BI()}}var yI=()=>{let r={string:{unit:"àmi",verb:"ní"},file:{unit:"bytes",verb:"ní"},array:{unit:"nkan",verb:"ní"},set:{unit:"nkan",verb:"ní"}};function t(n){return r[n]??null}let i=(n)=>{let e=typeof n;switch(e){case"number":return Number.isNaN(n)?"NaN":"nọ́mbà";case"object":{if(Array.isArray(n))return"akopọ";if(n===null)return"null";if(Object.getPrototypeOf(n)!==Object.prototype&&n.constructor)return n.constructor.name}}return e},o={regex:"ẹ̀rọ ìbáwọlé",email:"àdírẹ́sì ìmẹ́lì",url:"URL",emoji:"emoji",uuid:"UUID",uuidv4:"UUIDv4",uuidv6:"UUIDv6",nanoid:"nanoid",guid:"GUID",cuid:"cuid",cuid2:"cuid2",ulid:"ULID",xid:"XID",ksuid:"KSUID",datetime:"àkókò ISO",date:"ọjọ́ ISO",time:"àkókò ISO",duration:"àkókò tó pé ISO",ipv4:"àdírẹ́sì IPv4",ipv6:"àdírẹ́sì IPv6",cidrv4:"àgbègbè IPv4",cidrv6:"àgbègbè IPv6",base64:"ọ̀rọ̀ tí a kọ́ ní base64",base64url:"ọ̀rọ̀ base64url",json_string:"ọ̀rọ̀ JSON",e164:"nọ́mbà E.164",jwt:"JWT",template_literal:"ẹ̀rọ ìbáwọlé"};return(n)=>{switch(n.code){case"invalid_type":return`Ìbáwọlé aṣìṣe: a ní láti fi ${n.expected}, àmọ̀ a rí ${i(n.input)}`;case"invalid_value":if(n.values.length===1)return`Ìbáwọlé aṣìṣe: a ní láti fi ${j(n.values[0])}`;return`Àṣàyàn aṣìṣe: yan ọ̀kan lára ${x(n.values,"|")}`;case"too_big":{let e=n.inclusive?"<=":"<",l=t(n.origin);if(l)return`Tó pọ̀ jù: a ní láti jẹ́ pé ${n.origin??"iye"} ${l.verb} ${e}${n.maximum} ${l.unit}`;return`Tó pọ̀ jù: a ní láti jẹ́ ${e}${n.maximum}`}case"too_small":{let e=n.inclusive?">=":">",l=t(n.origin);if(l)return`Kéré ju: a ní láti jẹ́ pé ${n.origin} ${l.verb} ${e}${n.minimum} ${l.unit}`;return`Kéré ju: a ní láti jẹ́ ${e}${n.minimum}`}case"invalid_format":{let e=n;if(e.format==="starts_with")return`Ọ̀rọ̀ aṣìṣe: gbọ́dọ̀ bẹ̀rẹ̀ pẹ̀lú "${e.prefix}"`;if(e.format==="ends_with")return`Ọ̀rọ̀ aṣìṣe: gbọ́dọ̀ parí pẹ̀lú "${e.suffix}"`;if(e.format==="includes")return`Ọ̀rọ̀ aṣìṣe: gbọ́dọ̀ ní "${e.includes}"`;if(e.format==="regex")return`Ọ̀rọ̀ aṣìṣe: gbọ́dọ̀ bá àpẹẹrẹ mu ${e.pattern}`;return`Aṣìṣe: ${o[e.format]??n.format}`}case"not_multiple_of":return`Nọ́mbà aṣìṣe: gbọ́dọ̀ jẹ́ èyà pípín ti ${n.divisor}`;case"unrecognized_keys":return`Bọtìnì àìmọ̀: ${x(n.keys,", ")}`;case"invalid_key":return`Bọtìnì aṣìṣe nínú ${n.origin}`;case"invalid_union":return"Ìbáwọlé aṣìṣe";case"invalid_element":return`Iye aṣìṣe nínú ${n.origin}`;default:return"Ìbáwọlé aṣìṣe"}}};function Gm(){return{localeError:yI()}}var Sm=Symbol("ZodOutput"),Nm=Symbol("ZodInput");class li{constructor(){this._map=new Map,this._idmap=new Map}add(r,...t){let i=t[0];if(this._map.set(r,i),i&&typeof i==="object"&&"id"in i){if(this._idmap.has(i.id))throw new Error(`ID ${i.id} already exists in the registry`);this._idmap.set(i.id,r)}return this}clear(){return this._map=new Map,this._idmap=new Map,this}remove(r){let t=this._map.get(r);if(t&&typeof t==="object"&&"id"in t)this._idmap.delete(t.id);return this._map.delete(r),this}get(r){let t=r._zod.parent;if(t){let i={...this.get(t)??{}};delete i.id;let o={...i,...this._map.get(r)};return Object.keys(o).length?o:void 0}return this._map.get(r)}has(r){return this._map.has(r)}}function Qo(){return new li}var Tr=Qo();function Bm(r,t){return new r({type:"string",...D(t)})}function ym(r,t){return new r({type:"string",coerce:!0,...D(t)})}function Fo(r,t){return new r({type:"string",format:"email",check:"string_format",abort:!1,...D(t)})}function ci(r,t){return new r({type:"string",format:"guid",check:"string_format",abort:!1,...D(t)})}function Go(r,t){return new r({type:"string",format:"uuid",check:"string_format",abort:!1,...D(t)})}function So(r,t){return new r({type:"string",format:"uuid",check:"string_format",abort:!1,version:"v4",...D(t)})}function No(r,t){return new r({type:"string",format:"uuid",check:"string_format",abort:!1,version:"v6",...D(t)})}function Bo(r,t){return new r({type:"string",format:"uuid",check:"string_format",abort:!1,version:"v7",...D(t)})}function ui(r,t){return new r({type:"string",format:"url",check:"string_format",abort:!1,...D(t)})}function yo(r,t){return new r({type:"string",format:"emoji",check:"string_format",abort:!1,...D(t)})}function Ao(r,t){return new r({type:"string",format:"nanoid",check:"string_format",abort:!1,...D(t)})}function Ho(r,t){return new r({type:"string",format:"cuid",check:"string_format",abort:!1,...D(t)})}function Ro(r,t){return new r({type:"string",format:"cuid2",check:"string_format",abort:!1,...D(t)})}function Mo(r,t){return new r({type:"string",format:"ulid",check:"string_format",abort:!1,...D(t)})}function Zo(r,t){return new r({type:"string",format:"xid",check:"string_format",abort:!1,...D(t)})}function Co(r,t){return new r({type:"string",format:"ksuid",check:"string_format",abort:!1,...D(t)})}function To(r,t){return new r({type:"string",format:"ipv4",check:"string_format",abort:!1,...D(t)})}function so(r,t){return new r({type:"string",format:"ipv6",check:"string_format",abort:!1,...D(t)})}function re(r,t){return new r({type:"string",format:"cidrv4",check:"string_format",abort:!1,...D(t)})}function te(r,t){return new r({type:"string",format:"cidrv6",check:"string_format",abort:!1,...D(t)})}function ne(r,t){return new r({type:"string",format:"base64",check:"string_format",abort:!1,...D(t)})}function ie(r,t){return new r({type:"string",format:"base64url",check:"string_format",abort:!1,...D(t)})}function oe(r,t){return new r({type:"string",format:"e164",check:"string_format",abort:!1,...D(t)})}function ee(r,t){return new r({type:"string",format:"jwt",check:"string_format",abort:!1,...D(t)})}var Am={Any:null,Minute:-1,Second:0,Millisecond:3,Microsecond:6};function Hm(r,t){return new r({type:"string",format:"datetime",check:"string_format",offset:!1,local:!1,precision:null,...D(t)})}function Rm(r,t){return new r({type:"string",format:"date",check:"string_format",...D(t)})}function Mm(r,t){return new r({type:"string",format:"time",check:"string_format",precision:null,...D(t)})}function Zm(r,t){return new r({type:"string",format:"duration",check:"string_format",...D(t)})}function Cm(r,t){return new r({type:"number",checks:[],...D(t)})}function Tm(r,t){return new r({type:"number",coerce:!0,checks:[],...D(t)})}function dm(r,t){return new r({type:"number",check:"number_format",abort:!1,format:"safeint",...D(t)})}function sm(r,t){return new r({type:"number",check:"number_format",abort:!1,format:"float32",...D(t)})}function rv(r,t){return new r({type:"number",check:"number_format",abort:!1,format:"float64",...D(t)})}function tv(r,t){return new r({type:"number",check:"number_format",abort:!1,format:"int32",...D(t)})}function nv(r,t){return new r({type:"number",check:"number_format",abort:!1,format:"uint32",...D(t)})}function iv(r,t){return new r({type:"boolean",...D(t)})}function ov(r,t){return new r({type:"boolean",coerce:!0,...D(t)})}function ev(r,t){return new r({type:"bigint",...D(t)})}function lv(r,t){return new r({type:"bigint",coerce:!0,...D(t)})}function cv(r,t){return new r({type:"bigint",check:"bigint_format",abort:!1,format:"int64",...D(t)})}function uv(r,t){return new r({type:"bigint",check:"bigint_format",abort:!1,format:"uint64",...D(t)})}function gv(r,t){return new r({type:"symbol",...D(t)})}function bv(r,t){return new r({type:"undefined",...D(t)})}function mv(r,t){return new r({type:"null",...D(t)})}function vv(r){return new r({type:"any"})}function hv(r){return new r({type:"unknown"})}function $v(r,t){return new r({type:"never",...D(t)})}function xv(r,t){return new r({type:"void",...D(t)})}function wv(r,t){return new r({type:"date",...D(t)})}function fv(r,t){return new r({type:"date",coerce:!0,...D(t)})}function zv(r,t){return new r({type:"nan",...D(t)})}function et(r,t){return new Uo({check:"less_than",...D(t),value:r,inclusive:!1})}function Er(r,t){return new Uo({check:"less_than",...D(t),value:r,inclusive:!0})}function lt(r,t){return new ko({check:"greater_than",...D(t),value:r,inclusive:!1})}function Ur(r,t){return new ko({check:"greater_than",...D(t),value:r,inclusive:!0})}function Dv(r){return lt(0,r)}function _v(r){return et(0,r)}function jv(r){return Er(0,r)}function Ov(r){return Ur(0,r)}function Kt(r,t){return new kg({check:"multiple_of",...D(t),value:r})}function mn(r,t){return new Pg({check:"max_size",...D(t),maximum:r})}function Vt(r,t){return new qg({check:"min_size",...D(t),minimum:r})}function gi(r,t){return new Wg({check:"size_equals",...D(t),size:r})}function vn(r,t){return new Kg({check:"max_length",...D(t),maximum:r})}function jt(r,t){return new Vg({check:"min_length",...D(t),minimum:r})}function hn(r,t){return new Lg({check:"length_equals",...D(t),length:r})}function bi(r,t){return new Yg({check:"string_format",format:"regex",...D(t),pattern:r})}function mi(r){return new Eg({check:"string_format",format:"lowercase",...D(r)})}function vi(r){return new Qg({check:"string_format",format:"uppercase",...D(r)})}function hi(r,t){return new Fg({check:"string_format",format:"includes",...D(t),includes:r})}function $i(r,t){return new Gg({check:"string_format",format:"starts_with",...D(t),prefix:r})}function xi(r,t){return new Sg({check:"string_format",format:"ends_with",...D(t),suffix:r})}function pv(r,t,i){return new Ng({check:"property",property:r,schema:t,...D(i)})}function wi(r,t){return new Bg({check:"mime_type",mime:r,...D(t)})}function ct(r){return new yg({check:"overwrite",tx:r})}function fi(r){return ct((t)=>t.normalize(r))}function zi(){return ct((r)=>r.trim())}function Di(){return ct((r)=>r.toLowerCase())}function _i(){return ct((r)=>r.toUpperCase())}function Iv(r,t,i){return new r({type:"array",element:t,...D(i)})}function AI(r,t,i){return new r({type:"union",options:t,...D(i)})}function HI(r,t,i,o){return new r({type:"union",options:i,discriminator:t,...D(o)})}function RI(r,t,i){return new r({type:"intersection",left:t,right:i})}function MI(r,t,i,o){let n=i instanceof V;return new r({type:"tuple",items:t,rest:n?i:null,...D(n?o:i)})}function ZI(r,t,i,o){return new r({type:"record",keyType:t,valueType:i,...D(o)})}function CI(r,t,i,o){return new r({type:"map",keyType:t,valueType:i,...D(o)})}function TI(r,t,i){return new r({type:"set",valueType:t,...D(i)})}function dI(r,t,i){let o=Array.isArray(t)?Object.fromEntries(t.map((n)=>[n,n])):t;return new r({type:"enum",entries:o,...D(i)})}function sI(r,t,i){return new r({type:"enum",entries:t,...D(i)})}function ra(r,t,i){return new r({type:"literal",values:Array.isArray(t)?t:[t],...D(i)})}function av(r,t){return new r({type:"file",...D(t)})}function ta(r,t){return new r({type:"transform",transform:t})}function na(r,t){return new r({type:"optional",innerType:t})}function ia(r,t){return new r({type:"nullable",innerType:t})}function oa(r,t,i){return new r({type:"default",innerType:t,get defaultValue(){return typeof i==="function"?i():Bu(i)}})}function ea(r,t,i){return new r({type:"nonoptional",innerType:t,...D(i)})}function la(r,t){return new r({type:"success",innerType:t})}function ca(r,t,i){return new r({type:"catch",innerType:t,catchValue:typeof i==="function"?i:()=>i})}function ua(r,t,i){return new r({type:"pipe",in:t,out:i})}function ga(r,t){return new r({type:"readonly",innerType:t})}function ba(r,t,i){return new r({type:"template_literal",parts:t,...D(i)})}function ma(r,t){return new r({type:"lazy",getter:t})}function va(r,t){return new r({type:"promise",innerType:t})}function Uv(r,t,i){let o=D(i);return o.abort??(o.abort=!0),new r({type:"custom",check:"custom",fn:t,...o})}function kv(r,t,i){return new r({type:"custom",check:"custom",fn:t,...D(i)})}function Jv(r){let t=Df((i)=>{return i.addIssue=(o)=>{if(typeof o==="string")i.issues.push(en(o,i.value,t._zod.def));else{let n=o;if(n.fatal)n.continue=!1;n.code??(n.code="custom"),n.input??(n.input=i.value),n.inst??(n.inst=t),n.continue??(n.continue=!t._zod.def.abort),i.issues.push(en(n))}},r(i.value,i)});return t}function Df(r,t){let i=new s({check:"custom",...D(t)});return i._zod.check=r,i}function Xv(r,t){let i=D(t),o=i.truthy??["true","1","yes","on","y","enabled"],n=i.falsy??["false","0","no","off","n","disabled"];if(i.case!=="sensitive")o=o.map((m)=>typeof m==="string"?m.toLowerCase():m),n=n.map((m)=>typeof m==="string"?m.toLowerCase():m);let e=new Set(o),l=new Set(n),u=r.Codec??ii,g=r.Boolean??ni,b=new(r.String??Wt)({type:"string",error:i.error}),v=new g({type:"boolean",error:i.error}),h=new u({type:"pipe",in:b,out:v,transform:(m,w)=>{let O=m;if(i.case!=="sensitive")O=O.toLowerCase();if(e.has(O))return!0;else if(l.has(O))return!1;else return w.issues.push({code:"invalid_value",expected:"stringbool",values:[...e,...l],input:w.value,inst:h,continue:!1}),{}},reverseTransform:(m,w)=>{if(m===!0)return o[0]||"true";else return n[0]||"false"},error:i.error});return h}function $n(r,t,i,o={}){let n=D(o),e={...D(o),check:"string_format",type:"string",format:t,fn:typeof i==="function"?i:(u)=>i.test(u),...n};if(i instanceof RegExp)e.pattern=i;return new r(e)}class le{constructor(r){this.counter=0,this.metadataRegistry=r?.metadata??Tr,this.target=r?.target??"draft-2020-12",this.unrepresentable=r?.unrepresentable??"throw",this.override=r?.override??(()=>{}),this.io=r?.io??"output",this.seen=new Map}process(r,t={path:[],schemaPath:[]}){var i;let o=r._zod.def,n={guid:"uuid",url:"uri",datetime:"date-time",json_string:"json-string",regex:""},e=this.seen.get(r);if(e){if(e.count++,t.schemaPath.includes(r))e.cycle=t.path;return e.schema}let l={schema:{},count:1,cycle:void 0,path:t.path};this.seen.set(r,l);let u=r._zod.toJSONSchema?.();if(u)l.schema=u;else{let b={...t,schemaPath:[...t.schemaPath,r],path:t.path},v=r._zod.parent;if(v)l.ref=v,this.process(v,b),this.seen.get(v).isParent=!0;else{let h=l.schema;switch(o.type){case"string":{let m=h;m.type="string";let{minimum:w,maximum:O,format:I,patterns:_,contentEncoding:J}=r._zod.bag;if(typeof w==="number")m.minLength=w;if(typeof O==="number")m.maxLength=O;if(I){if(m.format=n[I]??I,m.format==="")delete m.format}if(J)m.contentEncoding=J;if(_&&_.size>0){let L=[..._];if(L.length===1)m.pattern=L[0].source;else if(L.length>1)l.schema.allOf=[...L.map((P)=>({...this.target==="draft-7"||this.target==="draft-4"||this.target==="openapi-3.0"?{type:"string"}:{},pattern:P.source}))]}break}case"number":{let m=h,{minimum:w,maximum:O,format:I,multipleOf:_,exclusiveMaximum:J,exclusiveMinimum:L}=r._zod.bag;if(typeof I==="string"&&I.includes("int"))m.type="integer";else m.type="number";if(typeof L==="number")if(this.target==="draft-4"||this.target==="openapi-3.0")m.minimum=L,m.exclusiveMinimum=!0;else m.exclusiveMinimum=L;if(typeof w==="number"){if(m.minimum=w,typeof L==="number"&&this.target!=="draft-4")if(L>=w)delete m.minimum;else delete m.exclusiveMinimum}if(typeof J==="number")if(this.target==="draft-4"||this.target==="openapi-3.0")m.maximum=J,m.exclusiveMaximum=!0;else m.exclusiveMaximum=J;if(typeof O==="number"){if(m.maximum=O,typeof J==="number"&&this.target!=="draft-4")if(J<=O)delete m.maximum;else delete m.exclusiveMaximum}if(typeof _==="number")m.multipleOf=_;break}case"boolean":{let m=h;m.type="boolean";break}case"bigint":{if(this.unrepresentable==="throw")throw new Error("BigInt cannot be represented in JSON Schema");break}case"symbol":{if(this.unrepresentable==="throw")throw new Error("Symbols cannot be represented in JSON Schema");break}case"null":{if(this.target==="openapi-3.0")h.type="string",h.nullable=!0,h.enum=[null];else h.type="null";break}case"any":break;case"unknown":break;case"undefined":{if(this.unrepresentable==="throw")throw new Error("Undefined cannot be represented in JSON Schema");break}case"void":{if(this.unrepresentable==="throw")throw new Error("Void cannot be represented in JSON Schema");break}case"never":{h.not={};break}case"date":{if(this.unrepresentable==="throw")throw new Error("Date cannot be represented in JSON Schema");break}case"array":{let m=h,{minimum:w,maximum:O}=r._zod.bag;if(typeof w==="number")m.minItems=w;if(typeof O==="number")m.maxItems=O;m.type="array",m.items=this.process(o.element,{...b,path:[...b.path,"items"]});break}case"object":{let m=h;m.type="object",m.properties={};let w=o.shape;for(let _ in w)m.properties[_]=this.process(w[_],{...b,path:[...b.path,"properties",_]});let O=new Set(Object.keys(w)),I=new Set([...O].filter((_)=>{let J=o.shape[_]._zod;if(this.io==="input")return J.optin===void 0;else return J.optout===void 0}));if(I.size>0)m.required=Array.from(I);if(o.catchall?._zod.def.type==="never")m.additionalProperties=!1;else if(!o.catchall){if(this.io==="output")m.additionalProperties=!1}else if(o.catchall)m.additionalProperties=this.process(o.catchall,{...b,path:[...b.path,"additionalProperties"]});break}case"union":{let m=h,w=o.options.map((O,I)=>this.process(O,{...b,path:[...b.path,"anyOf",I]}));m.anyOf=w;break}case"intersection":{let m=h,w=this.process(o.left,{...b,path:[...b.path,"allOf",0]}),O=this.process(o.right,{...b,path:[...b.path,"allOf",1]}),I=(J)=>("allOf"in J)&&Object.keys(J).length===1,_=[...I(w)?w.allOf:[w],...I(O)?O.allOf:[O]];m.allOf=_;break}case"tuple":{let m=h;m.type="array";let w=this.target==="draft-2020-12"?"prefixItems":"items",O=this.target==="draft-2020-12"?"items":this.target==="openapi-3.0"?"items":"additionalItems",I=o.items.map((P,W)=>this.process(P,{...b,path:[...b.path,w,W]})),_=o.rest?this.process(o.rest,{...b,path:[...b.path,O,...this.target==="openapi-3.0"?[o.items.length]:[]]}):null;if(this.target==="draft-2020-12"){if(m.prefixItems=I,_)m.items=_}else if(this.target==="openapi-3.0"){if(m.items={anyOf:I},_)m.items.anyOf.push(_);if(m.minItems=I.length,!_)m.maxItems=I.length}else if(m.items=I,_)m.additionalItems=_;let{minimum:J,maximum:L}=r._zod.bag;if(typeof J==="number")m.minItems=J;if(typeof L==="number")m.maxItems=L;break}case"record":{let m=h;if(m.type="object",this.target==="draft-7"||this.target==="draft-2020-12")m.propertyNames=this.process(o.keyType,{...b,path:[...b.path,"propertyNames"]});m.additionalProperties=this.process(o.valueType,{...b,path:[...b.path,"additionalProperties"]});break}case"map":{if(this.unrepresentable==="throw")throw new Error("Map cannot be represented in JSON Schema");break}case"set":{if(this.unrepresentable==="throw")throw new Error("Set cannot be represented in JSON Schema");break}case"enum":{let m=h,w=Hn(o.entries);if(w.every((O)=>typeof O==="number"))m.type="number";if(w.every((O)=>typeof O==="string"))m.type="string";m.enum=w;break}case"literal":{let m=h,w=[];for(let O of o.values)if(O===void 0){if(this.unrepresentable==="throw")throw new Error("Literal `undefined` cannot be represented in JSON Schema")}else if(typeof O==="bigint")if(this.unrepresentable==="throw")throw new Error("BigInt literals cannot be represented in JSON Schema");else w.push(Number(O));else w.push(O);if(w.length===0);else if(w.length===1){let O=w[0];if(m.type=O===null?"null":typeof O,this.target==="draft-4"||this.target==="openapi-3.0")m.enum=[O];else m.const=O}else{if(w.every((O)=>typeof O==="number"))m.type="number";if(w.every((O)=>typeof O==="string"))m.type="string";if(w.every((O)=>typeof O==="boolean"))m.type="string";if(w.every((O)=>O===null))m.type="null";m.enum=w}break}case"file":{let m=h,w={type:"string",format:"binary",contentEncoding:"binary"},{minimum:O,maximum:I,mime:_}=r._zod.bag;if(O!==void 0)w.minLength=O;if(I!==void 0)w.maxLength=I;if(_)if(_.length===1)w.contentMediaType=_[0],Object.assign(m,w);else m.anyOf=_.map((J)=>{return{...w,contentMediaType:J}});else Object.assign(m,w);break}case"transform":{if(this.unrepresentable==="throw")throw new Error("Transforms cannot be represented in JSON Schema");break}case"nullable":{let m=this.process(o.innerType,b);if(this.target==="openapi-3.0")l.ref=o.innerType,h.nullable=!0;else h.anyOf=[m,{type:"null"}];break}case"nonoptional":{this.process(o.innerType,b),l.ref=o.innerType;break}case"success":{let m=h;m.type="boolean";break}case"default":{this.process(o.innerType,b),l.ref=o.innerType,h.default=JSON.parse(JSON.stringify(o.defaultValue));break}case"prefault":{if(this.process(o.innerType,b),l.ref=o.innerType,this.io==="input")h._prefault=JSON.parse(JSON.stringify(o.defaultValue));break}case"catch":{this.process(o.innerType,b),l.ref=o.innerType;let m;try{m=o.catchValue(void 0)}catch{throw new Error("Dynamic catch values are not supported in JSON Schema")}h.default=m;break}case"nan":{if(this.unrepresentable==="throw")throw new Error("NaN cannot be represented in JSON Schema");break}case"template_literal":{let m=h,w=r._zod.pattern;if(!w)throw new Error("Pattern not found in template literal");m.type="string",m.pattern=w.source;break}case"pipe":{let m=this.io==="input"?o.in._zod.def.type==="transform"?o.out:o.in:o.out;this.process(m,b),l.ref=m;break}case"readonly":{this.process(o.innerType,b),l.ref=o.innerType,h.readOnly=!0;break}case"promise":{this.process(o.innerType,b),l.ref=o.innerType;break}case"optional":{this.process(o.innerType,b),l.ref=o.innerType;break}case"lazy":{let m=r._zod.innerType;this.process(m,b),l.ref=m;break}case"custom":{if(this.unrepresentable==="throw")throw new Error("Custom types cannot be represented in JSON Schema");break}case"function":{if(this.unrepresentable==="throw")throw new Error("Function types cannot be represented in JSON Schema");break}default:}}}let g=this.metadataRegistry.get(r);if(g)Object.assign(l.schema,g);if(this.io==="input"&&$r(r))delete l.schema.examples,delete l.schema.default;if(this.io==="input"&&l.schema._prefault)(i=l.schema).default??(i.default=l.schema._prefault);return delete l.schema._prefault,this.seen.get(r).schema}emit(r,t){let i={cycles:t?.cycles??"ref",reused:t?.reused??"inline",external:t?.external??void 0},o=this.seen.get(r);if(!o)throw new Error("Unprocessed schema. This is a bug in Zod.");let n=(c)=>{let b=this.target==="draft-2020-12"?"$defs":"definitions";if(i.external){let w=i.external.registry.get(c[0])?.id,O=i.external.uri??((_)=>_);if(w)return{ref:O(w)};let I=c[1].defId??c[1].schema.id??`schema${this.counter++}`;return c[1].defId=I,{defId:I,ref:`${O("__shared")}#/${b}/${I}`}}if(c[1]===o)return{ref:"#"};let h=`${"#"}/${b}/`,m=c[1].schema.id??`__schema${this.counter++}`;return{defId:m,ref:h+m}},e=(c)=>{if(c[1].schema.$ref)return;let b=c[1],{ref:v,defId:h}=n(c);if(b.def={...b.schema},h)b.defId=h;let m=b.schema;for(let w in m)delete m[w];m.$ref=v};if(i.cycles==="throw")for(let c of this.seen.entries()){let b=c[1];if(b.cycle)throw new Error(`Cycle detected: #/${b.cycle?.join("/")}/<root>

Set the \`cycles\` parameter to \`"ref"\` to resolve cyclical schemas with defs.`)}for(let c of this.seen.entries()){let b=c[1];if(r===c[0]){e(c);continue}if(i.external){let h=i.external.registry.get(c[0])?.id;if(r!==c[0]&&h){e(c);continue}}if(this.metadataRegistry.get(c[0])?.id){e(c);continue}if(b.cycle){e(c);continue}if(b.count>1){if(i.reused==="ref"){e(c);continue}}}let l=(c,b)=>{let v=this.seen.get(c),h=v.def??v.schema,m={...h};if(v.ref===null)return;let w=v.ref;if(v.ref=null,w){l(w,b);let O=this.seen.get(w).schema;if(O.$ref&&(b.target==="draft-7"||b.target==="draft-4"||b.target==="openapi-3.0"))h.allOf=h.allOf??[],h.allOf.push(O);else Object.assign(h,O),Object.assign(h,m)}if(!v.isParent)this.override({zodSchema:c,jsonSchema:h,path:v.path??[]})};for(let c of[...this.seen.entries()].reverse())l(c[0],{target:this.target});let u={};if(this.target==="draft-2020-12")u.$schema="https://json-schema.org/draft/2020-12/schema";else if(this.target==="draft-7")u.$schema="http://json-schema.org/draft-07/schema#";else if(this.target==="draft-4")u.$schema="http://json-schema.org/draft-04/schema#";else if(this.target==="openapi-3.0");else console.warn(`Invalid target: ${this.target}`);if(i.external?.uri){let c=i.external.registry.get(r)?.id;if(!c)throw new Error("Schema is missing an `id` property");u.$id=i.external.uri(c)}Object.assign(u,o.def);let g=i.external?.defs??{};for(let c of this.seen.entries()){let b=c[1];if(b.def&&b.defId)g[b.defId]=b.def}if(i.external);else if(Object.keys(g).length>0)if(this.target==="draft-2020-12")u.$defs=g;else u.definitions=g;try{return JSON.parse(JSON.stringify(u))}catch(c){throw new Error("Error converting schema to JSON.")}}}function Pv(r,t){if(r instanceof li){let o=new le(t),n={};for(let u of r._idmap.entries()){let[g,c]=u;o.process(c)}let e={},l={registry:r,uri:t?.uri,defs:n};for(let u of r._idmap.entries()){let[g,c]=u;e[g]=o.emit(c,{...t,external:l})}if(Object.keys(n).length>0){let u=o.target==="draft-2020-12"?"$defs":"definitions";e.__shared={[u]:n}}return{schemas:e}}let i=new le(t);return i.process(r),i.emit(r,t)}function $r(r,t){let i=t??{seen:new Set};if(i.seen.has(r))return!1;i.seen.add(r);let n=r._zod.def;switch(n.type){case"string":case"number":case"bigint":case"boolean":case"date":case"symbol":case"undefined":case"null":case"any":case"unknown":case"never":case"void":case"literal":case"enum":case"nan":case"file":case"template_literal":return!1;case"array":return $r(n.element,i);case"object":{for(let e in n.shape)if($r(n.shape[e],i))return!0;return!1}case"union":{for(let e of n.options)if($r(e,i))return!0;return!1}case"intersection":return $r(n.left,i)||$r(n.right,i);case"tuple":{for(let e of n.items)if($r(e,i))return!0;if(n.rest&&$r(n.rest,i))return!0;return!1}case"record":return $r(n.keyType,i)||$r(n.valueType,i);case"map":return $r(n.keyType,i)||$r(n.valueType,i);case"set":return $r(n.valueType,i);case"promise":case"optional":case"nonoptional":case"nullable":case"readonly":return $r(n.innerType,i);case"lazy":return $r(n.getter(),i);case"default":return $r(n.innerType,i);case"prefault":return $r(n.innerType,i);case"custom":return!1;case"transform":return!0;case"pipe":return $r(n.in,i)||$r(n.out,i);case"success":return!1;case"catch":return!1;case"function":return!1;default:}throw new Error(`Unknown schema type: ${n.type}`)}var _f={};var me={};a(me,{time:()=>Kv,duration:()=>Vv,datetime:()=>qv,date:()=>Wv,ZodISOTime:()=>ge,ZodISODuration:()=>be,ZodISODateTime:()=>ce,ZodISODate:()=>ue});var ce=$("ZodISODateTime",(r,t)=>{ob.init(r,t),T.init(r,t)});function qv(r){return Hm(ce,r)}var ue=$("ZodISODate",(r,t)=>{eb.init(r,t),T.init(r,t)});function Wv(r){return Rm(ue,r)}var ge=$("ZodISOTime",(r,t)=>{lb.init(r,t),T.init(r,t)});function Kv(r){return Mm(ge,r)}var be=$("ZodISODuration",(r,t)=>{cb.init(r,t),T.init(r,t)});function Vv(r){return Zm(be,r)}var Of=(r,t)=>{Tn.init(r,t),r.name="ZodError",Object.defineProperties(r,{format:{value:(i)=>sn(r,i)},flatten:{value:(i)=>dn(r,i)},addIssue:{value:(i)=>{r.issues.push(i),r.message=JSON.stringify(r.issues,nn,2)}},addIssues:{value:(i)=>{r.issues.push(...i),r.message=JSON.stringify(r.issues,nn,2)}},isEmpty:{get(){return r.issues.length===0}}})},$a=$("ZodError",Of),kr=$("ZodError",Of,{Parent:Error});var Lv=ln(kr),Yv=cn(kr),Ev=un(kr),Qv=gn(kr),Fv=fo(kr),Gv=zo(kr),Sv=Do(kr),Nv=_o(kr),Bv=jo(kr),yv=Oo(kr),Av=po(kr),Hv=Io(kr);var E=$("ZodType",(r,t)=>{return V.init(r,t),r.def=t,r.type=t.type,Object.defineProperty(r,"_def",{value:t}),r.check=(...i)=>{return r.clone({...t,checks:[...t.checks??[],...i.map((o)=>typeof o==="function"?{_zod:{check:o,def:{check:"custom"},onattach:[]}}:o)]})},r.clone=(i,o)=>pr(r,i,o),r.brand=()=>r,r.register=(i,o)=>{return i.add(r,o),r},r.parse=(i,o)=>Lv(r,i,o,{callee:r.parse}),r.safeParse=(i,o)=>Ev(r,i,o),r.parseAsync=async(i,o)=>Yv(r,i,o,{callee:r.parseAsync}),r.safeParseAsync=async(i,o)=>Qv(r,i,o),r.spa=r.safeParseAsync,r.encode=(i,o)=>Fv(r,i,o),r.decode=(i,o)=>Gv(r,i,o),r.encodeAsync=async(i,o)=>Sv(r,i,o),r.decodeAsync=async(i,o)=>Nv(r,i,o),r.safeEncode=(i,o)=>Bv(r,i,o),r.safeDecode=(i,o)=>yv(r,i,o),r.safeEncodeAsync=async(i,o)=>Av(r,i,o),r.safeDecodeAsync=async(i,o)=>Hv(r,i,o),r.refine=(i,o)=>r.check(u4(i,o)),r.superRefine=(i)=>r.check(g4(i)),r.overwrite=(i)=>r.check(ct(i)),r.optional=()=>he(r),r.nullable=()=>$e(r),r.nullish=()=>he($e(r)),r.nonoptional=(i)=>Cf(r,i),r.array=()=>ze(r),r.or=(i)=>xh([r,i]),r.and=(i)=>Yf(r,i),r.transform=(i)=>xe(r,zh(i)),r.default=(i)=>Rf(r,i),r.prefault=(i)=>Zf(r,i),r.catch=(i)=>sf(r,i),r.pipe=(i)=>xe(r,i),r.readonly=()=>n4(r),r.describe=(i)=>{let o=r.clone();return Tr.add(o,{description:i}),o},Object.defineProperty(r,"description",{get(){return Tr.get(r)?.description},configurable:!0}),r.meta=(...i)=>{if(i.length===0)return Tr.get(r);let o=r.clone();return Tr.add(o,i[0]),o},r.isOptional=()=>r.safeParse(void 0).success,r.isNullable=()=>r.safeParse(null).success,r}),Zv=$("_ZodString",(r,t)=>{Wt.init(r,t),E.init(r,t);let i=r._zod.bag;r.format=i.format??null,r.minLength=i.minimum??null,r.maxLength=i.maximum??null,r.regex=(...o)=>r.check(bi(...o)),r.includes=(...o)=>r.check(hi(...o)),r.startsWith=(...o)=>r.check($i(...o)),r.endsWith=(...o)=>r.check(xi(...o)),r.min=(...o)=>r.check(jt(...o)),r.max=(...o)=>r.check(vn(...o)),r.length=(...o)=>r.check(hn(...o)),r.nonempty=(...o)=>r.check(jt(1,...o)),r.lowercase=(o)=>r.check(mi(o)),r.uppercase=(o)=>r.check(vi(o)),r.trim=()=>r.check(zi()),r.normalize=(...o)=>r.check(fi(...o)),r.toLowerCase=()=>r.check(Di()),r.toUpperCase=()=>r.check(_i())}),Oi=$("ZodString",(r,t)=>{Wt.init(r,t),Zv.init(r,t),r.email=(i)=>r.check(Fo(Cv,i)),r.url=(i)=>r.check(ui(we,i)),r.jwt=(i)=>r.check(ee(mh,i)),r.emoji=(i)=>r.check(yo(Tv,i)),r.guid=(i)=>r.check(ci(ve,i)),r.uuid=(i)=>r.check(Go(gt,i)),r.uuidv4=(i)=>r.check(So(gt,i)),r.uuidv6=(i)=>r.check(No(gt,i)),r.uuidv7=(i)=>r.check(Bo(gt,i)),r.nanoid=(i)=>r.check(Ao(dv,i)),r.guid=(i)=>r.check(ci(ve,i)),r.cuid=(i)=>r.check(Ho(sv,i)),r.cuid2=(i)=>r.check(Ro(rh,i)),r.ulid=(i)=>r.check(Mo(th,i)),r.base64=(i)=>r.check(ne(uh,i)),r.base64url=(i)=>r.check(ie(gh,i)),r.xid=(i)=>r.check(Zo(nh,i)),r.ksuid=(i)=>r.check(Co(ih,i)),r.ipv4=(i)=>r.check(To(oh,i)),r.ipv6=(i)=>r.check(so(eh,i)),r.cidrv4=(i)=>r.check(re(lh,i)),r.cidrv6=(i)=>r.check(te(ch,i)),r.e164=(i)=>r.check(oe(bh,i)),r.datetime=(i)=>r.check(qv(i)),r.date=(i)=>r.check(Wv(i)),r.time=(i)=>r.check(Kv(i)),r.duration=(i)=>r.check(Vv(i))});function Rv(r){return Bm(Oi,r)}var T=$("ZodStringFormat",(r,t)=>{M.init(r,t),Zv.init(r,t)}),Cv=$("ZodEmail",(r,t)=>{Zg.init(r,t),T.init(r,t)});function wa(r){return Fo(Cv,r)}var ve=$("ZodGUID",(r,t)=>{Rg.init(r,t),T.init(r,t)});function fa(r){return ci(ve,r)}var gt=$("ZodUUID",(r,t)=>{Mg.init(r,t),T.init(r,t)});function za(r){return Go(gt,r)}function Da(r){return So(gt,r)}function _a(r){return No(gt,r)}function ja(r){return Bo(gt,r)}var we=$("ZodURL",(r,t)=>{Cg.init(r,t),T.init(r,t)});function Oa(r){return ui(we,r)}function pa(r){return ui(we,{protocol:/^https?$/,hostname:Yr.domain,...z.normalizeParams(r)})}var Tv=$("ZodEmoji",(r,t)=>{Tg.init(r,t),T.init(r,t)});function Ia(r){return yo(Tv,r)}var dv=$("ZodNanoID",(r,t)=>{dg.init(r,t),T.init(r,t)});function aa(r){return Ao(dv,r)}var sv=$("ZodCUID",(r,t)=>{sg.init(r,t),T.init(r,t)});function Ua(r){return Ho(sv,r)}var rh=$("ZodCUID2",(r,t)=>{rb.init(r,t),T.init(r,t)});function ka(r){return Ro(rh,r)}var th=$("ZodULID",(r,t)=>{tb.init(r,t),T.init(r,t)});function Ja(r){return Mo(th,r)}var nh=$("ZodXID",(r,t)=>{nb.init(r,t),T.init(r,t)});function Xa(r){return Zo(nh,r)}var ih=$("ZodKSUID",(r,t)=>{ib.init(r,t),T.init(r,t)});function Pa(r){return Co(ih,r)}var oh=$("ZodIPv4",(r,t)=>{ub.init(r,t),T.init(r,t)});function qa(r){return To(oh,r)}var eh=$("ZodIPv6",(r,t)=>{gb.init(r,t),T.init(r,t)});function Wa(r){return so(eh,r)}var lh=$("ZodCIDRv4",(r,t)=>{bb.init(r,t),T.init(r,t)});function Ka(r){return re(lh,r)}var ch=$("ZodCIDRv6",(r,t)=>{mb.init(r,t),T.init(r,t)});function Va(r){return te(ch,r)}var uh=$("ZodBase64",(r,t)=>{hb.init(r,t),T.init(r,t)});function La(r){return ne(uh,r)}var gh=$("ZodBase64URL",(r,t)=>{$b.init(r,t),T.init(r,t)});function Ya(r){return ie(gh,r)}var bh=$("ZodE164",(r,t)=>{xb.init(r,t),T.init(r,t)});function Ea(r){return oe(bh,r)}var mh=$("ZodJWT",(r,t)=>{wb.init(r,t),T.init(r,t)});function Qa(r){return ee(mh,r)}var pi=$("ZodCustomStringFormat",(r,t)=>{fb.init(r,t),T.init(r,t)});function Fa(r,t,i={}){return $n(pi,r,t,i)}function Ga(r){return $n(pi,"hostname",Yr.hostname,r)}function Sa(r){return $n(pi,"hex",Yr.hex,r)}function Na(r,t){let i=t?.enc??"hex",o=`${r}_${i}`,n=Yr[o];if(!n)throw new Error(`Unrecognized hash format: ${o}`);return $n(pi,o,n,t)}var Ii=$("ZodNumber",(r,t)=>{Vo.init(r,t),E.init(r,t),r.gt=(o,n)=>r.check(lt(o,n)),r.gte=(o,n)=>r.check(Ur(o,n)),r.min=(o,n)=>r.check(Ur(o,n)),r.lt=(o,n)=>r.check(et(o,n)),r.lte=(o,n)=>r.check(Er(o,n)),r.max=(o,n)=>r.check(Er(o,n)),r.int=(o)=>r.check(Mv(o)),r.safe=(o)=>r.check(Mv(o)),r.positive=(o)=>r.check(lt(0,o)),r.nonnegative=(o)=>r.check(Ur(0,o)),r.negative=(o)=>r.check(et(0,o)),r.nonpositive=(o)=>r.check(Er(0,o)),r.multipleOf=(o,n)=>r.check(Kt(o,n)),r.step=(o,n)=>r.check(Kt(o,n)),r.finite=()=>r;let i=r._zod.bag;r.minValue=Math.max(i.minimum??Number.NEGATIVE_INFINITY,i.exclusiveMinimum??Number.NEGATIVE_INFINITY)??null,r.maxValue=Math.min(i.maximum??Number.POSITIVE_INFINITY,i.exclusiveMaximum??Number.POSITIVE_INFINITY)??null,r.isInt=(i.format??"").includes("int")||Number.isSafeInteger(i.multipleOf??0.5),r.isFinite=!0,r.format=i.format??null});function pf(r){return Cm(Ii,r)}var wn=$("ZodNumberFormat",(r,t)=>{zb.init(r,t),Ii.init(r,t)});function Mv(r){return dm(wn,r)}function Ba(r){return sm(wn,r)}function ya(r){return rv(wn,r)}function Aa(r){return tv(wn,r)}function Ha(r){return nv(wn,r)}var ai=$("ZodBoolean",(r,t)=>{ni.init(r,t),E.init(r,t)});function If(r){return iv(ai,r)}var Ui=$("ZodBigInt",(r,t)=>{Lo.init(r,t),E.init(r,t),r.gte=(o,n)=>r.check(Ur(o,n)),r.min=(o,n)=>r.check(Ur(o,n)),r.gt=(o,n)=>r.check(lt(o,n)),r.gte=(o,n)=>r.check(Ur(o,n)),r.min=(o,n)=>r.check(Ur(o,n)),r.lt=(o,n)=>r.check(et(o,n)),r.lte=(o,n)=>r.check(Er(o,n)),r.max=(o,n)=>r.check(Er(o,n)),r.positive=(o)=>r.check(lt(BigInt(0),o)),r.negative=(o)=>r.check(et(BigInt(0),o)),r.nonpositive=(o)=>r.check(Er(BigInt(0),o)),r.nonnegative=(o)=>r.check(Ur(BigInt(0),o)),r.multipleOf=(o,n)=>r.check(Kt(o,n));let i=r._zod.bag;r.minValue=i.minimum??null,r.maxValue=i.maximum??null,r.format=i.format??null});function Ra(r){return ev(Ui,r)}var vh=$("ZodBigIntFormat",(r,t)=>{Db.init(r,t),Ui.init(r,t)});function Ma(r){return cv(vh,r)}function Za(r){return uv(vh,r)}var af=$("ZodSymbol",(r,t)=>{_b.init(r,t),E.init(r,t)});function Ca(r){return gv(af,r)}var Uf=$("ZodUndefined",(r,t)=>{jb.init(r,t),E.init(r,t)});function Ta(r){return bv(Uf,r)}var kf=$("ZodNull",(r,t)=>{Ob.init(r,t),E.init(r,t)});function Jf(r){return mv(kf,r)}var Xf=$("ZodAny",(r,t)=>{pb.init(r,t),E.init(r,t)});function da(){return vv(Xf)}var Pf=$("ZodUnknown",(r,t)=>{Ib.init(r,t),E.init(r,t)});function xn(){return hv(Pf)}var qf=$("ZodNever",(r,t)=>{ab.init(r,t),E.init(r,t)});function hh(r){return $v(qf,r)}var Wf=$("ZodVoid",(r,t)=>{Ub.init(r,t),E.init(r,t)});function sa(r){return xv(Wf,r)}var fe=$("ZodDate",(r,t)=>{kb.init(r,t),E.init(r,t),r.min=(o,n)=>r.check(Ur(o,n)),r.max=(o,n)=>r.check(Er(o,n));let i=r._zod.bag;r.minDate=i.minimum?new Date(i.minimum):null,r.maxDate=i.maximum?new Date(i.maximum):null});function r3(r){return wv(fe,r)}var Kf=$("ZodArray",(r,t)=>{Jb.init(r,t),E.init(r,t),r.element=t.element,r.min=(i,o)=>r.check(jt(i,o)),r.nonempty=(i)=>r.check(jt(1,i)),r.max=(i,o)=>r.check(vn(i,o)),r.length=(i,o)=>r.check(hn(i,o)),r.unwrap=()=>r.element});function ze(r,t){return Iv(Kf,r,t)}function t3(r){let t=r._zod.def.shape;return fh(Object.keys(t))}var De=$("ZodObject",(r,t)=>{Xb.init(r,t),E.init(r,t),z.defineLazy(r,"shape",()=>t.shape),r.keyof=()=>fh(Object.keys(r._zod.def.shape)),r.catchall=(i)=>r.clone({...r._zod.def,catchall:i}),r.passthrough=()=>r.clone({...r._zod.def,catchall:xn()}),r.loose=()=>r.clone({...r._zod.def,catchall:xn()}),r.strict=()=>r.clone({...r._zod.def,catchall:hh()}),r.strip=()=>r.clone({...r._zod.def,catchall:void 0}),r.extend=(i)=>{return z.extend(r,i)},r.safeExtend=(i)=>{return z.safeExtend(r,i)},r.merge=(i)=>z.merge(r,i),r.pick=(i)=>z.pick(r,i),r.omit=(i)=>z.omit(r,i),r.partial=(...i)=>z.partial(Dh,r,i[0]),r.required=(...i)=>z.required(_h,r,i[0])});function n3(r,t){let i={type:"object",get shape(){return z.assignProp(this,"shape",r?z.objectClone(r):{}),this.shape},...z.normalizeParams(t)};return new De(i)}function i3(r,t){return new De({type:"object",get shape(){return z.assignProp(this,"shape",z.objectClone(r)),this.shape},catchall:hh(),...z.normalizeParams(t)})}function o3(r,t){return new De({type:"object",get shape(){return z.assignProp(this,"shape",z.objectClone(r)),this.shape},catchall:xn(),...z.normalizeParams(t)})}var $h=$("ZodUnion",(r,t)=>{Yo.init(r,t),E.init(r,t),r.options=t.options});function xh(r,t){return new $h({type:"union",options:r,...z.normalizeParams(t)})}var Vf=$("ZodDiscriminatedUnion",(r,t)=>{$h.init(r,t),Pb.init(r,t)});function e3(r,t,i){return new Vf({type:"union",options:t,discriminator:r,...z.normalizeParams(i)})}var Lf=$("ZodIntersection",(r,t)=>{qb.init(r,t),E.init(r,t)});function Yf(r,t){return new Lf({type:"intersection",left:r,right:t})}var Ef=$("ZodTuple",(r,t)=>{Eo.init(r,t),E.init(r,t),r.rest=(i)=>r.clone({...r._zod.def,rest:i})});function Qf(r,t,i){let o=t instanceof V,n=o?i:t;return new Ef({type:"tuple",items:r,rest:o?t:null,...z.normalizeParams(n)})}var wh=$("ZodRecord",(r,t)=>{Wb.init(r,t),E.init(r,t),r.keyType=t.keyType,r.valueType=t.valueType});function Ff(r,t,i){return new wh({type:"record",keyType:r,valueType:t,...z.normalizeParams(i)})}function l3(r,t,i){let o=pr(r);return o._zod.values=void 0,new wh({type:"record",keyType:o,valueType:t,...z.normalizeParams(i)})}var Gf=$("ZodMap",(r,t)=>{Kb.init(r,t),E.init(r,t),r.keyType=t.keyType,r.valueType=t.valueType});function c3(r,t,i){return new Gf({type:"map",keyType:r,valueType:t,...z.normalizeParams(i)})}var Sf=$("ZodSet",(r,t)=>{Vb.init(r,t),E.init(r,t),r.min=(...i)=>r.check(Vt(...i)),r.nonempty=(i)=>r.check(Vt(1,i)),r.max=(...i)=>r.check(mn(...i)),r.size=(...i)=>r.check(gi(...i))});function u3(r,t){return new Sf({type:"set",valueType:r,...z.normalizeParams(t)})}var ji=$("ZodEnum",(r,t)=>{Lb.init(r,t),E.init(r,t),r.enum=t.entries,r.options=Object.values(t.entries);let i=new Set(Object.keys(t.entries));r.extract=(o,n)=>{let e={};for(let l of o)if(i.has(l))e[l]=t.entries[l];else throw new Error(`Key ${l} not found in enum`);return new ji({...t,checks:[],...z.normalizeParams(n),entries:e})},r.exclude=(o,n)=>{let e={...t.entries};for(let l of o)if(i.has(l))delete e[l];else throw new Error(`Key ${l} not found in enum`);return new ji({...t,checks:[],...z.normalizeParams(n),entries:e})}});function fh(r,t){let i=Array.isArray(r)?Object.fromEntries(r.map((o)=>[o,o])):r;return new ji({type:"enum",entries:i,...z.normalizeParams(t)})}function g3(r,t){return new ji({type:"enum",entries:r,...z.normalizeParams(t)})}var Nf=$("ZodLiteral",(r,t)=>{Yb.init(r,t),E.init(r,t),r.values=new Set(t.values),Object.defineProperty(r,"value",{get(){if(t.values.length>1)throw new Error("This schema contains multiple valid literal values. Use `.values` instead.");return t.values[0]}})});function b3(r,t){return new Nf({type:"literal",values:Array.isArray(r)?r:[r],...z.normalizeParams(t)})}var Bf=$("ZodFile",(r,t)=>{Eb.init(r,t),E.init(r,t),r.min=(i,o)=>r.check(Vt(i,o)),r.max=(i,o)=>r.check(mn(i,o)),r.mime=(i,o)=>r.check(wi(Array.isArray(i)?i:[i],o))});function m3(r){return av(Bf,r)}var yf=$("ZodTransform",(r,t)=>{Qb.init(r,t),E.init(r,t),r._zod.parse=(i,o)=>{if(o.direction==="backward")throw new Xt(r.constructor.name);i.addIssue=(e)=>{if(typeof e==="string")i.issues.push(z.issue(e,i.value,t));else{let l=e;if(l.fatal)l.continue=!1;l.code??(l.code="custom"),l.input??(l.input=i.value),l.inst??(l.inst=r),i.issues.push(z.issue(l))}};let n=t.transform(i.value,i);if(n instanceof Promise)return n.then((e)=>{return i.value=e,i});return i.value=n,i}});function zh(r){return new yf({type:"transform",transform:r})}var Dh=$("ZodOptional",(r,t)=>{Fb.init(r,t),E.init(r,t),r.unwrap=()=>r._zod.def.innerType});function he(r){return new Dh({type:"optional",innerType:r})}var Af=$("ZodNullable",(r,t)=>{Gb.init(r,t),E.init(r,t),r.unwrap=()=>r._zod.def.innerType});function $e(r){return new Af({type:"nullable",innerType:r})}function v3(r){return he($e(r))}var Hf=$("ZodDefault",(r,t)=>{Sb.init(r,t),E.init(r,t),r.unwrap=()=>r._zod.def.innerType,r.removeDefault=r.unwrap});function Rf(r,t){return new Hf({type:"default",innerType:r,get defaultValue(){return typeof t==="function"?t():z.shallowClone(t)}})}var Mf=$("ZodPrefault",(r,t)=>{Nb.init(r,t),E.init(r,t),r.unwrap=()=>r._zod.def.innerType});function Zf(r,t){return new Mf({type:"prefault",innerType:r,get defaultValue(){return typeof t==="function"?t():z.shallowClone(t)}})}var _h=$("ZodNonOptional",(r,t)=>{Bb.init(r,t),E.init(r,t),r.unwrap=()=>r._zod.def.innerType});function Cf(r,t){return new _h({type:"nonoptional",innerType:r,...z.normalizeParams(t)})}var Tf=$("ZodSuccess",(r,t)=>{yb.init(r,t),E.init(r,t),r.unwrap=()=>r._zod.def.innerType});function h3(r){return new Tf({type:"success",innerType:r})}var df=$("ZodCatch",(r,t)=>{Ab.init(r,t),E.init(r,t),r.unwrap=()=>r._zod.def.innerType,r.removeCatch=r.unwrap});function sf(r,t){return new df({type:"catch",innerType:r,catchValue:typeof t==="function"?t:()=>t})}var r4=$("ZodNaN",(r,t)=>{Hb.init(r,t),E.init(r,t)});function $3(r){return zv(r4,r)}var jh=$("ZodPipe",(r,t)=>{Rb.init(r,t),E.init(r,t),r.in=t.in,r.out=t.out});function xe(r,t){return new jh({type:"pipe",in:r,out:t})}var Oh=$("ZodCodec",(r,t)=>{jh.init(r,t),ii.init(r,t)});function x3(r,t,i){return new Oh({type:"pipe",in:r,out:t,transform:i.decode,reverseTransform:i.encode})}var t4=$("ZodReadonly",(r,t)=>{Mb.init(r,t),E.init(r,t),r.unwrap=()=>r._zod.def.innerType});function n4(r){return new t4({type:"readonly",innerType:r})}var i4=$("ZodTemplateLiteral",(r,t)=>{Zb.init(r,t),E.init(r,t)});function w3(r,t){return new i4({type:"template_literal",parts:r,...z.normalizeParams(t)})}var o4=$("ZodLazy",(r,t)=>{db.init(r,t),E.init(r,t),r.unwrap=()=>r._zod.def.getter()});function e4(r){return new o4({type:"lazy",getter:r})}var l4=$("ZodPromise",(r,t)=>{Tb.init(r,t),E.init(r,t),r.unwrap=()=>r._zod.def.innerType});function f3(r){return new l4({type:"promise",innerType:r})}var c4=$("ZodFunction",(r,t)=>{Cb.init(r,t),E.init(r,t)});function z3(r){return new c4({type:"function",input:Array.isArray(r?.input)?Qf(r?.input):r?.input??ze(xn()),output:r?.output??xn()})}var _e=$("ZodCustom",(r,t)=>{sb.init(r,t),E.init(r,t)});function D3(r){let t=new s({check:"custom"});return t._zod.check=r,t}function _3(r,t){return Uv(_e,r??(()=>!0),t)}function u4(r,t={}){return kv(_e,r,t)}function g4(r){return Jv(r)}function j3(r,t={error:`Input not instance of ${r.name}`}){let i=new _e({type:"custom",check:"custom",fn:(o)=>o instanceof r,abort:!0,...z.normalizeParams(t)});return i._zod.bag.Class=r,i}var O3=(...r)=>Xv({Codec:Oh,Boolean:ai,String:Oi},...r);function p3(r){let t=e4(()=>{return xh([Rv(r),pf(),If(),Jf(),ze(t),Ff(Rv(),t)])});return t}function I3(r,t){return xe(zh(r),t)}var a3={invalid_type:"invalid_type",too_big:"too_big",too_small:"too_small",invalid_format:"invalid_format",not_multiple_of:"not_multiple_of",unrecognized_keys:"unrecognized_keys",invalid_union:"invalid_union",invalid_key:"invalid_key",invalid_element:"invalid_element",invalid_value:"invalid_value",custom:"custom"};function U3(r){mr({customError:r})}function k3(){return mr().customError}var ph;(function(r){})(ph||(ph={}));var Ih={};a(Ih,{string:()=>J3,number:()=>X3,date:()=>W3,boolean:()=>P3,bigint:()=>q3});function J3(r){return ym(Oi,r)}function X3(r){return Tm(Ii,r)}function P3(r){return ov(ai,r)}function q3(r){return lv(Ui,r)}function W3(r){return fv(fe,r)}mr(oi());var b4=rr.object({type:rr.enum(["prepend","append"]),targetDomId:rr.string(),targetOid:rr.string().nullable()}),K3=b4.extend({type:rr.literal("index"),index:rr.number(),originalIndex:rr.number()}),fW=rr.discriminatedUnion("type",[K3,b4]);var YW=rr.object({suggestions:rr.array(rr.object({title:rr.string().describe("The display title of the suggestion. This will be shown to the user. Keep it concise but descriptive."),prompt:rr.string().describe("The prompt for the suggestion. This will be used to generate the suggestion. Make this as detailed and specific as possible.")})).length(3)});var FW=rr.object({filesDiscussed:rr.array(rr.string()).describe("List of file paths mentioned in the conversation"),projectContext:rr.string().describe("Summary of what the user is building and their overall goals"),implementationDetails:rr.string().describe("Summary of key code decisions, patterns, and important implementation details"),userPreferences:rr.string().describe("Specific preferences the user has expressed about implementation, design, etc."),currentStatus:rr.string().describe("Current state of the project and any pending work")});var gK={["anthropic/claude-sonnet-4"]:200000,["anthropic/claude-3.5-haiku"]:200000,["openai/gpt-5-nano"]:400000,["openai/gpt-5-mini"]:400000,["openai/gpt-5"]:400000,["claude-sonnet-4-20250514"]:200000,["claude-3-5-haiku-20241022"]:200000};function m4(){try{return window?.localStorage.getItem("theme")||"light"}catch(r){return console.warn("Failed to get theme",r),"light"}}function v4(r){try{if(r==="dark")document.documentElement.classList.add("dark"),window?.localStorage.setItem("theme","dark");else if(r==="light")document.documentElement.classList.remove("dark"),window?.localStorage.setItem("theme","light");else if(r==="system"){if(window.matchMedia("(prefers-color-scheme: dark)").matches)document.documentElement.classList.add("dark");else document.documentElement.classList.remove("dark");window?.localStorage.setItem("theme","system")}return!0}catch(t){return console.warn("Failed to set theme",t),!1}}function V3(r){return(...t)=>{try{return r(...t)}catch(i){return console.error(`Error in ${r.name}:`,i),null}}}var L3={processDom:Xi,setFrameId:Ke,getComputedStyleByDomId:I$,updateElementInstance:q$,getFirstOnlookElement:N$,captureScreenshot:Bw,buildLayerTree:zr,getElementAtLoc:P$,getElementByDomId:qi,getElementIndex:_w,setElementType:S$,getElementType:G$,getParentElement:W$,getChildrenCount:K$,getOffsetParent:V$,getActionLocation:F$,getActionElement:Wi,getInsertLocation:$w,getRemoveAction:zw,getTheme:m4,setTheme:v4,startDrag:Uw,drag:Jw,dragAbsolute:kw,endDrag:Pw,endDragAbsolute:Xw,endAllDrag:Yu,startEditingText:Ww,editText:Kw,stopEditingText:Vw,isChildTextEditable:Ew,updateStyle:mw,insertElement:xw,removeElement:fw,moveElement:Dw,groupElements:L$,ungroupElements:Y$,insertImage:vw,removeImage:hw,handleBodyReady:Eu},h4=Object.fromEntries(Object.entries(L3).map(([r,t])=>[r,V3(t)]));var jr=null,je=!1,w4=async()=>{if(je||jr)return jr;je=!0,console.log(`${Lt} - Creating penpal connection`);let r=new x$({remoteWindow:window.parent,allowedOrigins:["*"]}),t=$$({messenger:r,methods:h4});return t.promise.then((i)=>{if(!i){console.error(`${Lt} - Failed to setup penpal connection: child is null`),$4();return}jr=i,console.log(`${Lt} - Penpal connection set`)}).finally(()=>{je=!1}),t.promise.catch((i)=>{console.error(`${Lt} - Failed to setup penpal connection:`,i),$4()}),jr},$4=x4.default(()=>{if(je)return;console.log(`${Lt} - Reconnecting to penpal parent`),jr=null,w4()},1000);w4();export{jr as penpalParent};
